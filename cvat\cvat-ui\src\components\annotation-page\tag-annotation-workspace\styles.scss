// Copyright (C) 2020-2022 Intel Corporation
// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-tag-annotation-workspace.ant-layout {
    height: 100%;
}

.cvat-tag-annotation-sidebar:not(.ant-layout-sider-collapsed) {
    background: $background-color-2;
    padding: $grid-unit-size;
    padding-left: $grid-unit-size;
    overflow-y: auto;
}

.cvat-tag-annotation-sidebar-label-select {
    padding-top: $grid-unit-size;
    padding-bottom: $grid-unit-size * 2;

    > .ant-col > .ant-select {
        width: $grid-unit-size * 25;
    }
}

.cvat-tag-annotation-sidebar-shortcut-help {
    padding-top: $grid-unit-size;
    text-align: center;
}

.cvat-tag-annotation-sidebar-checkbox-skip-frame {
    padding-bottom: $grid-unit-size;
}

.cvat-tag-annotation-label-selects {
    padding-top: $grid-unit-size;

    .ant-select {
        width: $grid-unit-size * 29;
        margin: $grid-unit-size * 0.5 0;
    }

    .cvat-tag-annotation-shortcut-key {
        margin-left: $grid-unit-size;
    }
}

.cvat-tag-annotation-sidebar-empty {
    margin-top: $grid-unit-size * 4;
}

.cvat-frame-tags {
    .ant-tag {
        display: inline-flex;
        justify-content: center;
        align-items: center;

        .ant-tag-close-icon {
            margin-left: $grid-unit-size;
            font-size: 12px;
        }
    }
}

.cvat-canvas-frame-tags {
    @extend .cvat-frame-tags;

    position: absolute;
    top: $grid-unit-size * 4;
    left: $grid-unit-size;
    z-index: 3;

    .ant-tag {
        user-select: none;
    }
}

.cvat-tag-annotation-sidebar-tag-label {
    margin-top: $grid-unit-size * 2;
}

.cvat-add-tag-button {
    margin-left: $grid-unit-size;
    width: $grid-unit-size * 4;
    height: $grid-unit-size * 4;
}

.cvat-frame-tag {
    transform-origin: left;
}

.cvat-frame-tag-highlighted {
    @extend .cvat-frame-tag;

    transform: scale(1.1);
}

.cvat-canvas-annotation-frame-tags {
    margin-bottom: $grid-unit-size;
}
