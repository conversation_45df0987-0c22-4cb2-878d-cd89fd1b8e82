../../Scripts/f2py.exe,sha256=wfirlTfK-xs56SN25MAP23F4t4-g7mWSROyb2mKSx8k,106339
numpy-1.19.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-1.19.5.dist-info/LICENSE.txt,sha256=VDW_78UWZhzN_Z729vbkkr7MzLGzi7lj3OKbwZkxrP0,47238
numpy-1.19.5.dist-info/LICENSES_bundled.txt,sha256=3I3WKh8SY17BDjg5LGim_VA0zdgPDYAMsn4J-04iQrc,791
numpy-1.19.5.dist-info/METADATA,sha256=QxT8zplTKMukeyMRj0SSLM4yQJ0OH_yVZr2ijKSvbns,2050
numpy-1.19.5.dist-info/RECORD,,
numpy-1.19.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-1.19.5.dist-info/WHEEL,sha256=jr7ubY0Lkz_yXH9FfFe9PTtLhGOsf62dZkNvTYrJINE,100
numpy-1.19.5.dist-info/entry_points.txt,sha256=cOAXaHCx7qU-bvE9TStTTZW46RN-s-i6Mz9smnWkL3g,49
numpy-1.19.5.dist-info/top_level.txt,sha256=4J9lbBMLnAiyxatxh8iRKV5Entd_6-oqbO7pzJjMsPw,6
numpy/.libs/libopenblas.WCDJNK7YVMPZQ2ME2ZZHJJRJ3JIKNDB7.gfortran-win_amd64.dll,sha256=X2PVWyTId0elJvpRZPT0mzfFbTqTy0R307JAtCrPp1A,34369888
numpy/LICENSE.txt,sha256=VDW_78UWZhzN_Z729vbkkr7MzLGzi7lj3OKbwZkxrP0,47238
numpy/__config__.py,sha256=m_FyVZ1o5OtbniChpVrUUGACZXBw03jS6Er12L02mnA,2988
numpy/__init__.cython-30.pxd,sha256=VH-_4qywNddiVTdnwX6Upji1i6rc-mcXCbWNWAoA2pY,36666
numpy/__init__.pxd,sha256=qYDQe0byfzkEl2n4mqjXDznpooiR1GX9UJDhKQaaTX4,32601
numpy/__init__.py,sha256=O9RHk5sHpXvUuxULLy-wvvwRO_7Q8BzteW46BhO7v3k,12292
numpy/__pycache__/__config__.cpython-39.pyc,,
numpy/__pycache__/__init__.cpython-39.pyc,,
numpy/__pycache__/_distributor_init.cpython-39.pyc,,
numpy/__pycache__/_globals.cpython-39.pyc,,
numpy/__pycache__/_pytesttester.cpython-39.pyc,,
numpy/__pycache__/conftest.cpython-39.pyc,,
numpy/__pycache__/ctypeslib.cpython-39.pyc,,
numpy/__pycache__/dual.cpython-39.pyc,,
numpy/__pycache__/matlib.cpython-39.pyc,,
numpy/__pycache__/setup.cpython-39.pyc,,
numpy/__pycache__/version.cpython-39.pyc,,
numpy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
numpy/_globals.py,sha256=Hn6HQltNo4q5i1uia9p4o0l4H8RCAa9y9DG9rEF5J_M,2384
numpy/_pytesttester.py,sha256=RUCosg1k6UlkT_jqkq3UMsSht41kw7Z3wB6PlfbxRRo,6959
numpy/compat/__init__.py,sha256=kryOGy6TD3f9oEXy1sZZOxEMc50A7GtON1yf0nMPzr8,450
numpy/compat/__pycache__/__init__.cpython-39.pyc,,
numpy/compat/__pycache__/_inspect.cpython-39.pyc,,
numpy/compat/__pycache__/py3k.cpython-39.pyc,,
numpy/compat/__pycache__/setup.cpython-39.pyc,,
numpy/compat/_inspect.py,sha256=4PWDVD-iE3lZGrBCWdiLMn2oSytssuFszubUkC0oruA,7638
numpy/compat/py3k.py,sha256=P2MkAjs5YJM9OCN1fBllxPuzxcGm3DURKEIDkrpI9Mg,5644
numpy/compat/setup.py,sha256=PmRas58NGR72H-7OsQj6kElSUeQHjN75qVh5jlQIJmc,345
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/compat/tests/__pycache__/test_compat.cpython-39.pyc,,
numpy/compat/tests/test_compat.py,sha256=6i0bPM1Nqw0n3wtMphMU7ul7fkQNwcuH2Xxc9vpnQy8,495
numpy/conftest.py,sha256=aichnxaKFA-EVeiRNQNVnRVlvPc4g2lrEKWu4Yhgs6E,3763
numpy/core/__init__.py,sha256=gE-5KMe6aazGgpzDbBRjm8GIkpgQIAqGB5Cdueoml5M,4478
numpy/core/__pycache__/__init__.cpython-39.pyc,,
numpy/core/__pycache__/_add_newdocs.cpython-39.pyc,,
numpy/core/__pycache__/_asarray.cpython-39.pyc,,
numpy/core/__pycache__/_dtype.cpython-39.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-39.pyc,,
numpy/core/__pycache__/_exceptions.cpython-39.pyc,,
numpy/core/__pycache__/_internal.cpython-39.pyc,,
numpy/core/__pycache__/_methods.cpython-39.pyc,,
numpy/core/__pycache__/_string_helpers.cpython-39.pyc,,
numpy/core/__pycache__/_type_aliases.cpython-39.pyc,,
numpy/core/__pycache__/_ufunc_config.cpython-39.pyc,,
numpy/core/__pycache__/arrayprint.cpython-39.pyc,,
numpy/core/__pycache__/cversions.cpython-39.pyc,,
numpy/core/__pycache__/defchararray.cpython-39.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-39.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-39.pyc,,
numpy/core/__pycache__/function_base.cpython-39.pyc,,
numpy/core/__pycache__/generate_numpy_api.cpython-39.pyc,,
numpy/core/__pycache__/getlimits.cpython-39.pyc,,
numpy/core/__pycache__/machar.cpython-39.pyc,,
numpy/core/__pycache__/memmap.cpython-39.pyc,,
numpy/core/__pycache__/multiarray.cpython-39.pyc,,
numpy/core/__pycache__/numeric.cpython-39.pyc,,
numpy/core/__pycache__/numerictypes.cpython-39.pyc,,
numpy/core/__pycache__/overrides.cpython-39.pyc,,
numpy/core/__pycache__/records.cpython-39.pyc,,
numpy/core/__pycache__/setup.cpython-39.pyc,,
numpy/core/__pycache__/setup_common.cpython-39.pyc,,
numpy/core/__pycache__/shape_base.cpython-39.pyc,,
numpy/core/__pycache__/umath.cpython-39.pyc,,
numpy/core/__pycache__/umath_tests.cpython-39.pyc,,
numpy/core/_add_newdocs.py,sha256=Y6kY6JbPh76podN7aaNFDvMOtGeG7XyRaeKtcA1aeoA,209585
numpy/core/_asarray.py,sha256=FfuRpE2yY8o_APGVuPPrJHdYhX0pg1H7OMjdywn3hsg,10196
numpy/core/_dtype.py,sha256=sddrJRUYJRr5lBFkYQOi5B-JwLNBDX3HH2t0-VmQA38,10162
numpy/core/_dtype_ctypes.py,sha256=O8tYBqU1QzCG1CXviBe6jrgHYnyIPqpci9GEy9lXO08,3790
numpy/core/_exceptions.py,sha256=AKdiXex1lQsv-tdKzhBbW-m5anJJFXpsWisjz8S3tAY,6342
numpy/core/_internal.py,sha256=Rc3o6axTsBI35aWXy4F0T4AHDvzDU690ioPe6aybGco,27167
numpy/core/_methods.py,sha256=QrUoh1dojqVNY3LjFT-dgHK1x3uyvauWSniwq6xY_KI,9409
numpy/core/_multiarray_tests.cp39-win_amd64.pyd,sha256=98H-4USs1o8xDQgEq6cULiMlU12Zh2OLku5-wGbP6Gc,105984
numpy/core/_multiarray_umath.cp39-win_amd64.pyd,sha256=72mmcD-r6RhzVpq7PEUb3-y-oeAKY7aFKO-LDK49LjE,2768896
numpy/core/_operand_flag_tests.cp39-win_amd64.pyd,sha256=YD9yBHBOhWCZ-TXvr56uDIaB9XMMuio076s7mdo4kww,13824
numpy/core/_rational_tests.cp39-win_amd64.pyd,sha256=NDVIeuXQk8sIP9WjhiCIcoXc84xwdAXQrSDLixEF-YY,47616
numpy/core/_string_helpers.py,sha256=xFVFp6go9I8O7PKRR2zwkOk2VJxlnXTFSYt4s7MwXGM,2955
numpy/core/_struct_ufunc_tests.cp39-win_amd64.pyd,sha256=hrym7w-WqbARfNRLyPqCiqE3Pi8e37Jk9y7Zxhg59dI,14848
numpy/core/_type_aliases.py,sha256=hEcynWM8bX_b6Ktamqp9EWanQwI36nps_0qP8rNUn6Y,9118
numpy/core/_ufunc_config.py,sha256=K_L35alWVK-CBKiQDp2Bp2-7LgXPBNmGHVAg5gNJbCg,14271
numpy/core/_umath_tests.cp39-win_amd64.pyd,sha256=lHp0xUkwuWfFx86etiJW3EfY8HwebJYGNbWZw9EoJRM,24576
numpy/core/arrayprint.py,sha256=p1IGWeE_XsqB3R03Bx2kTH3h0LKJE2CquKvAqgeyUEk,60720
numpy/core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/core/defchararray.py,sha256=22KKqZkIUTSruv9fpFyAo8SNKquWLiSUywVpyODltTI,72836
numpy/core/einsumfunc.py,sha256=ZVSR-OFiwna8PuG8kQvRai0YPBdGXT7rhduDqkJK8-Y,52104
numpy/core/fromnumeric.py,sha256=1f_9JUttTgw40hccXhx2KwQG41R4H0sFbn3q8s2eJbM,122903
numpy/core/function_base.py,sha256=pti65ytjDaNA7WLG-66BCENWTk5ILt1PE4NP0Uj6Au0,18539
numpy/core/generate_numpy_api.py,sha256=888NFRWyHei7OhOxambeR0kjzWMAuPraLXfOgNpEOPo,7259
numpy/core/getlimits.py,sha256=rG9sl-1Atr130qfkAExh8ZRS69Q9ijP66uXPu8ah2e8,19625
numpy/core/include/numpy/__multiarray_api.h,sha256=WxWdkutB8npLHtvOsGr7q-EhYcgvZa1yASZ6n_N0zFQ,63144
numpy/core/include/numpy/__ufunc_api.h,sha256=Jmeal3EUlXxBZFX1zAUFvO7jWFVhvIOyf2DG_TW0X2k,12739
numpy/core/include/numpy/_neighborhood_iterator_imp.h,sha256=9QiCyQf-O1MEjBJQ7T4JUu_JyUyQhCBZHbwOr7sAlyk,1951
numpy/core/include/numpy/_numpyconfig.h,sha256=t8Nu7SsABLGlvsWq6fHPOrjNK07gGpTctueLWJup77g,891
numpy/core/include/numpy/arrayobject.h,sha256=Xt8fnhPhTkbhB313Xj315N3fLi3uYBEPbqNrsF-MUXE,175
numpy/core/include/numpy/arrayscalars.h,sha256=2IOwYufP2meo-i-CSp1EuZp7xvaOcAbLX9p9fbztNY0,3850
numpy/core/include/numpy/halffloat.h,sha256=AaeF7vnfAjeoIg5krxbhDn7j_T6CAQIx-HfMBYYmGiQ,1948
numpy/core/include/numpy/multiarray_api.txt,sha256=U9kT--p3paE9tIEcO8_EEukykiZR2gf-3X1AIE3JHsc,57907
numpy/core/include/numpy/ndarrayobject.h,sha256=3GqcdPD2qgESrcH1O5oFlY04HC3aZ_VdN2tuLl65BrQ,10956
numpy/core/include/numpy/ndarraytypes.h,sha256=CXaIHXNs_aHierB5g9TKuXtga2DZiRmFsgTrwUbbb1c,66416
numpy/core/include/numpy/noprefix.h,sha256=fg28OipEj4EaPsrNGWu4YNZoGK7Bxdm5o45pAO5HaSk,6998
numpy/core/include/numpy/npy_1_7_deprecated_api.h,sha256=J90pyNSaA6CJkrt4F8-U0V1_tVWq4ZWd5MBbmaCwNhI,4477
numpy/core/include/numpy/npy_3kcompat.h,sha256=K-g1EV-lqWzTCcPO1RlU8sR0x9qNguHx7vu31PT8Dus,15106
numpy/core/include/numpy/npy_common.h,sha256=1OETQY6oleu77oh0KnncqLZ-G2CtVcnE6PQ1UaQiGEc,38801
numpy/core/include/numpy/npy_cpu.h,sha256=53geT_dAYyBsf_lkAeuwF8XyDvoKVOuNkDQWvJNgE9k,4160
numpy/core/include/numpy/npy_endian.h,sha256=_vfSgtUccP2UrI6ag77Gj2mWbBnMKQfsPS2ggNwmJ2A,2714
numpy/core/include/numpy/npy_interrupt.h,sha256=CjwMAXyJjxLbpbPFpyHrTgT1QDa2mrzQ-5EhGA-feqk,1923
numpy/core/include/numpy/npy_math.h,sha256=qJ4lNGUY-o-fZRNrNWf-Dsd0u6P0A2LCgs_2sJcMWQ0,21626
numpy/core/include/numpy/npy_no_deprecated_api.h,sha256=4-ALjOYp43ACG7z4zdabmQtFsxxKDaqYuQFTbElty1o,586
numpy/core/include/numpy/npy_os.h,sha256=g6I_-QEotWP0p9g1v-GA_Qibg5HRAhQj46WsZ19fIuw,847
numpy/core/include/numpy/numpyconfig.h,sha256=_-_S3Q6rmola77NK1KzU7de-3Ta9O0cPIX_xUAw3bPo,1412
numpy/core/include/numpy/old_defines.h,sha256=gxn96tn2uCpdL0yBFEO6H_JDkhikf8Bj-w3x8sOz5Go,6493
numpy/core/include/numpy/oldnumeric.h,sha256=b2lR7L8Lajka8FQ3HrrkSnekPKYK0v4vsUduS8gWZqc,733
numpy/core/include/numpy/random/bitgen.h,sha256=V0PTeCTsLhKTq_z0hM8VgDa7jTQUTPASD0MkMaGXrgk,409
numpy/core/include/numpy/random/distributions.h,sha256=QO6dZQ44z6oFyNtS6vYEvE3wkDJ0xuv9j-ycHZeVImk,9833
numpy/core/include/numpy/ufunc_api.txt,sha256=8gADu8fBcUgFZV0yMbJwa1CtJKJsPVrx8MjuXsu18aw,7397
numpy/core/include/numpy/ufuncobject.h,sha256=P5b7oDLvsE4sYGLxQ8gNDSN-f__LR064FV4duDq97Uw,13127
numpy/core/include/numpy/utils.h,sha256=2moeSYMf5sRP51gPne9F-Nixa14F6ZjObLYCseXpJmE,750
numpy/core/lib/npy-pkg-config/mlib.ini,sha256=mQBSOI6opCVmMZK4vwIhLe5J7YevO3PbaHsI0MlJGHs,151
numpy/core/lib/npy-pkg-config/npymath.ini,sha256=5dwvhvbX3a_9toniEDvGPDGChbXIfFiLa36H4YOR-vw,380
numpy/core/lib/npymath.lib,sha256=ylxHWwBtXlsU_DLgGcTSxqDd4QY9Zp_Wr_syX14pmkg,102340
numpy/core/machar.py,sha256=Lia31J_lHCuREuCbQv8jK2ROHCCfhDa9XhQOW-EGEW4,11128
numpy/core/memmap.py,sha256=h9J1auLOKHxEgColBNSxwkzBx43_JjnV0TBKQxLw7f0,11896
numpy/core/multiarray.py,sha256=N9aC-BLmzaA_TJMt6XET1tW4rdeKjneRsUKPuQMFM7Y,56072
numpy/core/numeric.py,sha256=rtuJMigd68Ee5tLhALuiJFaOLONj7cdsE8wBMFjtEzY,77084
numpy/core/numerictypes.py,sha256=OwSX8i9qjibOqnB3Stk3t83AYu1qcIWAumlGm7axOs4,17205
numpy/core/overrides.py,sha256=z1RR4qiSeRYVd7rHygHPzPsnRfD8oWX-Fq6aVmHNI5I,7691
numpy/core/records.py,sha256=lRf1TOQADJGp7eulX7bo1KoPT_yyZnyw88DdknZLmCE,36102
numpy/core/setup.py,sha256=ukd03KWiI_-u8DdzCSu1MCPPR8wuNqGbnAsZ5_NOB3I,43390
numpy/core/setup_common.py,sha256=Ve-h7rFO27hhTnCxifVktJW_sqvWBIxSnWhy_s8xNvk,18842
numpy/core/shape_base.py,sha256=rhq0DQ3HyqKU-0AUpzMj4e1vjZECbgDKhBTl-HnO6uc,29914
numpy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/core/tests/__pycache__/_locales.cpython-39.pyc,,
numpy/core/tests/__pycache__/test__exceptions.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_abc.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_api.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_arrayprint.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_conversion_utils.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_cpu_features.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_datetime.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_defchararray.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_deprecations.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_dtype.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_einsum.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_errstate.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_extint128.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_function_base.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_getlimits.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_half.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_indexerrors.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_indexing.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_item_selection.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_longdouble.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_machar.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_mem_overlap.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_memmap.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_multiarray.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_nditer.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_numeric.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_numerictypes.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_overrides.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_print.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_protocols.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_records.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalar_ctors.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalar_methods.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarbuffer.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarinherit.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarmath.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_scalarprint.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_shape_base.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_ufunc.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_umath.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_umath_accuracy.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_umath_complex.cpython-39.pyc,,
numpy/core/tests/__pycache__/test_unicode.cpython-39.pyc,,
numpy/core/tests/_locales.py,sha256=PAV24baH5MIl_gH_VBi5glF-7kgifkK00WnAP0Hhc60,2266
numpy/core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/core/tests/data/umath-validation-set-README,sha256=hhKRS9byn4eKEJ_LHoHDYQnVArbEXPIKQtPdPBUtGbk,974
numpy/core/tests/data/umath-validation-set-cos,sha256=WO94BFik3VcliW2QBStw3Srpfd5Ykcxd-7phRf0be4Y,23898
numpy/core/tests/data/umath-validation-set-exp,sha256=mPhjF4KLe0bdwx38SJiNipD24ntLI_5aWc8h-V0UMgM,17903
numpy/core/tests/data/umath-validation-set-log,sha256=IZ4kRDJwxzbYkn4_LZOQjfwd9e0JhBpId9bO_GPVI84,4206
numpy/core/tests/data/umath-validation-set-sin,sha256=0nDRTZc1YG_zbDgpGYtMrf63nUTc8bzcCwEVWqkBqio,23705
numpy/core/tests/test__exceptions.py,sha256=_jJh6Xunn9GVkkRab3PJ5xzRn5mCdb5RHxE77F5ds5I,2063
numpy/core/tests/test_abc.py,sha256=KbyIH9nsGOnXQ8VtnwU6QyUgaFDC0XfEs476CnJ_-Wg,2382
numpy/core/tests/test_api.py,sha256=QQCMQeufRkkGeALg6fYjHB3N9UAK2GQk6uNUO2Pc4S4,21373
numpy/core/tests/test_arrayprint.py,sha256=PJxiPzarHJSoHnytdF_ghVKsIFtz4ymSxkey7euNYfk,35379
numpy/core/tests/test_conversion_utils.py,sha256=HbFAiYE4iAPhxGqeOG27EkvpyOyWWrRHrXn2sdaGcKQ,5137
numpy/core/tests/test_cpu_features.py,sha256=wcKrzblzXmZxGg8gdtw1Vn3XAG3d0Kl7TW6Lz0oyprQ,6956
numpy/core/tests/test_datetime.py,sha256=MWPU2O4n0EoiWVuNxkXeefmlqhI6fUvnVRiV3OLASdE,110569
numpy/core/tests/test_defchararray.py,sha256=GLf5I5-F3QAJIYPtx5etkRHKJF_64oF3DKAROOGRMUk,25048
numpy/core/tests/test_deprecations.py,sha256=ucGAy5ZR7DLeO1GGf0hvWnCRXo8gLkLhMjI8SIltBWs,24807
numpy/core/tests/test_dtype.py,sha256=Q9lPRfznS6q_gDSuXyVbx1rHduFWw8fcVKjDqgKoEEs,50584
numpy/core/tests/test_einsum.py,sha256=dBV4BdaEL8xKmDVuQwx0Oxl0CtarbvmtFEJdZ7l8tfA,46220
numpy/core/tests/test_errstate.py,sha256=kNCMfKs1xgXUrIRQM4qB8fEkVbREVE-p-yG2hsuHjd4,2125
numpy/core/tests/test_extint128.py,sha256=b8vP_hDPltdHoxWBn2vAmOgjJe2NVW_vjnatdCOtxu8,5862
numpy/core/tests/test_function_base.py,sha256=Fv0IC9YDjFKcs5JLlkUyvoPnbNQ-eg4Y1fFFisPNT9Q,13518
numpy/core/tests/test_getlimits.py,sha256=m2xfs_MhozzD88pqRoD00GlMbA-biaK0tEMmBW0vt9g,4418
numpy/core/tests/test_half.py,sha256=98ZvcBFSrx-2FNysp4Ucp04HTI5Z2c_IKf4cxs8KfMY,23641
numpy/core/tests/test_indexerrors.py,sha256=iJu4EorQks1MmiwTV-fda-vd4HfCEwv9_Ba_rVea7xw,5263
numpy/core/tests/test_indexing.py,sha256=iiGjKXvVPTNB23yAMBKdEru5BKPAIO8lhsI_hSHRz6A,50556
numpy/core/tests/test_item_selection.py,sha256=8AukBkttHRVkRBujEDDSW0AN1i6FvRDeJO5Cot9teFc,3665
numpy/core/tests/test_longdouble.py,sha256=0xWqtJdc-MUfxxZutfuI9W9d-hv_LouFb3355QU0UHE,13410
numpy/core/tests/test_machar.py,sha256=o4LNEPRU2p0fzok8AiO2Y9vIoZUfWQvqMN2_rJqLoKE,1096
numpy/core/tests/test_mem_overlap.py,sha256=Sc29ERD4HWExeUA43J96-4QBTkZS4dS_RUrb_DJLbwM,29781
numpy/core/tests/test_memmap.py,sha256=OOjDnRVuzoDXZisxN9GEcaHbEnig2Tz1BDI8sHkmcCI,7677
numpy/core/tests/test_multiarray.py,sha256=mLJq6vUgGUFWG_-6ZZUh8ND0eIZcC1Ke5bLfouflfC8,328337
numpy/core/tests/test_nditer.py,sha256=ESJ1wYhzM4db6D61NMRgPbgO7a37TCMXy7li-xAfiZg,115165
numpy/core/tests/test_numeric.py,sha256=cxilwORN8ix6BIpwfZyusKy0LQhqaYAOyjO38oPJCaA,127385
numpy/core/tests/test_numerictypes.py,sha256=c-xwoz-oawJff09G0tri2Q9FJQ0NLkH3vf_zmwGnheg,21400
numpy/core/tests/test_overrides.py,sha256=Ckqn-BSQ8XJFpW7JQiVOY88Cr7RRE13wMI_h2U51J4o,14860
numpy/core/tests/test_print.py,sha256=I3-R4iNbFjmVdl5xHPb9ezYb4zDfdXNfzVZt3Lz3bqU,6937
numpy/core/tests/test_protocols.py,sha256=Etu0M6T-xFJjAyXy2EcCH48Tbe5VRnZCySjMy0RhbPY,1212
numpy/core/tests/test_records.py,sha256=bTHoTLdHm4uhKFHXpyJNwvD0Y1kAK-rFTEGM9p62aT0,20198
numpy/core/tests/test_regression.py,sha256=GD2muSZ_J90Ol2U0ooBkQvxweDH9_c0TmBcIDPjx70A,91345
numpy/core/tests/test_scalar_ctors.py,sha256=xcbdNEXczuwFkhOQDHyltIjzpCMU22BHOPZ8t7ulAPc,2661
numpy/core/tests/test_scalar_methods.py,sha256=3uNPtYl73DJBLGQxJJ1df4cyr90A5Sp0pcRtXMVTjac,4166
numpy/core/tests/test_scalarbuffer.py,sha256=oBS76DABJlqx38KU2CQ0u9-B5QWwf2y5Rb9p2TpNdAA,4184
numpy/core/tests/test_scalarinherit.py,sha256=i_HSM5UmcTUMHovSSYskr8J_mBiQ5dHAMV7ENkVDSFw,2218
numpy/core/tests/test_scalarmath.py,sha256=2JqJMdh0_naVEe2dbwhduGJRvh4XCTAlNDz6cJlp1Ag,29267
numpy/core/tests/test_scalarprint.py,sha256=VmPnnhMyFScjwUjEknjt5oF1YXR43oESZSYDuqbo614,15480
numpy/core/tests/test_shape_base.py,sha256=NCUG5rfgTrefEW1Pga627jXG7EhTkh65r65o7WPIuW8,25392
numpy/core/tests/test_ufunc.py,sha256=OqHBvJNEUsJlCAED0-vt-Jnge1HdcTmdMgUjE6MltsE,86001
numpy/core/tests/test_umath.py,sha256=o6iZcGYAsDqvDchgRAL3jJ642nwnIttzqwawm7djPj0,124673
numpy/core/tests/test_umath_accuracy.py,sha256=_3N9e6662AmFLqIkyvTYFxeGL0HzM4lF4z2R00LnSoM,3103
numpy/core/tests/test_umath_complex.py,sha256=*******************************************,23755
numpy/core/tests/test_unicode.py,sha256=c6SB-PZaiNH7HvEZ2xIrfAMPmvuJmcTo79aBBkdCFvY,12915
numpy/core/umath.py,sha256=IE9whDRUf3FOx3hdo6bGB0X_4OOJn_Wk6ajnbrc542A,2076
numpy/core/umath_tests.py,sha256=IuFDModusxI6j5Qk-VWYHRZDIE806dzvju0qYlvwmfY,402
numpy/ctypeslib.py,sha256=LQDEFV54CRTKmyLV93iq1Fj4obQ_O-EXGUHtkwETNEA,17919
numpy/distutils/__config__.py,sha256=m_FyVZ1o5OtbniChpVrUUGACZXBw03jS6Er12L02mnA,2988
numpy/distutils/__init__.py,sha256=kuNMyZmAP8MtuKKOGG5pMz5wWcLVzHkU7wW7CTwzNxY,1610
numpy/distutils/__pycache__/__config__.cpython-39.pyc,,
numpy/distutils/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/__pycache__/_shell_utils.cpython-39.pyc,,
numpy/distutils/__pycache__/ccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/conv_template.cpython-39.pyc,,
numpy/distutils/__pycache__/core.cpython-39.pyc,,
numpy/distutils/__pycache__/cpuinfo.cpython-39.pyc,,
numpy/distutils/__pycache__/exec_command.cpython-39.pyc,,
numpy/distutils/__pycache__/extension.cpython-39.pyc,,
numpy/distutils/__pycache__/from_template.cpython-39.pyc,,
numpy/distutils/__pycache__/intelccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/lib2def.cpython-39.pyc,,
numpy/distutils/__pycache__/line_endings.cpython-39.pyc,,
numpy/distutils/__pycache__/log.cpython-39.pyc,,
numpy/distutils/__pycache__/mingw32ccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/misc_util.cpython-39.pyc,,
numpy/distutils/__pycache__/msvc9compiler.cpython-39.pyc,,
numpy/distutils/__pycache__/msvccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/npy_pkg_config.cpython-39.pyc,,
numpy/distutils/__pycache__/numpy_distribution.cpython-39.pyc,,
numpy/distutils/__pycache__/pathccompiler.cpython-39.pyc,,
numpy/distutils/__pycache__/setup.cpython-39.pyc,,
numpy/distutils/__pycache__/system_info.cpython-39.pyc,,
numpy/distutils/__pycache__/unixccompiler.cpython-39.pyc,,
numpy/distutils/_shell_utils.py,sha256=9pI0lXlRJxB22TPVBNUhWe7EnE-V6xIhMNQSR8LOw40,2704
numpy/distutils/ccompiler.py,sha256=v1eLW6iXNZkyIyC_E-7zf0WbfWOalJHr59igzaP7otY,28157
numpy/distutils/command/__init__.py,sha256=DCxnKqTLrauOD3Fc8b7qg9U3gV2k9SADevE_Q3H78ng,1073
numpy/distutils/command/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/command/__pycache__/autodist.cpython-39.pyc,,
numpy/distutils/command/__pycache__/bdist_rpm.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_clib.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_ext.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_py.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_scripts.cpython-39.pyc,,
numpy/distutils/command/__pycache__/build_src.cpython-39.pyc,,
numpy/distutils/command/__pycache__/config.cpython-39.pyc,,
numpy/distutils/command/__pycache__/config_compiler.cpython-39.pyc,,
numpy/distutils/command/__pycache__/develop.cpython-39.pyc,,
numpy/distutils/command/__pycache__/egg_info.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install_clib.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install_data.cpython-39.pyc,,
numpy/distutils/command/__pycache__/install_headers.cpython-39.pyc,,
numpy/distutils/command/__pycache__/sdist.cpython-39.pyc,,
numpy/distutils/command/autodist.py,sha256=i2ip0Zru8_AFx3lNQhlZfj6o_vg-RQ8yu1WNstcIYhE,3866
numpy/distutils/command/bdist_rpm.py,sha256=9uZfOzdHV0_PRUD8exNNwafc0qUqUjHuTDxQcZXLIbg,731
numpy/distutils/command/build.py,sha256=ThE0Pxz8uFq13CGR_1rt4VKdzOE_zqF0XkEQM-S84dA,1422
numpy/distutils/command/build_clib.py,sha256=WZpSbBopo31UzXckD27ZgZPlyGpEwHu-maaiBbgSU2g,14064
numpy/distutils/command/build_ext.py,sha256=IdIIQ1Z_2VeZBTpB8k1cvTaqMKAInPAZHdn1gxVlonA,26932
numpy/distutils/command/build_py.py,sha256=xBHZCtx91GqucanjIBETPeXmR-gyUKPDyr1iMx1ARWE,1175
numpy/distutils/command/build_scripts.py,sha256=AEQLNmO2v5N-GXl4lwd8v_nHlrauBx9Y-UudDcdCs_A,1714
numpy/distutils/command/build_src.py,sha256=TbN3yNLm_FafCLv2Dx0RHkvYzaPsF1UwPINe1TUepgI,31953
numpy/distutils/command/config.py,sha256=zczmFmKmYMnv1DMlJzUGtFTOascA1ZLh6Sr-EEcXSVk,21144
numpy/distutils/command/config_compiler.py,sha256=I-xAL3JxaGFfpR4lg7g0tDdA_t7zCt-D4JtOACCP_Ak,4495
numpy/distutils/command/develop.py,sha256=5ro-Sudt8l58JpKvH9FauH6vIfYRv2ohHLz-9eHytbc,590
numpy/distutils/command/egg_info.py,sha256=n6trbjRfD1qWc_hRtMFkOJsg82BCiLvdl-NeXyuceGc,946
numpy/distutils/command/install.py,sha256=s_0Uf39tFoRLUBlkrRK4YlROZsLdkI-IsuiFFaiS3ls,3157
numpy/distutils/command/install_clib.py,sha256=q3yrfJY9EBaxOIYUQoiu2-juNKLKAKKfXC0nrd4t6z0,1439
numpy/distutils/command/install_data.py,sha256=r8EVbIaXyN3aOmRugT3kp_F4Z03PsVX2l_x4RjTOWU4,872
numpy/distutils/command/install_headers.py,sha256=g5Ag2H3j3dz-qSwWegxiZSAnvAf0thYYFwfPVHf9rxc,944
numpy/distutils/command/sdist.py,sha256=XQM39b-MMO08bfE3SJrrtDWwX0XVnzCZqfAoVuuaFuE,760
numpy/distutils/conv_template.py,sha256=q60i2Jf0PlQGHmjr7pu5zwBUnoYiLhtfcdgV94uLSaw,9888
numpy/distutils/core.py,sha256=baf9SXdCVV8fA0lhLpOV4yEEWQP4ZwMTeeWoXHMg9LE,8374
numpy/distutils/cpuinfo.py,sha256=frzEtCIEsSbQzGmNUXdWctiFqRcNFeLurzbvyCh8thY,23340
numpy/distutils/exec_command.py,sha256=xMR7Dou5VZp2cP23xNd2TlXqexppXzBUd1wLMEAD2is,10668
numpy/distutils/extension.py,sha256=E6m-GBUisj8kWbZlKlQhe6UUQvZOQ6JBGS5eqaSU7lY,3463
numpy/distutils/fcompiler/__init__.py,sha256=aLe0Stc201s7R31PuLVsrfeQC-3JkFYaJYPV4cX9lLQ,41017
numpy/distutils/fcompiler/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/absoft.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/compaq.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/environment.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/g95.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/gnu.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/hpux.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/ibm.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/intel.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/lahey.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/mips.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/nag.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/none.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/nv.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/pathf95.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/pg.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/sun.cpython-39.pyc,,
numpy/distutils/fcompiler/__pycache__/vast.cpython-39.pyc,,
numpy/distutils/fcompiler/absoft.py,sha256=4UKxvpWQIphSdi6vJb3qILML_zyM3K_m7ddhAMS5dBI,5655
numpy/distutils/fcompiler/compaq.py,sha256=nBN5tnyvvVDtiybSbSlvaPwo5LNZ9uXxs72plYU09No,4031
numpy/distutils/fcompiler/environment.py,sha256=SxPO5epeUMvEpOhCO0EUY-wVk0I3111xjHjzQl7SOY0,3073
numpy/distutils/fcompiler/g95.py,sha256=1TJe4IynWYqqYBy8gJ-nz8WQ_TaSbv8k2UzUIY5Erqc,1372
numpy/distutils/fcompiler/gnu.py,sha256=HzDnrFe1ip0-aT-SMpA2xZSm6XhXTKr4fe25IFxA9Os,20946
numpy/distutils/fcompiler/hpux.py,sha256=SLbDOPYgiixqE32GgUrAJjpDLFy9g7E01vGNZCGv6Pc,1394
numpy/distutils/fcompiler/ibm.py,sha256=jL9fOpUU9g1Qx3wFqv34ow6LMq8TOSZ3EAwUb1bXs_c,3638
numpy/distutils/fcompiler/intel.py,sha256=3nBmdhH8pRMyNZfkMU0MaJGkRxVQg3wh8AOduXCjRRs,7029
numpy/distutils/fcompiler/lahey.py,sha256=EV3Zhwq-iowWAu4BFBPv_UGJ-IB-qxlxmi6WU1qHDOs,1372
numpy/distutils/fcompiler/mips.py,sha256=mlUNgGrRSLnNhtxQXWVfC9l4_OP2GMvOkgbZQwBon0A,1768
numpy/distutils/fcompiler/nag.py,sha256=3ViQnBrZBhgm-t-jvjZ_Hl_fq9s2O5FBMNwW6wOmU2Q,2624
numpy/distutils/fcompiler/none.py,sha256=auMK2ou1WtJ20LeMbwCZJ3XofpT9A0YYbMVd-62Mi_E,786
numpy/distutils/fcompiler/nv.py,sha256=ml2xco_01pGH9x23Qv-3yalFzTk-GK45BVJoDMlGod4,1627
numpy/distutils/fcompiler/pathf95.py,sha256=ipbaZIO8sqPJ1lUppOurnboiTwRzIasWNAJvKmktvv4,1094
numpy/distutils/fcompiler/pg.py,sha256=NWxCKSTxOaF4XjoXqgB27nETBakdcVouHsGrvJl1C7Y,3700
numpy/distutils/fcompiler/sun.py,sha256=JMdFfKldTYlfW1DxV7nR09k5PZypKLWpP7wmQzmlnH0,1628
numpy/distutils/fcompiler/vast.py,sha256=JUGP68JGOUOBS9WbXftE-qCVUD13fpLyPnhpHfTL5y0,1719
numpy/distutils/from_template.py,sha256=lL0BhwJHz7OrMwocJnnUElgzv8vVkZdr6NupI1ZnsLw,8224
numpy/distutils/intelccompiler.py,sha256=kVXrdCVY7YA6HdYI89De3Fy5lCjAwDpy2mrbpK7Lc10,4336
numpy/distutils/lib2def.py,sha256=HQ7i5FUtBcFGNlSlN20lgVtiBAHQbGXxmYdvkaJTjLI,3760
numpy/distutils/line_endings.py,sha256=hlI71r840mhfu8lmzdHPVZ4NFm-kJDDUMV3lETblVTY,2109
numpy/distutils/log.py,sha256=9tqE2Tq55mugFj_pn-RwV1xFoejmk0yWvVQHBL1e-Gc,2652
numpy/distutils/mingw/gfortran_vs2003_hack.c,sha256=FDTA53KYTIhil9ytvZlocOqghQVp9LacLHn1IurV0wI,83
numpy/distutils/mingw32ccompiler.py,sha256=pFGoS_FeRjWac8iN8hCbQ9kH60JsYz6ZWXhp4n5tZbc,25885
numpy/distutils/misc_util.py,sha256=-QbdJs-Xfp-56POmlg9SqdGXqJi22bGjeeJ8n2zm5vc,87812
numpy/distutils/msvc9compiler.py,sha256=bCtCVJmGrBHPm9sOoxa3oSrdrEVCNQFEM5O5hdqX8Hc,2255
numpy/distutils/msvccompiler.py,sha256=sGGkjB-iSQFEfsMfQY8ZJfPKs6vm2DY9Y_OKi0Fk__0,1986
numpy/distutils/npy_pkg_config.py,sha256=4CMG6sN7HAFSmw2ljPAAN5f04wdnZECkFXZcKoX06f0,13409
numpy/distutils/numpy_distribution.py,sha256=nrdp8rlyjEBBV1tzzi5cE-aYeXB5U3X8T5-G0akXSoY,651
numpy/distutils/pathccompiler.py,sha256=a5CYDXilCaIC85v0fVh-wrb0fClv0A7mPS87aF1inUc,734
numpy/distutils/setup.py,sha256=2chBhymbAMcc0ETIoYp_qzP6vBSJFrpbrBDm0TlU-zo,580
numpy/distutils/system_info.py,sha256=cKqMhKe5CVj88zY4KAQF0k4ao04PEmfea_VdYBTtCyo,108331
numpy/distutils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/distutils/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_exec_command.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_gnu.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_intel.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_fcompiler_nagfor.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_from_template.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_mingw32ccompiler.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_misc_util.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_npy_pkg_config.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_shell_utils.cpython-39.pyc,,
numpy/distutils/tests/__pycache__/test_system_info.cpython-39.pyc,,
numpy/distutils/tests/test_exec_command.py,sha256=Ltd4T3A_t3Oo_QSYjOkWKqPj-LttXEumEVKhLxVfZEU,7515
numpy/distutils/tests/test_fcompiler.py,sha256=SS5HOLIg0eqkmZTRKeWq9_ahW2tmV9c9piwYfzcBPmc,1320
numpy/distutils/tests/test_fcompiler_gnu.py,sha256=RlRHZbyazgKGY17NmdYSF3ehO0M0xXN4UkbsJzJz4i8,2191
numpy/distutils/tests/test_fcompiler_intel.py,sha256=4cppjLugoa8P4bjzYdiPxmyCywmP9plXOkfsklhnYsQ,1088
numpy/distutils/tests/test_fcompiler_nagfor.py,sha256=ntyr8f-67dNI0OF_l6-aeTwu9wW-vnxpheqrc4cXAUI,1124
numpy/distutils/tests/test_from_template.py,sha256=ZzUSEPyZIG4Zak3-TFqmRGXHMp58aKTuLKb0t-5XpDg,1147
numpy/distutils/tests/test_mingw32ccompiler.py,sha256=7X8V4hLMtsNj1pYoLkSSla04gJu66e87E_k-6ce3PrA,1651
numpy/distutils/tests/test_misc_util.py,sha256=YKK2WrJqVJ5o71mWL5oP0l-EVQmqKlf3XU8y7co0KYc,3300
numpy/distutils/tests/test_npy_pkg_config.py,sha256=1pQh-mApHjj0y9Ba2tqns79U8dsfDpJ9zcPdsa2qbps,2641
numpy/distutils/tests/test_shell_utils.py,sha256=okNSfjFSAvY3XyBsyZrKXAtV9RBmb7vX9o4ZLJc28Ds,2030
numpy/distutils/tests/test_system_info.py,sha256=KcsvlajSjkqvtEDgEq_MsGVGF2cclzcXfkRQenbVFyg,9991
numpy/distutils/unixccompiler.py,sha256=6p2pR21e47KTAMvQRaDQEeb4wStDNSyO5rJOQARXZJE,5137
numpy/doc/__init__.py,sha256=llSbqjSXybPuXqt6WJFZhgYnscgYl4m1tUBy_LhfCE0,534
numpy/doc/__pycache__/__init__.cpython-39.pyc,,
numpy/doc/__pycache__/basics.cpython-39.pyc,,
numpy/doc/__pycache__/broadcasting.cpython-39.pyc,,
numpy/doc/__pycache__/byteswapping.cpython-39.pyc,,
numpy/doc/__pycache__/constants.cpython-39.pyc,,
numpy/doc/__pycache__/creation.cpython-39.pyc,,
numpy/doc/__pycache__/dispatch.cpython-39.pyc,,
numpy/doc/__pycache__/glossary.cpython-39.pyc,,
numpy/doc/__pycache__/indexing.cpython-39.pyc,,
numpy/doc/__pycache__/internals.cpython-39.pyc,,
numpy/doc/__pycache__/misc.cpython-39.pyc,,
numpy/doc/__pycache__/structured_arrays.cpython-39.pyc,,
numpy/doc/__pycache__/subclassing.cpython-39.pyc,,
numpy/doc/__pycache__/ufuncs.cpython-39.pyc,,
numpy/doc/basics.py,sha256=RK_lYLxlQL-yXouYnt6osJqhhul7srGR9Sfjz0e8XqU,11528
numpy/doc/broadcasting.py,sha256=ZFIrPh2daTyc-P5ifM6VCgwDrlZ5VoPjJqilPGaq5kw,5710
numpy/doc/byteswapping.py,sha256=b64P25IpKME9NIQ03iFdBLxepP3GkaOCDqka_0SOqtM,5465
numpy/doc/constants.py,sha256=3vbu3Nxy8_5JWc6k8-BIY16Q2Lb3CtG0_MuYluXs9W8,9648
numpy/doc/creation.py,sha256=L2DulN_X_xBZd9vRZR2bPpFUZPLLDnlEcwYs09ba5dE,5574
numpy/doc/dispatch.py,sha256=weKPXdjp9VJverQ2tFGIRTDXCxyoud8LO6kje3lHqKI,10284
numpy/doc/glossary.py,sha256=7LHhzSyIvz-H78BrlbOot0lHYcBR20F4px6PSBkJpJU,15172
numpy/doc/indexing.py,sha256=b1GwJEVkWK5vbiaRZY-PiECKsMZrYE61QYgRtVBnmrk,16698
numpy/doc/internals.py,sha256=8dbWVbCl5xnOjeXeuIzupmoWvqeDhnlA9HPDj6EG4F4,9766
numpy/doc/misc.py,sha256=rVDxNfefLqFbddFF_PJgo10-_Nu9tUFSzgGkjgnft_A,6352
numpy/doc/structured_arrays.py,sha256=ybJN81Apnxu7xvwg80L0AneJclKWFuMYDj2e9dKUBEU,27094
numpy/doc/subclassing.py,sha256=lHgTW_BXUUeZKzJWgu_UyYngiZuSf0_YfUCZDz_euXo,29294
numpy/doc/ufuncs.py,sha256=jL-idm49Qd8xNns12ZPp533jorDuDnUN2I96hbmFZoo,5497
numpy/dual.py,sha256=710T-mMGyFb22oATrtkiJozhhrbdAyBoBux4ADMbkZ4,1880
numpy/f2py/__init__.py,sha256=2XyTg-Di000huAXaibNB6AnXhQart1umjscgy0aW8WI,3154
numpy/f2py/__main__.py,sha256=XSvcMI54qWQiicJ51nRxK8L8PGzuUy3c2NGTVg1tzyk,89
numpy/f2py/__pycache__/__init__.cpython-39.pyc,,
numpy/f2py/__pycache__/__main__.cpython-39.pyc,,
numpy/f2py/__pycache__/__version__.cpython-39.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-39.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-39.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-39.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-39.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-39.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-39.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-39.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-39.pyc,,
numpy/f2py/__pycache__/f2py_testing.cpython-39.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-39.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-39.pyc,,
numpy/f2py/__pycache__/rules.cpython-39.pyc,,
numpy/f2py/__pycache__/setup.cpython-39.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-39.pyc,,
numpy/f2py/__version__.py,sha256=N_m8jjlfteJqqS8jfRZQaJMGbiuulC4RyDX1BCqmDy8,196
numpy/f2py/auxfuncs.py,sha256=UJE-OcD3gSFXVWaN-_IQ2zdlMFSmWWpbXwdY9vsjbIs,22605
numpy/f2py/capi_maps.py,sha256=QmqD140JWMl62KQb3XxDht51F14nwWx3NaWNp4jXPQk,32297
numpy/f2py/cb_rules.py,sha256=XNkMLl_EcSaPDy-NfPHMdjuDdFRKCr-91MOx1aNjhXY,23419
numpy/f2py/cfuncs.py,sha256=R8KBoIHrQkBsDf0-haA5rjB21dhPzSbkpsjiK-NpSXQ,46497
numpy/f2py/common_rules.py,sha256=W7rKmlCBJgcgs9U14Yyz79k4U5VgNxgOQV-WN4VrAQ0,5114
numpy/f2py/crackfortran.py,sha256=PalQFk702YDQZ8rjryIPjMBlqkXrrLxSgRx8NcIFcwQ,132330
numpy/f2py/diagnose.py,sha256=LmGu6iZKr9WHfMZFIIEobZvRJd9Ktdqx0nYekWk4N7g,5384
numpy/f2py/f2py2e.py,sha256=9-fjOOzNI9n_-RxVWhNdjfwgGAwK0O0prApThbYqKKE,25044
numpy/f2py/f2py_testing.py,sha256=vybOK-G_b0KYFNNZdhOCUf5pZExEdWfcIueIKODu024,1503
numpy/f2py/f90mod_rules.py,sha256=umkXYf5Y6TrJ77DIZX0L0xemyFQ2sfe7M63f71A94hs,10017
numpy/f2py/func2subr.py,sha256=LDVKbtAeejaCEuR4uQLp83XG0FZymf7xvis21afW74s,9456
numpy/f2py/rules.py,sha256=kkM1clDIWBti1zz4FurwFHdzEKSuZBI2p33AftV1Z-g,59757
numpy/f2py/setup.py,sha256=-vMbuIcSpp7g7K8KHDURjyBho8EALeKIGwWEbDA_poQ,2497
numpy/f2py/src/fortranobject.c,sha256=xaUYHdjvYIhQhnfSU3jf0HBGmwB-lavgbQA4OvMFUoE,35754
numpy/f2py/src/fortranobject.h,sha256=KFnSJVT4aGQb4akNw0PXVaq5CjMDT7baGV_DASli62E,4530
numpy/f2py/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/f2py/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_compile_function.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-39.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-39.pyc,,
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=MieDOlG9d5iTanWmgKRI9kSydMckFKoY1g9FVWI3ODo,7697
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/test_array_from_pyobj.py,sha256=3rASd81apCvTWbWR0yA6OVW7ZXjK1Qi7l2vDFb5OUC4,22556
numpy/f2py/tests/test_assumed_shape.py,sha256=TDLfEzJc7tlfiqFzmonD8LO85PXySgJ4JE_5IZTzinA,1637
numpy/f2py/tests/test_block_docstring.py,sha256=pC00xi0ycn1Kpbo_F92yM2QzFMnrPAsZ7-qpZeQNyCs,644
numpy/f2py/tests/test_callback.py,sha256=LEHeQaKPf1E5_qdF0zAWE5L2hMbEv6FvYve46hc7WRw,4150
numpy/f2py/tests/test_common.py,sha256=tRwTdz6aQ0RYREFC-T-SfEyq25wWYo5pknVAqvvvBjM,827
numpy/f2py/tests/test_compile_function.py,sha256=d2zOi1oPKwqSMBILFSUuJeIX3BY6uAOBZnvYw5h3K54,4434
numpy/f2py/tests/test_crackfortran.py,sha256=EzSfkWzFM7Kf6fNXZYuplp83KtRegEbP0umoHaPOmQY,2885
numpy/f2py/tests/test_kind.py,sha256=eH_sM5X5wIwe3T9yVMH6DH8I4spsgRaeIork_FJG-8Q,1044
numpy/f2py/tests/test_mixed.py,sha256=faYM1laPKwStbz-RY-6b9LCuj2kFojTPIR0wxsosXoI,946
numpy/f2py/tests/test_parameter.py,sha256=S1K9K9Uj5dWjmWF8euBUMJdGilIqn1nNH2FftcS1INU,4026
numpy/f2py/tests/test_quoted_character.py,sha256=81CR1ZIyKygz7noFJMcgaLdg18nY8zcFfrSN2L_U9xg,959
numpy/f2py/tests/test_regression.py,sha256=DLPJGoDz78obYsXxx3ZXnS5YkkAG9ybCiyzGtt-92dI,725
numpy/f2py/tests/test_return_character.py,sha256=bpuHEjBj53YpvsI_aLQOcSVdoGAEtgtmAK8kbHKdcgc,4064
numpy/f2py/tests/test_return_complex.py,sha256=jYcoM3pZj0opGXXF69EsGQ6HD5SPaRhBeGx3RhnJx8c,4778
numpy/f2py/tests/test_return_integer.py,sha256=MpdNZLxeQwER4M4w5utHYe08gDzrjAWOq_z5wRO8hP8,4751
numpy/f2py/tests/test_return_logical.py,sha256=uP9rWNyA3UARKwaQ6Fsc8grh72G5xnp9XXFSWLdPZQ8,5028
numpy/f2py/tests/test_return_real.py,sha256=05OwUsgmI_G_pr197xUXgIgTNVOHygtY2kPqRiexeSY,5605
numpy/f2py/tests/test_semicolon_split.py,sha256=Ap6S5tGL6R8I2i9iUlVxElW4IMNoz0LxIHK1hLIYZhQ,1577
numpy/f2py/tests/test_size.py,sha256=ukbP0NlzqpfD6M_K58WK6IERSXc_6nHpyUqbYfEbk8M,1335
numpy/f2py/tests/test_string.py,sha256=w2GiZmdwNAaWOBhak9IuUnnVktMhUq_Y0ajI0SX9YNY,632
numpy/f2py/tests/util.py,sha256=rXqfB6384TeBl0KLNnUc80tnn1Hp1E2LXUVb-_qH4_Y,9974
numpy/f2py/use_rules.py,sha256=ROzvjl0-GUOkT3kJS5KkYq8PsFxGjebA6uPq9-CyZEQ,3700
numpy/fft/__init__.py,sha256=iRvj7AexTBPHlGP1wXYdTQJU9n0aD4OvJaefStBSVNg,7720
numpy/fft/__pycache__/__init__.cpython-39.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-39.pyc,,
numpy/fft/__pycache__/helper.cpython-39.pyc,,
numpy/fft/__pycache__/setup.cpython-39.pyc,,
numpy/fft/_pocketfft.py,sha256=J4BSwSlZXAJKdCl8FraMeemhoBJFOjLuDro60beA5tI,49029
numpy/fft/_pocketfft_internal.cp39-win_amd64.pyd,sha256=LL5yYUeqLRkBGPCpe2ub7Y8qeSAXnfeY5RB6-PaoCj8,112640
numpy/fft/helper.py,sha256=yhM2U72L2BT4oIGAtHaEcSeasYTUsLh_vv0GA-NUFcg,6427
numpy/fft/setup.py,sha256=GDFeFVDjNw4u-WoVGirHU2t3eswgYsSpJNguhbRzv3Q,714
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-39.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-39.pyc,,
numpy/fft/tests/test_helper.py,sha256=OMUhUqJacTOD1CWIFZ3w4HOWHOQZxdLGnWqOt3mnACM,6383
numpy/fft/tests/test_pocketfft.py,sha256=LZCkx-aIrDqGjxK8CJIl7VGT5-EDQs0kAS7895a1374,9878
numpy/lib/__init__.py,sha256=2Arwvv9UMENY_nqvqVxuGrrEM2nwP3tP9HLMOuBb8dE,1896
numpy/lib/__pycache__/__init__.cpython-39.pyc,,
numpy/lib/__pycache__/_datasource.cpython-39.pyc,,
numpy/lib/__pycache__/_iotools.cpython-39.pyc,,
numpy/lib/__pycache__/_version.cpython-39.pyc,,
numpy/lib/__pycache__/arraypad.cpython-39.pyc,,
numpy/lib/__pycache__/arraysetops.cpython-39.pyc,,
numpy/lib/__pycache__/arrayterator.cpython-39.pyc,,
numpy/lib/__pycache__/financial.cpython-39.pyc,,
numpy/lib/__pycache__/format.cpython-39.pyc,,
numpy/lib/__pycache__/function_base.cpython-39.pyc,,
numpy/lib/__pycache__/histograms.cpython-39.pyc,,
numpy/lib/__pycache__/index_tricks.cpython-39.pyc,,
numpy/lib/__pycache__/mixins.cpython-39.pyc,,
numpy/lib/__pycache__/nanfunctions.cpython-39.pyc,,
numpy/lib/__pycache__/npyio.cpython-39.pyc,,
numpy/lib/__pycache__/polynomial.cpython-39.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-39.pyc,,
numpy/lib/__pycache__/scimath.cpython-39.pyc,,
numpy/lib/__pycache__/setup.cpython-39.pyc,,
numpy/lib/__pycache__/shape_base.cpython-39.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-39.pyc,,
numpy/lib/__pycache__/twodim_base.cpython-39.pyc,,
numpy/lib/__pycache__/type_check.cpython-39.pyc,,
numpy/lib/__pycache__/ufunclike.cpython-39.pyc,,
numpy/lib/__pycache__/user_array.cpython-39.pyc,,
numpy/lib/__pycache__/utils.cpython-39.pyc,,
numpy/lib/_datasource.py,sha256=rnLi18WlhoE-9NppspLiIMHH9-RjqXdWTvWnon9xfhk,23498
numpy/lib/_iotools.py,sha256=EaK3oBNDchHXyVndxxfkk5XTSpCIXs1KqlRc2_-daaQ,31807
numpy/lib/_version.py,sha256=syCq5PJX3QCmhjIR4nO1HG2YWPTarArAR-7m8dv93gk,5010
numpy/lib/arraypad.py,sha256=Iw9RVTXfPxAj9_Qr7kalJzBAdl8OshwvA8Oc4aHLjuw,32200
numpy/lib/arraysetops.py,sha256=w6b5zdv7cnd5GaEocAVKTwuz7jgpKD8nQ2PMBtti9zE,25702
numpy/lib/arrayterator.py,sha256=29pO5S0ciEZwt1402Q0-5cRbyKspV4tlPX1-m_D_Hgc,7282
numpy/lib/financial.py,sha256=4jSobkUZyuZpXbLKYu0qpSWMCxhFovO_RmGEHV0HNjA,32490
numpy/lib/format.py,sha256=RBTmNV-XYeSWKMhl_UZhgvdzZkWghhsAImyRcOOWR5Y,31958
numpy/lib/function_base.py,sha256=Q_IOu9VaR3w_aZ8GOmFTmTGXCwoCWT0cuL4nzOf5vjo,160085
numpy/lib/histograms.py,sha256=sJNjLvJLQCmPTIlwMsrtOmFK2SxxyQn6-9gmBBsQa14,41261
numpy/lib/index_tricks.py,sha256=RrqkEi_5UxD8B_bcGJD0d7CVguCncEfgHhKioptpVT8,30622
numpy/lib/mixins.py,sha256=BFWJiMFWohFHtTQH27hg5dpUqWj31VM2rMOPfUik1VY,7216
numpy/lib/nanfunctions.py,sha256=tnDGArTWr5jojoFCOEAvfY9qfi7SIdMUsaobb4IZKEo,60559
numpy/lib/npyio.py,sha256=k_zYKifTQsEPujEi6aJaGlXGTziHQGhYF_WaNrPbh2k,89911
numpy/lib/polynomial.py,sha256=_srQxiw9BiaWJy-5Cj__o7vnd75lltPiSW5BZeY9M-0,42058
numpy/lib/recfunctions.py,sha256=zFN5AwXUbMsR9Srva-2eFOTjyrVJkMHy3f_mJTGe2R8,58119
numpy/lib/scimath.py,sha256=XQNQVHKZTK__ED9_meUQmRx7m_vzNbPvgugNo0Jii68,15324
numpy/lib/setup.py,sha256=svb3fz5-yXB2M9YEnKZD2LgVgAxrQeHLXGMXVvRmE8E,381
numpy/lib/shape_base.py,sha256=Mf0115HklQKM0IqRR57hC7PjPoQIKPLN-Uc5NheJJLw,39624
numpy/lib/stride_tricks.py,sha256=3oMkGhDksyM4n-AC6e39JxB-oHET_IJAshIo70yv9zA,9289
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_financial.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-39.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-39.pyc,,
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=pTTVh8ezp-lwAK3fkgvdKU8Arp5NMKznVD-M6Ex_uA0,341
numpy/lib/tests/data/py3-objarr.npz,sha256=qQR0gS57e9ta16d_vCQjaaKM74gPdlwCPkp55P-qrdw,449
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=OYm6qsPy7Q8yXV_5GfSMMNQKon4izf_T9ULaqjHLJA4,10837
numpy/lib/tests/test__iotools.py,sha256=q44VFSi9VzWaf_dJ-MGBtYA7z7TFR0j0AF-rbzhLXoo,14096
numpy/lib/tests/test__version.py,sha256=WwCOmobk2UN6Hlp93bXsd7RR1UmjJlE4r9ol8d87ctg,2053
numpy/lib/tests/test_arraypad.py,sha256=ZoqM25xb5iI_K6ampX5cov2zFUwQZ8i0Xu3gY_ncdk0,55647
numpy/lib/tests/test_arraysetops.py,sha256=QHZSJOGcpV42HakwDUSYjVQu6JpZgOpkTuT50ywBGKs,24981
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_financial.py,sha256=E-3H8s40B5LCYcgdp4GZp-bBzm-_mY1lMcaSLIz-mc0,18696
numpy/lib/tests/test_format.py,sha256=f7lykKehFvu_kIcAJ52C4EdUjWctQb6iiaRNhpVDFq4,39526
numpy/lib/tests/test_function_base.py,sha256=97YJdiYpF8Cs2SkaL1BYWghctCeHDRvJ-ntK8lapJt4,129544
numpy/lib/tests/test_histograms.py,sha256=_uKM8dtMWCwGtIgIwog3s3jyPb8w804TWqy9iOkYeSI,34510
numpy/lib/tests/test_index_tricks.py,sha256=vrTcZnpAus3-9l3TtWcNTTKRPBI9Fx74xDcBO3w4QMg,18822
numpy/lib/tests/test_io.py,sha256=WqGKE1WAcEpb2OL4Z6Q9HgAsR-b-ruYjer0nBDR_zbg,103502
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=KJbcTS5ylvlSoRBBE4Qqk60pYlRGYEoJ1hSB_ydZTbk,39065
numpy/lib/tests/test_packbits.py,sha256=XpFIaL8mOWfzD3WQaxd6WrDFWh4Mc51EPXIOqxt3wS0,17922
numpy/lib/tests/test_polynomial.py,sha256=cCNRkN_BR03F0RWZ0XFcXhjCDdaJxaWLZc6qgD-2z_Y,10272
numpy/lib/tests/test_recfunctions.py,sha256=f9J49n_8ot0zG0Bw4XXFx3l1XN2VeAu9ROgn0dV4GLY,42134
numpy/lib/tests/test_regression.py,sha256=NXKpFka0DPE7z0-DID-FGh0ITFXi8nEj6Pg_-uBcDIg,8504
numpy/lib/tests/test_shape_base.py,sha256=kC4k86CdiwUoqBs_KjAc_JekFJLVtlbanlPrXXXpWRU,25020
numpy/lib/tests/test_stride_tricks.py,sha256=Iy5tIhmM7-zIQHMfLacDnG9tvZ0l5IXy5uSCVpsGgV4,17433
numpy/lib/tests/test_twodim_base.py,sha256=USISKs6mDYgEl20k4_k899_pWSkFjZkPkafA_BUQxng,18890
numpy/lib/tests/test_type_check.py,sha256=ffuA-ndaMsUb0IvPalBMwGkoukIP9OZVJXCuq299qB8,15597
numpy/lib/tests/test_ufunclike.py,sha256=rpZTDbKPBVg5-byY1SKhkukyxB2Lvonw15ZARPE3mc0,3382
numpy/lib/tests/test_utils.py,sha256=dG7eDTkCWTUQtTGGKK1R3acRtGuPeAAqHvw9SEQfj-g,3911
numpy/lib/twodim_base.py,sha256=exuILTWzUB-o3xX_3TefU7A8qUqcCtkwXZS2RRE9YHs,28572
numpy/lib/type_check.py,sha256=WK5a3RgaIVoKqskfvG-U6VHiZyMNrfswV-9SBb5RxbU,20500
numpy/lib/ufunclike.py,sha256=D6NgciDjA0ETqfzX9lDzuArgPOWccl3-nws48_IMwYo,8211
numpy/lib/user_array.py,sha256=5yqkyjCmUIASGNx2bt7_ZMWJQJszkbD1Kn06qqv7POA,8007
numpy/lib/utils.py,sha256=6MwIC6RCKLnM7SQMVNC03-TxgmQcXjTqFrh1DiNCxS4,33643
numpy/linalg/__init__.py,sha256=nziJvIBC-t0x_9SjOrlgcorUR03gdviaCWhbvvPIT7s,1836
numpy/linalg/__pycache__/__init__.cpython-39.pyc,,
numpy/linalg/__pycache__/linalg.cpython-39.pyc,,
numpy/linalg/__pycache__/setup.cpython-39.pyc,,
numpy/linalg/_umath_linalg.cp39-win_amd64.pyd,sha256=KNgzjQTbA9GtFxLxTXdK-D4nMPv0tCIsgNhdixd6sQA,153600
numpy/linalg/lapack_lite.cp39-win_amd64.pyd,sha256=APSTSTZ5-6EAK_INIHk1PUDN3vG-7E4DsXqqRyOeyMA,22016
numpy/linalg/linalg.py,sha256=yYpGO5bVH-YAIiv8DAqsURla4dqieDvaAZpDcpxWhNM,92341
numpy/linalg/setup.py,sha256=-a8_WfO7pY1QGFkUj5kW5svaTWC8VdnOgi-8EFB0Vhw,2867
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_build.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-39.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/linalg/tests/test_build.py,sha256=o1l1ojThBlvC-qbshrYF7dx7WEnhb2G2z_ij8wGCGUc,1675
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=Etz-gCqEwABmLdA3ffVEUIz2JFaGCBLL_AylmA-uFAQ,76456
numpy/linalg/tests/test_regression.py,sha256=T0iQkRUhOoxIHHro5kyxU7GFRhN3pZov6UaJGXxtvu0,5745
numpy/ma/__init__.py,sha256=9i-au2uOZ_K9q2t9Ezc9nEAS74Y4TXQZMoP9601UitU,1458
numpy/ma/__pycache__/__init__.cpython-39.pyc,,
numpy/ma/__pycache__/bench.cpython-39.pyc,,
numpy/ma/__pycache__/core.cpython-39.pyc,,
numpy/ma/__pycache__/extras.cpython-39.pyc,,
numpy/ma/__pycache__/mrecords.cpython-39.pyc,,
numpy/ma/__pycache__/setup.cpython-39.pyc,,
numpy/ma/__pycache__/testutils.cpython-39.pyc,,
numpy/ma/__pycache__/timer_comparison.cpython-39.pyc,,
numpy/ma/bench.py,sha256=rh3pV-_6827crLjXHvdaazROh0w0K7Ix9De0eYm67O4,5024
numpy/ma/core.py,sha256=hkViazvE3HMaqFf5X_wT1dz0sedx7vggl-la5aUDZbU,272211
numpy/ma/extras.py,sha256=qeH3Cny4g_OD2I_ujBZkzO7FO7Nsbe6wIuvAhZAE64U,60506
numpy/ma/mrecords.py,sha256=yjug9UKe08ahEA24ahyghGs59JExnNlNu5FGgdfYNuI,27457
numpy/ma/setup.py,sha256=tgbpGMWeWAYKxpVx7XLeB5SuZtR6wqVPppBzSadXNw8,394
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-39.pyc,,
numpy/ma/tests/test_core.py,sha256=y3ULcaJjf_yhsTg_NyKGWj1GBXmBIzjTEw8XOPH6Txw,204449
numpy/ma/tests/test_deprecations.py,sha256=Dv-fqcxKJ_tfxyk-DyK2rA5onOl-7YC06Pu9nkvECt0,2326
numpy/ma/tests/test_extras.py,sha256=_uTlvEtwEcLyYw3dS657rlP3nUz2p697aspFW9kze48,68630
numpy/ma/tests/test_mrecords.py,sha256=KeUfhLLwjwhX-Wh1UvViiFdOO3JFj44BQDtiC4ZZUrE,20363
numpy/ma/tests/test_old_ma.py,sha256=_B_ysTXBqbxueyUBJPjwZeGsA-XaX6pU8mwCEjXrmro,33141
numpy/ma/tests/test_regression.py,sha256=Hi-p5QdcivsxUDSpTl3MGtAnmJCJPhhIj3c5nYI9rw8,3170
numpy/ma/tests/test_subclassing.py,sha256=z8LhDLqKN5ybMJ4FkFBgpFwy0OatsJ_yyCU1t5oU4Lg,13210
numpy/ma/testutils.py,sha256=Y7Ie5QSy8OL9AJFC0w1fwdzkcJ5ATPwDX1r7vxW8r2Q,10587
numpy/ma/timer_comparison.py,sha256=y5GWEwwbcWzO0I-TJGuOnW4brcEng8Y-gOwU5cut8-w,15911
numpy/matlib.py,sha256=l-292Lvk_yUCK5Y_U9u1Xa8grW8Ss0uh23o8kj-Hhd8,10741
numpy/matrixlib/__init__.py,sha256=OYwN1yrpX0doHcXpzdRm2moAUO8BCmgufnmd6DS43pI,228
numpy/matrixlib/__pycache__/__init__.cpython-39.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-39.pyc,,
numpy/matrixlib/__pycache__/setup.cpython-39.pyc,,
numpy/matrixlib/defmatrix.py,sha256=BE_0FL7Ie8rJETxL1Y0l1hx6ksYDwCwznR460GqPMG8,31784
numpy/matrixlib/setup.py,sha256=HJpLLVYLjzOIZI1FYyA3sFpwhLN-ErouMRObooQKhxw,402
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-39.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=nbY_HkwzoJbhYhACiEN-cZmR644sVJvMKWUcsANPayQ,15435
numpy/matrixlib/tests/test_interaction.py,sha256=C1YtIubO6Qh8RR-XONzo8Mle4bu4SvwsvBnB0x0Gy4g,12229
numpy/matrixlib/tests/test_masked_matrix.py,sha256=OyFkczRm-f51EQpP7TLlohU0bVKky0TwWtRXVJNfp_w,9064
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=FgYV3hwkpO0qyshDzG7n1JfQ-kKwnSZnA68jJHS7TeM,958
numpy/polynomial/__init__.py,sha256=cUci1M9bDomn34_LiOALHXH8lqEByVxAWguPSnk2QAI,1093
numpy/polynomial/__pycache__/__init__.cpython-39.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-39.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-39.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-39.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-39.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-39.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-39.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-39.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-39.pyc,,
numpy/polynomial/__pycache__/setup.cpython-39.pyc,,
numpy/polynomial/_polybase.py,sha256=jI7psP2tZbCM6L3T0MPlAmRGqRyXm9DV-HqBy8JZfig,33696
numpy/polynomial/chebyshev.py,sha256=75P-NmV2bxIfilKRfwWkODJVs1_b728W8I-Z7kR7O-0,63842
numpy/polynomial/hermite.py,sha256=DdTYeQf8tWBNPzTNrQ9xOcbEvfMz1SUDwXDx-lFaEew,53202
numpy/polynomial/hermite_e.py,sha256=nb-YuodJCOkQeQ-4AZY6LVMWhcVwb30xXmKvu7W3Qm0,53333
numpy/polynomial/laguerre.py,sha256=Tq1lylYS4cnHDRlUqldhBmoieEZttmXpLrSzUe7HLcQ,51522
numpy/polynomial/legendre.py,sha256=zbd2yX-WctRalgsV5_5ayMbuiPZw9rMVbmx7LerE37E,52264
numpy/polynomial/polynomial.py,sha256=JnYJzwsFhImXElFde5YbU6pnkXLBs1o5JFd3m7SvEaQ,49216
numpy/polynomial/polyutils.py,sha256=O8_quxmbGWKnp8q3TjeJ84AdKCo5nxObrH_Ssqzw_1U,23936
numpy/polynomial/setup.py,sha256=VNnC5cPwh8LAejwtCEWrcFFDA8mrTu9jzZfobmDUhx4,347
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-39.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-39.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=J6abNzhFKwaagzv_x53xN0whCfr5nMwqZ---Jrd9oxY,18931
numpy/polynomial/tests/test_hermite.py,sha256=zGYN24ia2xx4IH16D6sfAxIipnZrGrIe7D8QMJZPw4Y,19132
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=hBgo8w_3iEQosX2CqjTkUstTiuTPLZmfQNQtyKudZLo,18048
numpy/polynomial/tests/test_legendre.py,sha256=mJcXkot3E2uhcIZY-Bvb3EfWbOo601NG_gq0OwObuNk,18831
numpy/polynomial/tests/test_polynomial.py,sha256=BHR8Cy7nhcxdsgrhEwyPRdswgQhDRZmnaoT9gb5O1VU,20628
numpy/polynomial/tests/test_polyutils.py,sha256=F9Tghiw2LM1wqEHFHxCuFAR9rueLe-i-dakoEvsLgJE,3105
numpy/polynomial/tests/test_printing.py,sha256=5BSqFlP3B00Q7hSxQOSTtk_VvKpueFoW2Q4MRho5ACM,4049
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=rYnpCdx2jY6FC1tn4ev_5SekdgjT_xgl5X0dinpzLj4,7673
numpy/random/__pycache__/__init__.cpython-39.pyc,,
numpy/random/__pycache__/_pickle.cpython-39.pyc,,
numpy/random/__pycache__/setup.cpython-39.pyc,,
numpy/random/_bounded_integers.cp39-win_amd64.pyd,sha256=wba-gO6DP4Vpmh-zVNVXuLvPLrGsz9S6brvG561-4jw,239104
numpy/random/_bounded_integers.pxd,sha256=ugYlh8FvGggHCjEaqgO4S_MeRcZg3mw40sDYEqx07QQ,1698
numpy/random/_common.cp39-win_amd64.pyd,sha256=AyYlUyYhBZgpdkjiTaVKeRdyJXMl5QJExHQMCuwiRcE,180224
numpy/random/_common.pxd,sha256=N2NZYlMYNh7FzFbJ6Mr2DH3MkrG67HUwqOu-XX_ouAA,4855
numpy/random/_examples/cffi/__pycache__/extending.cpython-39.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-39.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=BX3tRknP-4v8PVNf-wFBx289DisbOvVZdbAXvAZWfAk,1561
numpy/random/_examples/cython/__pycache__/setup.cpython-39.pyc,,
numpy/random/_examples/cython/extending.pyx,sha256=_pKBslBsb8rGeFZkuQeAIhBdeIjDcX3roGqV_Jev7NE,2371
numpy/random/_examples/cython/extending_distributions.pyx,sha256=1zrMvPbKi0RinyZ93Syyy4OXGEOzAAKHSzTmDtN09ZY,3987
numpy/random/_examples/cython/setup.py,sha256=dvXjPDXSiWZwgq_Myapi2VggTzV0AGdSrEhrMmJmT0s,1366
numpy/random/_examples/numba/__pycache__/extending.cpython-39.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-39.pyc,,
numpy/random/_examples/numba/extending.py,sha256=xfAEeiSfWP_WIT7Va0duyX9zSqHeAtNRLy2Ksxol7xM,2061
numpy/random/_examples/numba/extending_distributions.py,sha256=tU62JEW13VyNuBPhSpDWqd9W9ammHJCLv61apg90lMc,2101
numpy/random/_generator.cp39-win_amd64.pyd,sha256=Ngo75d479Q6JkIKtHEqgOjUumkx9ZyCh20CTuLMlNIY,646144
numpy/random/_mt19937.cp39-win_amd64.pyd,sha256=t9RM_YMKp7aKvok1Z5MaaudoCYoMxDYficY3hODSGg0,78336
numpy/random/_pcg64.cp39-win_amd64.pyd,sha256=5aSSF7EE9J2YJZvhuLTrFOWMDG62psb8UomCzHs7bYg,65024
numpy/random/_philox.cp39-win_amd64.pyd,sha256=GCUgWZklb2EBMGH7u9IHIVXvgKYx4cDTNBM534ocWHk,72192
numpy/random/_pickle.py,sha256=X1IKY4xyLBsWLG1vubCWyRRn-QsV-jm5McUH-Zc-uiU,2329
numpy/random/_sfc64.cp39-win_amd64.pyd,sha256=7mR-uYY7K9ic6IAmtGJh9xQZ_5JbpmNaK_luWhMuwmg,52736
numpy/random/bit_generator.cp39-win_amd64.pyd,sha256=Jm4V9rqToH-Ulw2EtSzLShd5NlV8VBiDq98-01j69bk,151552
numpy/random/bit_generator.pxd,sha256=wvVMUsJ4CEi-X097EKdMiDaGdEbIF4OhpQvEVS_MQ1s,1040
numpy/random/c_distributions.pxd,sha256=N8O_29MDGDOyMqmSKoaoooo4OJRRerBwsBXLw7_4j54,6147
numpy/random/lib/npyrandom.lib,sha256=VhYL-5GagZgzZE-Ktq8X4sMAAV-05wlQ3fFZbGw3ikM,110146
numpy/random/mtrand.cp39-win_amd64.pyd,sha256=odFdOvIImPjwI-VXkNWHGwgyx6zKlgMEvtlgkkbbg3o,562176
numpy/random/setup.py,sha256=x1CA0fgu4ooolY9bPUEJKEWLyx-YgFEXp-Wum5Ke8EU,6222
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-39.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-39.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-39.pyc,,
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/test_direct.py,sha256=lorvgd9iyAHZtp_W2uVOQec46LL4JlwzBwPjZ7KBPPo,14843
numpy/random/tests/test_extending.py,sha256=ijY7x3180lgbUD8dwavR1SE56tnLq-j0TdhBantoHmA,3600
numpy/random/tests/test_generator_mt19937.py,sha256=PftMeh_JZgWsE0Gj2Y0mciJwAn015sSg-Ee8lxiIT2I,105221
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=qzfS5z9cY86X92qcoU3EX8-mVCmCz9gVwZ9cBtI0cLw,5803
numpy/random/tests/test_random.py,sha256=Lln-BoQ_l5j0MRz2-fkQeGRLNXP2G-gspSJrYqPr7gk,69058
numpy/random/tests/test_randomstate.py,sha256=3RsaYTBvwDDDvFcHQz3gvcKvLfV-J4sJQc_4rdz8wM4,81766
numpy/random/tests/test_randomstate_regression.py,sha256=1QBMG9KrWDBp5l2upMpaAOqEi8WHw3rSFI78eXITo8o,7758
numpy/random/tests/test_regression.py,sha256=7Aktlse9beP4mO41__ZlfgqTviwAGVCTQYfUPxMH1pI,5602
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=SMmdUvuBUXcrhkKlM-BXXHBCLMu2xTS8NqRxPryE_Qw,28678
numpy/setup.py,sha256=b8ZRQ41mwuzHT-e-z2VkOgGaJArDfixl5WPbVag0puI,900
numpy/testing/__init__.py,sha256=s9TGtO8tH0kG8emfVLC1pBRUrgsb4ueWAwtIb6XQXLg,586
numpy/testing/__pycache__/__init__.cpython-39.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-39.pyc,,
numpy/testing/__pycache__/setup.cpython-39.pyc,,
numpy/testing/__pycache__/utils.cpython-39.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-39.pyc,,
numpy/testing/_private/__pycache__/decorators.cpython-39.pyc,,
numpy/testing/_private/__pycache__/noseclasses.cpython-39.pyc,,
numpy/testing/_private/__pycache__/nosetester.cpython-39.pyc,,
numpy/testing/_private/__pycache__/parameterized.cpython-39.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-39.pyc,,
numpy/testing/_private/decorators.py,sha256=SlPl0mwKPXVpywpDJg__c6koz5BO3ihm6230ov-y7HU,9011
numpy/testing/_private/noseclasses.py,sha256=23nLzkcEy0a9GMSyy8HVl78k1GkfMMO3o1lKUOCAAzs,14892
numpy/testing/_private/nosetester.py,sha256=-cgYtMtxlN60eRQ83mHiYcA5E-bgubda74vBisSytMw,20015
numpy/testing/_private/parameterized.py,sha256=VP7nfeYtdrgJxIPWu9BQK5K3kH3gICZgEFokB2TVd0Y,16963
numpy/testing/_private/utils.py,sha256=wdOWK9Nk5djpy002m37un4rHzOnPuujlicZElZ_81DE,87553
numpy/testing/print_coercion_tables.py,sha256=wmvxAPbqLjgrMuCce6G_eX8tMI1LhsoCghnUTJIRiR0,2827
numpy/testing/setup.py,sha256=ltZquj0L8WaIXBisJvjHZ4oRJMCUitEBMkTf06e5OWQ,649
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/testing/tests/__pycache__/test_decorators.cpython-39.pyc,,
numpy/testing/tests/__pycache__/test_doctesting.cpython-39.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-39.pyc,,
numpy/testing/tests/test_decorators.py,sha256=zR2-CPT4vK_KE1st9LuaAAkP1PFthUkBCefjng088uI,6045
numpy/testing/tests/test_doctesting.py,sha256=wUauOPx75yuJgIHNWlPCpF0EUIGKDI-nzlImCwGeYo0,1404
numpy/testing/tests/test_utils.py,sha256=VzN7apbJyKFULS3T1R6k2jx35TA9lR-Pxd6EqNVfm0Y,57196
numpy/testing/utils.py,sha256=LhIaw_3hG1jUW98rUrBhOxRi1SjOUgVIr5--kwp0XZc,1260
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-39.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-39.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-39.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-39.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-39.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-39.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-39.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-39.pyc,,
numpy/tests/test_ctypeslib.py,sha256=4hdwdxDArcTLHpinigBSLLGb_ppGtksPWRsvkRD0Z84,12535
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_version.py,sha256=EzH8x1xpoowIlHlXQ-TEQaIJlmnx_rgrJtOCwdBXYyY,598
numpy/tests/test_public_api.py,sha256=BDFFwck0Zcgp5MZkVTJJvum1DtS4V_lCZRRuUUF9R2I,15976
numpy/tests/test_reloading.py,sha256=WuaGiMPNSmoS2_cKKcsLFNI2iSIvxxR0O0f1Id7qW40,2007
numpy/tests/test_scripts.py,sha256=jZHCW1jQrXwXycEUp4ntzQzm_DDscJXDsQL_moonq98,1567
numpy/tests/test_warnings.py,sha256=IMFVROBQqYZPibnHmwepGqEUQoBlDtdC8DlRulbMAd8,2354
numpy/version.py,sha256=pKESFuVGqZebdmgudT8UFyW68yYg5V84gAH8rTscW4M,306
