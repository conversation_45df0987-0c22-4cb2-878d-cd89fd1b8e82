# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2014
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2020-03-04 04:05+0000\n"
"Last-Translator: wolf ice <<EMAIL>>\n"
"Language-Team: Chinese (China) (http://www.transifex.com/django/django/"
"language/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Advanced options"
msgstr "高级选项"

msgid "Flat Pages"
msgstr "简单页面"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr "例如：“/about/contact/”。确保有开头和结尾的斜杠。"

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr "该值必须只能包含字母，数字，点号，下划线，破折号，和"

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "例如：“/about/contact”。确保有一个开头的斜杠。"

msgid "URL is missing a leading slash."
msgstr "URL头部缺失斜线."

msgid "URL is missing a trailing slash."
msgstr "URL尾部缺失斜线."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "带有url %(url)s的Flatpage已经存在于站点 %(site)s"

msgid "title"
msgstr "标题"

msgid "content"
msgstr "内容"

msgid "enable comments"
msgstr "允许评论"

msgid "template name"
msgstr "模板名称"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"例如：“flatpages/contact_page.html”。如果它未被提供，系统将使用“flatpages/"
"default.html”。"

msgid "registration required"
msgstr "请先注册"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr "如果被选中，仅登录用户才可以查看此页。"

msgid "sites"
msgstr "站点"

msgid "flat page"
msgstr "简单页面"

msgid "flat pages"
msgstr "简单页面"
