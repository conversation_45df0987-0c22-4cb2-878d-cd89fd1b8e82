metadata:
  name: openvino-omz-public-faster-rcnn-inception-resnet-v2-atrous-coco
  namespace: cvat
  annotations:
    name: Faster RCNN
    type: detector
    spec: |
      [
        { "id": 1, "name": "person", "type": "rectangle" },
        { "id": 2, "name": "bicycle", "type": "rectangle" },
        { "id": 3, "name": "car", "type": "rectangle" },
        { "id": 4, "name": "motorcycle", "type": "rectangle" },
        { "id": 5, "name": "airplane", "type": "rectangle" },
        { "id": 6, "name": "bus", "type": "rectangle" },
        { "id": 7, "name": "train", "type": "rectangle" },
        { "id": 8, "name": "truck", "type": "rectangle" },
        { "id": 9, "name": "boat", "type": "rectangle" },
        { "id":10, "name": "traffic_light", "type": "rectangle" },
        { "id":11, "name": "fire_hydrant", "type": "rectangle" },
        { "id":13, "name": "stop_sign", "type": "rectangle" },
        { "id":14, "name": "parking_meter", "type": "rectangle" },
        { "id":15, "name": "bench", "type": "rectangle" },
        { "id":16, "name": "bird", "type": "rectangle" },
        { "id":17, "name": "cat", "type": "rectangle" },
        { "id":18, "name": "dog", "type": "rectangle" },
        { "id":19, "name": "horse", "type": "rectangle" },
        { "id":20, "name": "sheep", "type": "rectangle" },
        { "id":21, "name": "cow", "type": "rectangle" },
        { "id":22, "name": "elephant", "type": "rectangle" },
        { "id":23, "name": "bear", "type": "rectangle" },
        { "id":24, "name": "zebra", "type": "rectangle" },
        { "id":25, "name": "giraffe", "type": "rectangle" },
        { "id":27, "name": "backpack", "type": "rectangle" },
        { "id":28, "name": "umbrella", "type": "rectangle" },
        { "id":31, "name": "handbag", "type": "rectangle" },
        { "id":32, "name": "tie", "type": "rectangle" },
        { "id":33, "name": "suitcase", "type": "rectangle" },
        { "id":34, "name": "frisbee", "type": "rectangle" },
        { "id":35, "name": "skis", "type": "rectangle" },
        { "id":36, "name": "snowboard", "type": "rectangle" },
        { "id":37, "name": "sports_ball", "type": "rectangle" },
        { "id":38, "name": "kite", "type": "rectangle" },
        { "id":39, "name": "baseball_bat", "type": "rectangle" },
        { "id":40, "name": "baseball_glove", "type": "rectangle" },
        { "id":41, "name": "skateboard", "type": "rectangle" },
        { "id":42, "name": "surfboard", "type": "rectangle" },
        { "id":43, "name": "tennis_racket", "type": "rectangle" },
        { "id":44, "name": "bottle", "type": "rectangle" },
        { "id":46, "name": "wine_glass", "type": "rectangle" },
        { "id":47, "name": "cup", "type": "rectangle" },
        { "id":48, "name": "fork", "type": "rectangle" },
        { "id":49, "name": "knife", "type": "rectangle" },
        { "id":50, "name": "spoon", "type": "rectangle" },
        { "id":51, "name": "bowl", "type": "rectangle" },
        { "id":52, "name": "banana", "type": "rectangle" },
        { "id":53, "name": "apple", "type": "rectangle" },
        { "id":54, "name": "sandwich", "type": "rectangle" },
        { "id":55, "name": "orange", "type": "rectangle" },
        { "id":56, "name": "broccoli", "type": "rectangle" },
        { "id":57, "name": "carrot", "type": "rectangle" },
        { "id":58, "name": "hot_dog", "type": "rectangle" },
        { "id":59, "name": "pizza", "type": "rectangle" },
        { "id":60, "name": "donut", "type": "rectangle" },
        { "id":61, "name": "cake", "type": "rectangle" },
        { "id":62, "name": "chair", "type": "rectangle" },
        { "id":63, "name": "couch", "type": "rectangle" },
        { "id":64, "name": "potted_plant", "type": "rectangle" },
        { "id":65, "name": "bed", "type": "rectangle" },
        { "id":67, "name": "dining_table", "type": "rectangle" },
        { "id":70, "name": "toilet", "type": "rectangle" },
        { "id":72, "name": "tv", "type": "rectangle" },
        { "id":73, "name": "laptop", "type": "rectangle" },
        { "id":74, "name": "mouse", "type": "rectangle" },
        { "id":75, "name": "remote", "type": "rectangle" },
        { "id":76, "name": "keyboard", "type": "rectangle" },
        { "id":77, "name": "cell_phone", "type": "rectangle" },
        { "id":78, "name": "microwave", "type": "rectangle" },
        { "id":79, "name": "oven", "type": "rectangle" },
        { "id":80, "name": "toaster", "type": "rectangle" },
        { "id":81, "name": "sink", "type": "rectangle" },
        { "id":83, "name": "refrigerator", "type": "rectangle" },
        { "id":84, "name": "book", "type": "rectangle" },
        { "id":85, "name": "clock", "type": "rectangle" },
        { "id":86, "name": "vase", "type": "rectangle" },
        { "id":87, "name": "scissors", "type": "rectangle" },
        { "id":88, "name": "teddy_bear", "type": "rectangle" },
        { "id":89, "name": "hair_drier", "type": "rectangle" },
        { "id":90, "name": "toothbrush", "type": "rectangle" }
      ]

spec:
  description: Faster R-CNN with Inception ResNet v2 Atrous via Intel OpenVINO toolkit
  runtime: 'python:3.8'
  handler: main:handler
  eventTimeout: 30s

  build:
    image: cvat.openvino.omz.public.faster_rcnn_inception_resnet_v2_atrous_coco
    baseImage: cvat.openvino.omz.public.faster_rcnn_inception_resnet_v2_atrous_coco.base

  triggers:
    myHttpTrigger:
      numWorkers: 2
      kind: 'http'
      workerAvailabilityTimeoutMilliseconds: 10000
      attributes:
        maxRequestBodySize: 33554432 # 32MB

  platform:
    attributes:
      restartPolicy:
        name: always
        maximumRetryCount: 3
      mountMode: volume
