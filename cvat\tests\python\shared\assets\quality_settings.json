{"count": 26, "next": null, "previous": null, "results": [{"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 1, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 2}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 2, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 5}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 3, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 6}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 4, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 7}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 5, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 8}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 6, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 9}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 7, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 11}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 8, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 12}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 9, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 13}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 10, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 14}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 11, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 15}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 12, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 17}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 13, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 18}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 14, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 19}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 15, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 20}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 16, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 21}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 17, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 22}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 18, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 23}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 19, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 24}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 20, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 25}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 21, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 26}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 22, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 27}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 23, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 28}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 24, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 29}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 25, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 30}, {"check_covered_annotations": true, "compare_attributes": true, "compare_groups": true, "compare_line_orientation": true, "empty_is_annotated": false, "group_match_threshold": 0.5, "id": 26, "iou_threshold": 0.4, "line_orientation_threshold": 0.1, "line_thickness": 0.01, "low_overlap_threshold": 0.8, "max_validations_per_job": 0, "object_visibility_threshold": 0.05, "oks_sigma": 0.09, "panoptic_comparison": true, "point_size_base": "group_bbox_size", "target_metric": "accuracy", "target_metric_threshold": 0.7, "task_id": 31}]}