{{ $cover := and
    (.HasShortcode "blocks/cover")
    (not .Site.Params.ui.navbar_translucent_over_cover_disable)
-}}
{{ $baseURL := urls.Parse $.Site.Params.Baseurl -}}

<nav class="td-navbar navbar-dark js-navbar-scroll
            {{- if $cover }} td-navbar-cover {{- end }}">
<div class="container-fluid flex-column flex-md-row">
  <a class="navbar-brand" href="{{ relref . "/about" }}">
    {{- /**/ -}}
    <span class="navbar-brand__logo navbar-logo">
      {{- if ne .Site.Params.ui.navbar_logo false -}}
        {{ with resources.Get "icons/logo.svg" -}}
          {{ ( . | minify).Content | safeHTML -}}
        {{ end -}}
      {{ end -}}
    </span>
    {{- /**/ -}}
  </a>
  <div class="td-navbar-nav-scroll ms-md-auto" style="height: auto;" id="main_navbar">
    <ul class="navbar-nav d-flex flex-wrap" style="padding-bottom: 0;">
      {{ $p := . -}}
      {{ range .Site.Menus.main -}}
      <li class="nav-item">
        {{ $active := or ($p.IsMenuCurrent "main" .) ($p.HasMenuCurrent "main" .) -}}
        {{ $href := "" -}}
        {{ with .Page -}}
          {{ $active = or $active ( $.IsDescendant .) -}}
          {{ $href = .RelPermalink -}}
        {{ else -}}
          {{ $href = .URL | relLangURL -}}
        {{ end -}}
        {{ $isExternal := ne $baseURL.Host (urls.Parse .URL).Host -}}
        <a {{/**/ -}}
          class="nav-link {{- if $active }} active {{- end }}" {{/**/ -}}
          href="{{ $href }}"
          {{- if $isExternal }} rel="noopener" {{- end -}}
        >
            {{- .Pre -}}
            <span>{{ .Name }}</span>
            {{- .Post -}}
        </a>
      </li>
      {{ end -}}
      {{ if .Site.Params.versions -}}
      <li class="nav-item dropdown d-none d-lg-block">
        {{ partial "navbar-version-selector.html" . -}}
      </li>
      {{ end -}}
      {{ if (gt (len .Site.Home.Translations) 0) -}}
      <li class="nav-item dropdown d-none d-lg-block">
        {{ partial "navbar-lang-selector.html" . -}}
      </li>
      {{ end -}}
    </ul>
  </div>
  <div class="d-none d-lg-block">
    {{ partial "search-input.html" . }}
  </div>
</div>
</nav>