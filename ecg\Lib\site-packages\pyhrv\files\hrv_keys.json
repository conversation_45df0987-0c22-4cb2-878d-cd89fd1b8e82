{"nn_intervals": ["nn_data", "NN INTERVALS", "ms", 1, "pyhrv.time_domain.nn_intervals"], "nni_counter": ["time", "Number of NNIs", "-", 2, "pyhrv.time_domain.nni_parameters"], "nni_mean": ["time", "Mean NNI", "ms", 3, "pyhrv.time_domain.nni_parameters"], "nni_min": ["time", "Minimum NNI", "ms", 4, "pyhrv.time_domain.nni_parameters"], "nni_max": ["time", "Maximum NNI", "ms", 5, "pyhrv.time_domain.nni_parameters"], "nni_diff_mean": ["time", "Mean Delta-NNI", "ms", 6, "pyhrv.time_domain.nni_differences_parameters"], "nni_diff_min": ["time", "Minimum Delta-NNI", "ms", 7, "pyhrv.time_domain.nni_differences_parameters"], "nni_diff_max": ["time", "Maximum Delta-NNI", "ms", 8, "pyhrv.time_domain.nni_differences_parameters"], "hr_mean": ["time", "Mean Heart Rate", "bpm", 9, "pyhrv.time_domain.hr_parameters"], "hr_min": ["time", "Minimum Heart Rate", "bpm", 10, "pyhrv.time_domain.hr_parameters"], "hr_max": ["time", "Maximum Heart Rate", "bpm", 11, "pyhrv.time_domain.hr_parameters"], "hr_std": ["time", "Standard Deviation of Heart Rate", "bpm", 12, "pyhrv.time_domain.hr_parameters"], "sdnn": ["time", "SDNN", "ms", 13, "pyhrv.time_domain.sdnn"], "sdnn_index": ["time", "SDNN Index", "ms", 14, "pyhrv.time_domain.sdnn_index"], "sdann": ["time", "SDANN", "ms", 15, "pyhrv.time_domain.sdann"], "sdsd": ["time", "SDSD", "ms", 16, "pyhrv.time_domain.sdsd"], "rmssd": ["time", "RMSSD", "ms", 17, "pyhrv.time_domain.rmssd"], "nn50": ["time", "NN50", "-", 18, "pyhrv.time_domain.nn50"], "pnn50": ["time", "pNN50", "%", 19, "pyhrv.time_domain.nn50"], "nn20": ["time", "NN20", "-", 20, "pyhrv.time_domain.nn20"], "pnn20": ["time", "pNN20", "%", 21, "pyhrv.time_domain.nn20"], "tri_index": ["time", "Triangular Index", "-", 22, "pyhrv.time_domain.triangular_index"], "tinn": ["time", "TINN", "ms", 23, "pyhrv.time_domain.tinn"], "tinn_n": ["time", "TINN N", "ms", 24, "pyhrv.time_domain.tinn"], "tinn_m": ["time", "TINN M", "ms", 25, "pyhrv.time_domain.tinn"], "fft_bands": ["frequency_fft", "Frequency Bands", "Hz", 26, "pyhrv.frequency_domain.welch_psd"], "fft_peak": ["frequency_fft", "Peak Frequencies", "Hz", 27, "pyhrv.frequency_domain.welch_psd"], "fft_abs": ["frequency_fft", "Absolute Powers", "Hz", 28, "pyhrv.frequency_domain.welch_psd"], "fft_log": ["frequency_fft", "Logarithmic Powers", "Hz", 29, "pyhrv.frequency_domain.welch_psd"], "fft_rel": ["frequency_fft", "Relative Powers", "%", 30, "pyhrv.frequency_domain.welch_psd"], "fft_norm": ["frequency_fft", "Normalized Powers", "-", 31, "pyhrv.frequency_domain.welch_psd"], "fft_total": ["frequency_fft", "Total Power", "ms^2", 32, "pyhrv.frequency_domain.welch_psd"], "fft_ratio": ["frequency_fft", "LF/HF Ratio", "-", 33, "pyhrv.frequency_domain.welch_psd"], "fft_nfft": ["frequency_fft", "Number of PSD Samples", "-", 34, "pyhrv.frequency_domain.welch_psd"], "fft_window": ["frequency_fft", "Window Function", "-", 35, "pyhrv.frequency_domain.welch_psd"], "fft_resampling_frequency": ["frequency_fft", "Resampling Frequency", "Hz", 36, "pyhrv.frequency_domain.welch_psd"], "fft_interpolation": ["frequency_fft", "Interpolation Method", "-", 37, "pyhrv.frequency_domain.welch_psd"], "ar_bands": ["frequency_ar", "Frequency Bands", "Hz", 38, "pyhrv.frequency_domain.ar_psd"], "ar_peak": ["frequency_ar", "Peak Frequencies", "Hz", 39, "pyhrv.frequency_domain.ar_psd"], "ar_abs": ["frequency_ar", "Absolute Powers", "ms^2", 40, "pyhrv.frequency_domain.ar_psd"], "ar_log": ["frequency_ar", "Logarithmic Powers", "Hz", 41, "pyhrv.frequency_domain.ar_psd"], "ar_rel": ["frequency_ar", "Relative Powers", "%", 42, "pyhrv.frequency_domain.ar_psd"], "ar_norm": ["frequency_ar", "Normalized Powers", "-", 43, "pyhrv.frequency_domain.ar_psd"], "ar_total": ["frequency_ar", "Total Power", "ms^2", 44, "pyhrv.frequency_domain.ar_psd"], "ar_ratio": ["frequency_ar", "LF/HF Ratio", "-", 45, "pyhrv.frequency_domain.ar_psd"], "ar_nfft": ["frequency_ar", "Number of PSD Samples", "-", 46, "pyhrv.frequency_domain.ar_psd"], "ar_order": ["frequency_ar", "AR Order", "-", 47, "pyhrv.frequency_domain.ar_psd"], "lomb_bands": ["frequency_lomb", "Frequency Bands", "Hz", 48, "pyhrv.frequency_domain.lomb_psd"], "lomb_peak": ["frequency_lomb", "Peak frequencies", "Hz", 49, "pyhrv.frequency_domain.lomb_psd"], "lomb_abs": ["frequency_lomb", "Absolute powers", "Hz", 50, "pyhrv.frequency_domain.lomb_psd"], "lomb_log": ["frequency_lomb", "Logarithmic powers", "Hz", 51, "pyhrv.frequency_domain.lomb_psd"], "lomb_rel": ["frequency_lomb", "Relative powers", "%", 52, "pyhrv.frequency_domain.lomb_psd"], "lomb_norm": ["frequency_lomb", "Normalized powers", "-", 53, "pyhrv.frequency_domain.lomb_psd"], "lomb_total": ["frequency_lomb", "Total power", "ms^2", 54, "pyhrv.frequency_domain.lomb_psd"], "lomb_ratio": ["frequency_lomb", "LF/HF ratio", "-", 55, "pyhrv.frequency_domain.lomb_psd"], "lomb_nfft": ["frequency_lomb", "Number of PSD Samples", "-", 56, "pyhrv.frequency_domain.lomb_psd"], "lomb_ma": ["frequency_lomb", "Moving Average Window Size", "-", 57, "pyhrv.frequency_domain.lomb_psd"], "sd1": ["nonlinear", "SD1", "ms", 58, "pyhrv.nonlinear.poincare"], "sd2": ["nonlinear", "SD2", "ms", 59, "pyhrv.nonlinear.poincare"], "sd_ratio": ["nonlinear", "SD2/SD1", "-", 60, "pyhrv.nonlinear.poincare"], "ellipse_area": ["nonlinear", "Ellipse Area S", "ms^2", 61, "pyhrv.nonlinear.poincare"], "sampen": ["nonlinear", "Sample Entropy", "-", 62, "pyhrv.nonlinear.sample_entropy"], "dfa_alpha1": ["nonlinear", "DFA alpha 1 (long term fluctuation)", "-", 63, "pyhrv.nonlinear.dfa"], "dfa_alpha1_beats": ["nonlinear", "DFA alpha 1 beats range", "-", 64, "pyhrv.nonlinear.dfa"], "dfa_alpha2": ["nonlinear", "DFA alpha 2 (long term fluctuation)", "-", 65, "pyhrv.nonlinear.dfa"], "dfa_alpha2_beats": ["nonlinear", "DFA alpha 2 beats range", "-", 66, "pyhrv.nonlinear.dfa"], "ecg_plot": ["plot", null, null, 67, "pyhrv.tools.plot_ecg"], "tachogram_plot": ["plot", null, null, 68, "pyhrv.tools.tachogram"], "poincare_plot": ["plot", null, null, 69, "pyhrv.nonlinear.poincare"], "fft_plot": ["plot", null, null, 70, "pyhrv.frequency_domain.welch_psd"], "ar_plot": ["plot", null, null, 71, "pyhrv.frequency_domain.ar_psd"], "lomb_plot": ["plot", null, null, 72, "pyhrv.frequency_domain.lomb_psd"], "nni_histogram": ["plot", null, null, 73, "pyhrv.time_domain.geometrical_parameters"], "tinn_histogram": ["plot", null, null, 74, "pyhrv.time_domain.tinn"], "tri_histogram": ["plot", null, null, 75, "pyhrv.time_domain.triangular_index"], "comment": ["metadata", null, null, 76, ""]}