# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <PERSON> <fried<PERSON>@translate.org.za>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2019-01-04 18:49+0000\n"
"Last-Translator: <PERSON> <PERSON> <<EMAIL>>\n"
"Language-Team: Afrikaans (http://www.transifex.com/django/django/language/"
"af/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: af\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Inhoudtipes"

msgid "python model class name"
msgstr "python-modelklasnaam"

msgid "content type"
msgstr "inhoudtipe"

msgid "content types"
msgstr "inhoudtipes"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Inhoudtipe %(ct_id)s-objek het geen geassosieerde model nie"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Inhoudtipe %(ct_id)s-objek %(obj_id)s bestaan nie"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s-objekte het nie 'n get_absolute_url()-metode nie"
