// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/xla/xla.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/xla/service/hlo.pb.h"
#include "tensorflow/compiler/xla/xla_data.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fxla_2fxla_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[40]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
namespace xla {
class CompileRequest;
class CompileRequestDefaultTypeInternal;
extern CompileRequestDefaultTypeInternal _CompileRequest_default_instance_;
class CompileResponse;
class CompileResponseDefaultTypeInternal;
extern CompileResponseDefaultTypeInternal _CompileResponse_default_instance_;
class ComputationGraphStatsRequest;
class ComputationGraphStatsRequestDefaultTypeInternal;
extern ComputationGraphStatsRequestDefaultTypeInternal _ComputationGraphStatsRequest_default_instance_;
class ComputationStatsResponse;
class ComputationStatsResponseDefaultTypeInternal;
extern ComputationStatsResponseDefaultTypeInternal _ComputationStatsResponse_default_instance_;
class ComputeConstantGraphRequest;
class ComputeConstantGraphRequestDefaultTypeInternal;
extern ComputeConstantGraphRequestDefaultTypeInternal _ComputeConstantGraphRequest_default_instance_;
class ComputeConstantResponse;
class ComputeConstantResponseDefaultTypeInternal;
extern ComputeConstantResponseDefaultTypeInternal _ComputeConstantResponse_default_instance_;
class CreateChannelHandleRequest;
class CreateChannelHandleRequestDefaultTypeInternal;
extern CreateChannelHandleRequestDefaultTypeInternal _CreateChannelHandleRequest_default_instance_;
class CreateChannelHandleResponse;
class CreateChannelHandleResponseDefaultTypeInternal;
extern CreateChannelHandleResponseDefaultTypeInternal _CreateChannelHandleResponse_default_instance_;
class DebugOptions;
class DebugOptionsDefaultTypeInternal;
extern DebugOptionsDefaultTypeInternal _DebugOptions_default_instance_;
class DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse;
class DebugOptions_XlaBackendExtraOptionsEntry_DoNotUseDefaultTypeInternal;
extern DebugOptions_XlaBackendExtraOptionsEntry_DoNotUseDefaultTypeInternal _DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse_default_instance_;
class DeconstructTupleRequest;
class DeconstructTupleRequestDefaultTypeInternal;
extern DeconstructTupleRequestDefaultTypeInternal _DeconstructTupleRequest_default_instance_;
class DeconstructTupleResponse;
class DeconstructTupleResponseDefaultTypeInternal;
extern DeconstructTupleResponseDefaultTypeInternal _DeconstructTupleResponse_default_instance_;
class ExecuteGraphParallelRequest;
class ExecuteGraphParallelRequestDefaultTypeInternal;
extern ExecuteGraphParallelRequestDefaultTypeInternal _ExecuteGraphParallelRequest_default_instance_;
class ExecuteGraphRequest;
class ExecuteGraphRequestDefaultTypeInternal;
extern ExecuteGraphRequestDefaultTypeInternal _ExecuteGraphRequest_default_instance_;
class ExecuteParallelResponse;
class ExecuteParallelResponseDefaultTypeInternal;
extern ExecuteParallelResponseDefaultTypeInternal _ExecuteParallelResponse_default_instance_;
class ExecuteRequest;
class ExecuteRequestDefaultTypeInternal;
extern ExecuteRequestDefaultTypeInternal _ExecuteRequest_default_instance_;
class ExecuteResponse;
class ExecuteResponseDefaultTypeInternal;
extern ExecuteResponseDefaultTypeInternal _ExecuteResponse_default_instance_;
class ExecutionOptions;
class ExecutionOptionsDefaultTypeInternal;
extern ExecutionOptionsDefaultTypeInternal _ExecutionOptions_default_instance_;
class GetDeviceHandlesRequest;
class GetDeviceHandlesRequestDefaultTypeInternal;
extern GetDeviceHandlesRequestDefaultTypeInternal _GetDeviceHandlesRequest_default_instance_;
class GetDeviceHandlesResponse;
class GetDeviceHandlesResponseDefaultTypeInternal;
extern GetDeviceHandlesResponseDefaultTypeInternal _GetDeviceHandlesResponse_default_instance_;
class GetShapeRequest;
class GetShapeRequestDefaultTypeInternal;
extern GetShapeRequestDefaultTypeInternal _GetShapeRequest_default_instance_;
class GetShapeResponse;
class GetShapeResponseDefaultTypeInternal;
extern GetShapeResponseDefaultTypeInternal _GetShapeResponse_default_instance_;
class LoadDataRequest;
class LoadDataRequestDefaultTypeInternal;
extern LoadDataRequestDefaultTypeInternal _LoadDataRequest_default_instance_;
class LoadDataResponse;
class LoadDataResponseDefaultTypeInternal;
extern LoadDataResponseDefaultTypeInternal _LoadDataResponse_default_instance_;
class ResetDeviceRequest;
class ResetDeviceRequestDefaultTypeInternal;
extern ResetDeviceRequestDefaultTypeInternal _ResetDeviceRequest_default_instance_;
class ResetDeviceResponse;
class ResetDeviceResponseDefaultTypeInternal;
extern ResetDeviceResponseDefaultTypeInternal _ResetDeviceResponse_default_instance_;
class TransferFromOutfeedRequest;
class TransferFromOutfeedRequestDefaultTypeInternal;
extern TransferFromOutfeedRequestDefaultTypeInternal _TransferFromOutfeedRequest_default_instance_;
class TransferFromOutfeedResponse;
class TransferFromOutfeedResponseDefaultTypeInternal;
extern TransferFromOutfeedResponseDefaultTypeInternal _TransferFromOutfeedResponse_default_instance_;
class TransferToClientRequest;
class TransferToClientRequestDefaultTypeInternal;
extern TransferToClientRequestDefaultTypeInternal _TransferToClientRequest_default_instance_;
class TransferToClientResponse;
class TransferToClientResponseDefaultTypeInternal;
extern TransferToClientResponseDefaultTypeInternal _TransferToClientResponse_default_instance_;
class TransferToInfeedRequest;
class TransferToInfeedRequestDefaultTypeInternal;
extern TransferToInfeedRequestDefaultTypeInternal _TransferToInfeedRequest_default_instance_;
class TransferToInfeedResponse;
class TransferToInfeedResponseDefaultTypeInternal;
extern TransferToInfeedResponseDefaultTypeInternal _TransferToInfeedResponse_default_instance_;
class TransferToServerRequest;
class TransferToServerRequestDefaultTypeInternal;
extern TransferToServerRequestDefaultTypeInternal _TransferToServerRequest_default_instance_;
class TransferToServerResponse;
class TransferToServerResponseDefaultTypeInternal;
extern TransferToServerResponseDefaultTypeInternal _TransferToServerResponse_default_instance_;
class UnpackRequest;
class UnpackRequestDefaultTypeInternal;
extern UnpackRequestDefaultTypeInternal _UnpackRequest_default_instance_;
class UnpackResponse;
class UnpackResponseDefaultTypeInternal;
extern UnpackResponseDefaultTypeInternal _UnpackResponse_default_instance_;
class UnregisterRequest;
class UnregisterRequestDefaultTypeInternal;
extern UnregisterRequestDefaultTypeInternal _UnregisterRequest_default_instance_;
class UnregisterResponse;
class UnregisterResponseDefaultTypeInternal;
extern UnregisterResponseDefaultTypeInternal _UnregisterResponse_default_instance_;
class WaitForExecutionRequest;
class WaitForExecutionRequestDefaultTypeInternal;
extern WaitForExecutionRequestDefaultTypeInternal _WaitForExecutionRequest_default_instance_;
class WaitForExecutionResponse;
class WaitForExecutionResponseDefaultTypeInternal;
extern WaitForExecutionResponseDefaultTypeInternal _WaitForExecutionResponse_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::CompileRequest* Arena::CreateMaybeMessage<::xla::CompileRequest>(Arena*);
template<> ::xla::CompileResponse* Arena::CreateMaybeMessage<::xla::CompileResponse>(Arena*);
template<> ::xla::ComputationGraphStatsRequest* Arena::CreateMaybeMessage<::xla::ComputationGraphStatsRequest>(Arena*);
template<> ::xla::ComputationStatsResponse* Arena::CreateMaybeMessage<::xla::ComputationStatsResponse>(Arena*);
template<> ::xla::ComputeConstantGraphRequest* Arena::CreateMaybeMessage<::xla::ComputeConstantGraphRequest>(Arena*);
template<> ::xla::ComputeConstantResponse* Arena::CreateMaybeMessage<::xla::ComputeConstantResponse>(Arena*);
template<> ::xla::CreateChannelHandleRequest* Arena::CreateMaybeMessage<::xla::CreateChannelHandleRequest>(Arena*);
template<> ::xla::CreateChannelHandleResponse* Arena::CreateMaybeMessage<::xla::CreateChannelHandleResponse>(Arena*);
template<> ::xla::DebugOptions* Arena::CreateMaybeMessage<::xla::DebugOptions>(Arena*);
template<> ::xla::DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse>(Arena*);
template<> ::xla::DeconstructTupleRequest* Arena::CreateMaybeMessage<::xla::DeconstructTupleRequest>(Arena*);
template<> ::xla::DeconstructTupleResponse* Arena::CreateMaybeMessage<::xla::DeconstructTupleResponse>(Arena*);
template<> ::xla::ExecuteGraphParallelRequest* Arena::CreateMaybeMessage<::xla::ExecuteGraphParallelRequest>(Arena*);
template<> ::xla::ExecuteGraphRequest* Arena::CreateMaybeMessage<::xla::ExecuteGraphRequest>(Arena*);
template<> ::xla::ExecuteParallelResponse* Arena::CreateMaybeMessage<::xla::ExecuteParallelResponse>(Arena*);
template<> ::xla::ExecuteRequest* Arena::CreateMaybeMessage<::xla::ExecuteRequest>(Arena*);
template<> ::xla::ExecuteResponse* Arena::CreateMaybeMessage<::xla::ExecuteResponse>(Arena*);
template<> ::xla::ExecutionOptions* Arena::CreateMaybeMessage<::xla::ExecutionOptions>(Arena*);
template<> ::xla::GetDeviceHandlesRequest* Arena::CreateMaybeMessage<::xla::GetDeviceHandlesRequest>(Arena*);
template<> ::xla::GetDeviceHandlesResponse* Arena::CreateMaybeMessage<::xla::GetDeviceHandlesResponse>(Arena*);
template<> ::xla::GetShapeRequest* Arena::CreateMaybeMessage<::xla::GetShapeRequest>(Arena*);
template<> ::xla::GetShapeResponse* Arena::CreateMaybeMessage<::xla::GetShapeResponse>(Arena*);
template<> ::xla::LoadDataRequest* Arena::CreateMaybeMessage<::xla::LoadDataRequest>(Arena*);
template<> ::xla::LoadDataResponse* Arena::CreateMaybeMessage<::xla::LoadDataResponse>(Arena*);
template<> ::xla::ResetDeviceRequest* Arena::CreateMaybeMessage<::xla::ResetDeviceRequest>(Arena*);
template<> ::xla::ResetDeviceResponse* Arena::CreateMaybeMessage<::xla::ResetDeviceResponse>(Arena*);
template<> ::xla::TransferFromOutfeedRequest* Arena::CreateMaybeMessage<::xla::TransferFromOutfeedRequest>(Arena*);
template<> ::xla::TransferFromOutfeedResponse* Arena::CreateMaybeMessage<::xla::TransferFromOutfeedResponse>(Arena*);
template<> ::xla::TransferToClientRequest* Arena::CreateMaybeMessage<::xla::TransferToClientRequest>(Arena*);
template<> ::xla::TransferToClientResponse* Arena::CreateMaybeMessage<::xla::TransferToClientResponse>(Arena*);
template<> ::xla::TransferToInfeedRequest* Arena::CreateMaybeMessage<::xla::TransferToInfeedRequest>(Arena*);
template<> ::xla::TransferToInfeedResponse* Arena::CreateMaybeMessage<::xla::TransferToInfeedResponse>(Arena*);
template<> ::xla::TransferToServerRequest* Arena::CreateMaybeMessage<::xla::TransferToServerRequest>(Arena*);
template<> ::xla::TransferToServerResponse* Arena::CreateMaybeMessage<::xla::TransferToServerResponse>(Arena*);
template<> ::xla::UnpackRequest* Arena::CreateMaybeMessage<::xla::UnpackRequest>(Arena*);
template<> ::xla::UnpackResponse* Arena::CreateMaybeMessage<::xla::UnpackResponse>(Arena*);
template<> ::xla::UnregisterRequest* Arena::CreateMaybeMessage<::xla::UnregisterRequest>(Arena*);
template<> ::xla::UnregisterResponse* Arena::CreateMaybeMessage<::xla::UnregisterResponse>(Arena*);
template<> ::xla::WaitForExecutionRequest* Arena::CreateMaybeMessage<::xla::WaitForExecutionRequest>(Arena*);
template<> ::xla::WaitForExecutionResponse* Arena::CreateMaybeMessage<::xla::WaitForExecutionResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

enum DebugOptions_StepMarkerLocation : int {
  DebugOptions_StepMarkerLocation_STEP_MARK_AT_ENTRY = 0,
  DebugOptions_StepMarkerLocation_STEP_MARK_AT_TOP_LEVEL_WHILE_LOOP = 1,
  DebugOptions_StepMarkerLocation_STEP_MARK_AT_SECOND_LEVEL_WHILE_LOOP = 3,
  DebugOptions_StepMarkerLocation_STEP_MARK_NONE = 2,
  DebugOptions_StepMarkerLocation_DebugOptions_StepMarkerLocation_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  DebugOptions_StepMarkerLocation_DebugOptions_StepMarkerLocation_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool DebugOptions_StepMarkerLocation_IsValid(int value);
constexpr DebugOptions_StepMarkerLocation DebugOptions_StepMarkerLocation_StepMarkerLocation_MIN = DebugOptions_StepMarkerLocation_STEP_MARK_AT_ENTRY;
constexpr DebugOptions_StepMarkerLocation DebugOptions_StepMarkerLocation_StepMarkerLocation_MAX = DebugOptions_StepMarkerLocation_STEP_MARK_AT_SECOND_LEVEL_WHILE_LOOP;
constexpr int DebugOptions_StepMarkerLocation_StepMarkerLocation_ARRAYSIZE = DebugOptions_StepMarkerLocation_StepMarkerLocation_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DebugOptions_StepMarkerLocation_descriptor();
template<typename T>
inline const std::string& DebugOptions_StepMarkerLocation_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DebugOptions_StepMarkerLocation>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DebugOptions_StepMarkerLocation_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DebugOptions_StepMarkerLocation_descriptor(), enum_t_value);
}
inline bool DebugOptions_StepMarkerLocation_Parse(
    const std::string& name, DebugOptions_StepMarkerLocation* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DebugOptions_StepMarkerLocation>(
    DebugOptions_StepMarkerLocation_descriptor(), name, value);
}
// ===================================================================

class DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    0 > SuperType;
  DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse();
  DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse& other);
  static const DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse*>(&_DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.DebugOptions.XlaBackendExtraOptionsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.DebugOptions.XlaBackendExtraOptionsEntry.value");
 }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[0];
  }

  public:
};

// -------------------------------------------------------------------

class DebugOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DebugOptions) */ {
 public:
  DebugOptions();
  virtual ~DebugOptions();

  DebugOptions(const DebugOptions& from);
  DebugOptions(DebugOptions&& from) noexcept
    : DebugOptions() {
    *this = ::std::move(from);
  }

  inline DebugOptions& operator=(const DebugOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugOptions& operator=(DebugOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DebugOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugOptions* internal_default_instance() {
    return reinterpret_cast<const DebugOptions*>(
               &_DebugOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DebugOptions& a, DebugOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugOptions* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DebugOptions* New() const final {
    return CreateMaybeMessage<DebugOptions>(nullptr);
  }

  DebugOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DebugOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DebugOptions& from);
  void MergeFrom(const DebugOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DebugOptions";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  typedef DebugOptions_StepMarkerLocation StepMarkerLocation;
  static constexpr StepMarkerLocation STEP_MARK_AT_ENTRY =
    DebugOptions_StepMarkerLocation_STEP_MARK_AT_ENTRY;
  static constexpr StepMarkerLocation STEP_MARK_AT_TOP_LEVEL_WHILE_LOOP =
    DebugOptions_StepMarkerLocation_STEP_MARK_AT_TOP_LEVEL_WHILE_LOOP;
  static constexpr StepMarkerLocation STEP_MARK_AT_SECOND_LEVEL_WHILE_LOOP =
    DebugOptions_StepMarkerLocation_STEP_MARK_AT_SECOND_LEVEL_WHILE_LOOP;
  static constexpr StepMarkerLocation STEP_MARK_NONE =
    DebugOptions_StepMarkerLocation_STEP_MARK_NONE;
  static inline bool StepMarkerLocation_IsValid(int value) {
    return DebugOptions_StepMarkerLocation_IsValid(value);
  }
  static constexpr StepMarkerLocation StepMarkerLocation_MIN =
    DebugOptions_StepMarkerLocation_StepMarkerLocation_MIN;
  static constexpr StepMarkerLocation StepMarkerLocation_MAX =
    DebugOptions_StepMarkerLocation_StepMarkerLocation_MAX;
  static constexpr int StepMarkerLocation_ARRAYSIZE =
    DebugOptions_StepMarkerLocation_StepMarkerLocation_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  StepMarkerLocation_descriptor() {
    return DebugOptions_StepMarkerLocation_descriptor();
  }
  template<typename T>
  static inline const std::string& StepMarkerLocation_Name(T enum_t_value) {
    static_assert(::std::is_same<T, StepMarkerLocation>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function StepMarkerLocation_Name.");
    return DebugOptions_StepMarkerLocation_Name(enum_t_value);
  }
  static inline bool StepMarkerLocation_Parse(const std::string& name,
      StepMarkerLocation* value) {
    return DebugOptions_StepMarkerLocation_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kXlaDisableHloPassesFieldNumber = 30,
    kXlaEnableHloPassesOnlyFieldNumber = 124,
    kXlaGpuPtxFileFieldNumber = 127,
    kXlaGpuLlvmIrFileFieldNumber = 150,
    kXlaBackendExtraOptionsFieldNumber = 500,
    kXlaGpuCudaDataDirFieldNumber = 61,
    kXlaDumpToFieldNumber = 109,
    kXlaDumpHloModuleReFieldNumber = 110,
    kXlaDumpHloPassReFieldNumber = 111,
    kXlaGpuAlgorithmDenylistPathFieldNumber = 128,
    kXlaGpuAsmExtraFlagsFieldNumber = 141,
    kXlaBackendOptimizationLevelFieldNumber = 31,
    kXlaHloGraphAddressesFieldNumber = 2,
    kXlaHloProfileFieldNumber = 9,
    kXlaDisableAllHloPassesFieldNumber = 104,
    kXlaEmbedIrInExecutableFieldNumber = 33,
    kXlaGpuDeterministicOpsFieldNumber = 148,
    kXlaGpuEnableAsyncAllReduceFieldNumber = 152,
    kXlaEliminateHloImplicitBroadcastFieldNumber = 35,
    kXlaCpuMultiThreadEigenFieldNumber = 60,
    kXlaGpuFtzFieldNumber = 62,
    kXlaGpuDisableMultiStreamingFieldNumber = 63,
    kXlaLlvmDisableExpensivePassesFieldNumber = 73,
    kXlaTestAllOutputLayoutsFieldNumber = 90,
    kXlaTestAllInputLayoutsFieldNumber = 91,
    kXlaHloGraphShardingColorFieldNumber = 92,
    kXlaGpuUseRandomStreamsFieldNumber = 134,
    kXlaLlvmEnableAliasScopeMetadataFieldNumber = 70,
    kXlaLlvmEnableNoaliasMetadataFieldNumber = 71,
    kXlaLlvmEnableInvariantLoadMetadataFieldNumber = 72,
    kXlaGpuMaxKernelUnrollFactorFieldNumber = 98,
    kXlaForceHostPlatformDeviceCountFieldNumber = 102,
    kXlaGpuUseCudnnBatchnormFieldNumber = 94,
    kXlaCpuUseMklDnnFieldNumber = 97,
    kXlaCpuEnableFastMathFieldNumber = 99,
    kXlaCpuFastMathHonorNansFieldNumber = 120,
    kXlaGpuEnableFastMinMaxFieldNumber = 100,
    kXlaAllowExcessPrecisionFieldNumber = 122,
    kXlaGpuCrashOnVerificationFailuresFieldNumber = 101,
    kXlaGpuDisableGpuasmOptimizationsFieldNumber = 103,
    kXlaStepMarkerLocationFieldNumber = 108,
    kXlaHloEvaluatorUseFastPathFieldNumber = 106,
    kXlaAllowScalarIndexDynamicOpsFieldNumber = 107,
    kXlaDumpHloAsTextFieldNumber = 112,
    kXlaDumpHloAsProtoFieldNumber = 113,
    kXlaGpuAutotuneLevelFieldNumber = 123,
    kXlaDumpHloAsDotFieldNumber = 114,
    kXlaDumpHloAsUrlFieldNumber = 115,
    kXlaDumpHloAsHtmlFieldNumber = 116,
    kXlaDumpFusionVisualizationFieldNumber = 149,
    kXlaCpuFastMathHonorInfsFieldNumber = 121,
    kXlaCpuFastMathHonorDivisionFieldNumber = 126,
    kXlaCpuFastMathHonorFunctionsFieldNumber = 129,
    kXlaCpuEnableFastMinMaxFieldNumber = 140,
    kXlaDumpMaxHloModulesFieldNumber = 132,
    kXlaGpuForceConvNchwFieldNumber = 125,
    kXlaGpuForceConvNhwcFieldNumber = 146,
    kXlaGpuDeterministicReductionsFieldNumber = 130,
    kXlaTpuDetectNanFieldNumber = 135,
    kXlaDumpHloSnapshotsFieldNumber = 118,
    kXlaDumpIncludeTimestampFieldNumber = 131,
    kXlaDumpModuleMetadataFieldNumber = 144,
    kXlaDumpCompressProtosFieldNumber = 151,
    kXlaTpuDetectInfFieldNumber = 136,
    kXlaCpuEnableXprofTracemeFieldNumber = 137,
    kXlaGpuUnsafeFallbackToDriverOnPtxasNotFoundFieldNumber = 138,
    kXlaDetailedLoggingAndDumpingFieldNumber = 143,
    kXlaMultiheapSizeConstraintPerHeapFieldNumber = 142,
    kXlaGpuForceCompilationParallelismFieldNumber = 147,
  };
  // repeated string xla_disable_hlo_passes = 30;
  int xla_disable_hlo_passes_size() const;
  void clear_xla_disable_hlo_passes();
  const std::string& xla_disable_hlo_passes(int index) const;
  std::string* mutable_xla_disable_hlo_passes(int index);
  void set_xla_disable_hlo_passes(int index, const std::string& value);
  void set_xla_disable_hlo_passes(int index, std::string&& value);
  void set_xla_disable_hlo_passes(int index, const char* value);
  void set_xla_disable_hlo_passes(int index, const char* value, size_t size);
  std::string* add_xla_disable_hlo_passes();
  void add_xla_disable_hlo_passes(const std::string& value);
  void add_xla_disable_hlo_passes(std::string&& value);
  void add_xla_disable_hlo_passes(const char* value);
  void add_xla_disable_hlo_passes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& xla_disable_hlo_passes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_xla_disable_hlo_passes();

  // repeated string xla_enable_hlo_passes_only = 124;
  int xla_enable_hlo_passes_only_size() const;
  void clear_xla_enable_hlo_passes_only();
  const std::string& xla_enable_hlo_passes_only(int index) const;
  std::string* mutable_xla_enable_hlo_passes_only(int index);
  void set_xla_enable_hlo_passes_only(int index, const std::string& value);
  void set_xla_enable_hlo_passes_only(int index, std::string&& value);
  void set_xla_enable_hlo_passes_only(int index, const char* value);
  void set_xla_enable_hlo_passes_only(int index, const char* value, size_t size);
  std::string* add_xla_enable_hlo_passes_only();
  void add_xla_enable_hlo_passes_only(const std::string& value);
  void add_xla_enable_hlo_passes_only(std::string&& value);
  void add_xla_enable_hlo_passes_only(const char* value);
  void add_xla_enable_hlo_passes_only(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& xla_enable_hlo_passes_only() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_xla_enable_hlo_passes_only();

  // repeated string xla_gpu_ptx_file = 127;
  int xla_gpu_ptx_file_size() const;
  void clear_xla_gpu_ptx_file();
  const std::string& xla_gpu_ptx_file(int index) const;
  std::string* mutable_xla_gpu_ptx_file(int index);
  void set_xla_gpu_ptx_file(int index, const std::string& value);
  void set_xla_gpu_ptx_file(int index, std::string&& value);
  void set_xla_gpu_ptx_file(int index, const char* value);
  void set_xla_gpu_ptx_file(int index, const char* value, size_t size);
  std::string* add_xla_gpu_ptx_file();
  void add_xla_gpu_ptx_file(const std::string& value);
  void add_xla_gpu_ptx_file(std::string&& value);
  void add_xla_gpu_ptx_file(const char* value);
  void add_xla_gpu_ptx_file(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& xla_gpu_ptx_file() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_xla_gpu_ptx_file();

  // repeated string xla_gpu_llvm_ir_file = 150;
  int xla_gpu_llvm_ir_file_size() const;
  void clear_xla_gpu_llvm_ir_file();
  const std::string& xla_gpu_llvm_ir_file(int index) const;
  std::string* mutable_xla_gpu_llvm_ir_file(int index);
  void set_xla_gpu_llvm_ir_file(int index, const std::string& value);
  void set_xla_gpu_llvm_ir_file(int index, std::string&& value);
  void set_xla_gpu_llvm_ir_file(int index, const char* value);
  void set_xla_gpu_llvm_ir_file(int index, const char* value, size_t size);
  std::string* add_xla_gpu_llvm_ir_file();
  void add_xla_gpu_llvm_ir_file(const std::string& value);
  void add_xla_gpu_llvm_ir_file(std::string&& value);
  void add_xla_gpu_llvm_ir_file(const char* value);
  void add_xla_gpu_llvm_ir_file(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& xla_gpu_llvm_ir_file() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_xla_gpu_llvm_ir_file();

  // map<string, string> xla_backend_extra_options = 500;
  int xla_backend_extra_options_size() const;
  void clear_xla_backend_extra_options();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      xla_backend_extra_options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_xla_backend_extra_options();

  // string xla_gpu_cuda_data_dir = 61;
  void clear_xla_gpu_cuda_data_dir();
  const std::string& xla_gpu_cuda_data_dir() const;
  void set_xla_gpu_cuda_data_dir(const std::string& value);
  void set_xla_gpu_cuda_data_dir(std::string&& value);
  void set_xla_gpu_cuda_data_dir(const char* value);
  void set_xla_gpu_cuda_data_dir(const char* value, size_t size);
  std::string* mutable_xla_gpu_cuda_data_dir();
  std::string* release_xla_gpu_cuda_data_dir();
  void set_allocated_xla_gpu_cuda_data_dir(std::string* xla_gpu_cuda_data_dir);

  // string xla_dump_to = 109;
  void clear_xla_dump_to();
  const std::string& xla_dump_to() const;
  void set_xla_dump_to(const std::string& value);
  void set_xla_dump_to(std::string&& value);
  void set_xla_dump_to(const char* value);
  void set_xla_dump_to(const char* value, size_t size);
  std::string* mutable_xla_dump_to();
  std::string* release_xla_dump_to();
  void set_allocated_xla_dump_to(std::string* xla_dump_to);

  // string xla_dump_hlo_module_re = 110;
  void clear_xla_dump_hlo_module_re();
  const std::string& xla_dump_hlo_module_re() const;
  void set_xla_dump_hlo_module_re(const std::string& value);
  void set_xla_dump_hlo_module_re(std::string&& value);
  void set_xla_dump_hlo_module_re(const char* value);
  void set_xla_dump_hlo_module_re(const char* value, size_t size);
  std::string* mutable_xla_dump_hlo_module_re();
  std::string* release_xla_dump_hlo_module_re();
  void set_allocated_xla_dump_hlo_module_re(std::string* xla_dump_hlo_module_re);

  // string xla_dump_hlo_pass_re = 111;
  void clear_xla_dump_hlo_pass_re();
  const std::string& xla_dump_hlo_pass_re() const;
  void set_xla_dump_hlo_pass_re(const std::string& value);
  void set_xla_dump_hlo_pass_re(std::string&& value);
  void set_xla_dump_hlo_pass_re(const char* value);
  void set_xla_dump_hlo_pass_re(const char* value, size_t size);
  std::string* mutable_xla_dump_hlo_pass_re();
  std::string* release_xla_dump_hlo_pass_re();
  void set_allocated_xla_dump_hlo_pass_re(std::string* xla_dump_hlo_pass_re);

  // string xla_gpu_algorithm_denylist_path = 128;
  void clear_xla_gpu_algorithm_denylist_path();
  const std::string& xla_gpu_algorithm_denylist_path() const;
  void set_xla_gpu_algorithm_denylist_path(const std::string& value);
  void set_xla_gpu_algorithm_denylist_path(std::string&& value);
  void set_xla_gpu_algorithm_denylist_path(const char* value);
  void set_xla_gpu_algorithm_denylist_path(const char* value, size_t size);
  std::string* mutable_xla_gpu_algorithm_denylist_path();
  std::string* release_xla_gpu_algorithm_denylist_path();
  void set_allocated_xla_gpu_algorithm_denylist_path(std::string* xla_gpu_algorithm_denylist_path);

  // string xla_gpu_asm_extra_flags = 141;
  void clear_xla_gpu_asm_extra_flags();
  const std::string& xla_gpu_asm_extra_flags() const;
  void set_xla_gpu_asm_extra_flags(const std::string& value);
  void set_xla_gpu_asm_extra_flags(std::string&& value);
  void set_xla_gpu_asm_extra_flags(const char* value);
  void set_xla_gpu_asm_extra_flags(const char* value, size_t size);
  std::string* mutable_xla_gpu_asm_extra_flags();
  std::string* release_xla_gpu_asm_extra_flags();
  void set_allocated_xla_gpu_asm_extra_flags(std::string* xla_gpu_asm_extra_flags);

  // int32 xla_backend_optimization_level = 31;
  void clear_xla_backend_optimization_level();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_backend_optimization_level() const;
  void set_xla_backend_optimization_level(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool xla_hlo_graph_addresses = 2;
  void clear_xla_hlo_graph_addresses();
  bool xla_hlo_graph_addresses() const;
  void set_xla_hlo_graph_addresses(bool value);

  // bool xla_hlo_profile = 9;
  void clear_xla_hlo_profile();
  bool xla_hlo_profile() const;
  void set_xla_hlo_profile(bool value);

  // bool xla_disable_all_hlo_passes = 104;
  void clear_xla_disable_all_hlo_passes();
  bool xla_disable_all_hlo_passes() const;
  void set_xla_disable_all_hlo_passes(bool value);

  // bool xla_embed_ir_in_executable = 33;
  void clear_xla_embed_ir_in_executable();
  bool xla_embed_ir_in_executable() const;
  void set_xla_embed_ir_in_executable(bool value);

  // bool xla_gpu_deterministic_ops = 148;
  void clear_xla_gpu_deterministic_ops();
  bool xla_gpu_deterministic_ops() const;
  void set_xla_gpu_deterministic_ops(bool value);

  // bool xla_gpu_enable_async_all_reduce = 152;
  void clear_xla_gpu_enable_async_all_reduce();
  bool xla_gpu_enable_async_all_reduce() const;
  void set_xla_gpu_enable_async_all_reduce(bool value);

  // bool xla_eliminate_hlo_implicit_broadcast = 35;
  void clear_xla_eliminate_hlo_implicit_broadcast();
  bool xla_eliminate_hlo_implicit_broadcast() const;
  void set_xla_eliminate_hlo_implicit_broadcast(bool value);

  // bool xla_cpu_multi_thread_eigen = 60;
  void clear_xla_cpu_multi_thread_eigen();
  bool xla_cpu_multi_thread_eigen() const;
  void set_xla_cpu_multi_thread_eigen(bool value);

  // bool xla_gpu_ftz = 62;
  void clear_xla_gpu_ftz();
  bool xla_gpu_ftz() const;
  void set_xla_gpu_ftz(bool value);

  // bool xla_gpu_disable_multi_streaming = 63;
  void clear_xla_gpu_disable_multi_streaming();
  bool xla_gpu_disable_multi_streaming() const;
  void set_xla_gpu_disable_multi_streaming(bool value);

  // bool xla_llvm_disable_expensive_passes = 73;
  void clear_xla_llvm_disable_expensive_passes();
  bool xla_llvm_disable_expensive_passes() const;
  void set_xla_llvm_disable_expensive_passes(bool value);

  // bool xla_test_all_output_layouts = 90;
  void clear_xla_test_all_output_layouts();
  bool xla_test_all_output_layouts() const;
  void set_xla_test_all_output_layouts(bool value);

  // bool xla_test_all_input_layouts = 91;
  void clear_xla_test_all_input_layouts();
  bool xla_test_all_input_layouts() const;
  void set_xla_test_all_input_layouts(bool value);

  // bool xla_hlo_graph_sharding_color = 92;
  void clear_xla_hlo_graph_sharding_color();
  bool xla_hlo_graph_sharding_color() const;
  void set_xla_hlo_graph_sharding_color(bool value);

  // bool xla_gpu_use_random_streams = 134;
  void clear_xla_gpu_use_random_streams();
  bool xla_gpu_use_random_streams() const;
  void set_xla_gpu_use_random_streams(bool value);

  // bool xla_llvm_enable_alias_scope_metadata = 70;
  void clear_xla_llvm_enable_alias_scope_metadata();
  bool xla_llvm_enable_alias_scope_metadata() const;
  void set_xla_llvm_enable_alias_scope_metadata(bool value);

  // bool xla_llvm_enable_noalias_metadata = 71;
  void clear_xla_llvm_enable_noalias_metadata();
  bool xla_llvm_enable_noalias_metadata() const;
  void set_xla_llvm_enable_noalias_metadata(bool value);

  // bool xla_llvm_enable_invariant_load_metadata = 72;
  void clear_xla_llvm_enable_invariant_load_metadata();
  bool xla_llvm_enable_invariant_load_metadata() const;
  void set_xla_llvm_enable_invariant_load_metadata(bool value);

  // int32 xla_gpu_max_kernel_unroll_factor = 98;
  void clear_xla_gpu_max_kernel_unroll_factor();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_gpu_max_kernel_unroll_factor() const;
  void set_xla_gpu_max_kernel_unroll_factor(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 xla_force_host_platform_device_count = 102;
  void clear_xla_force_host_platform_device_count();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_force_host_platform_device_count() const;
  void set_xla_force_host_platform_device_count(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool xla_gpu_use_cudnn_batchnorm = 94;
  void clear_xla_gpu_use_cudnn_batchnorm();
  bool xla_gpu_use_cudnn_batchnorm() const;
  void set_xla_gpu_use_cudnn_batchnorm(bool value);

  // bool xla_cpu_use_mkl_dnn = 97;
  void clear_xla_cpu_use_mkl_dnn();
  bool xla_cpu_use_mkl_dnn() const;
  void set_xla_cpu_use_mkl_dnn(bool value);

  // bool xla_cpu_enable_fast_math = 99;
  void clear_xla_cpu_enable_fast_math();
  bool xla_cpu_enable_fast_math() const;
  void set_xla_cpu_enable_fast_math(bool value);

  // bool xla_cpu_fast_math_honor_nans = 120;
  void clear_xla_cpu_fast_math_honor_nans();
  bool xla_cpu_fast_math_honor_nans() const;
  void set_xla_cpu_fast_math_honor_nans(bool value);

  // bool xla_gpu_enable_fast_min_max = 100;
  void clear_xla_gpu_enable_fast_min_max();
  bool xla_gpu_enable_fast_min_max() const;
  void set_xla_gpu_enable_fast_min_max(bool value);

  // bool xla_allow_excess_precision = 122;
  void clear_xla_allow_excess_precision();
  bool xla_allow_excess_precision() const;
  void set_xla_allow_excess_precision(bool value);

  // bool xla_gpu_crash_on_verification_failures = 101;
  void clear_xla_gpu_crash_on_verification_failures();
  bool xla_gpu_crash_on_verification_failures() const;
  void set_xla_gpu_crash_on_verification_failures(bool value);

  // bool xla_gpu_disable_gpuasm_optimizations = 103;
  void clear_xla_gpu_disable_gpuasm_optimizations();
  bool xla_gpu_disable_gpuasm_optimizations() const;
  void set_xla_gpu_disable_gpuasm_optimizations(bool value);

  // .xla.DebugOptions.StepMarkerLocation xla_step_marker_location = 108;
  void clear_xla_step_marker_location();
  ::xla::DebugOptions_StepMarkerLocation xla_step_marker_location() const;
  void set_xla_step_marker_location(::xla::DebugOptions_StepMarkerLocation value);

  // bool xla_hlo_evaluator_use_fast_path = 106;
  void clear_xla_hlo_evaluator_use_fast_path();
  bool xla_hlo_evaluator_use_fast_path() const;
  void set_xla_hlo_evaluator_use_fast_path(bool value);

  // bool xla_allow_scalar_index_dynamic_ops = 107;
  void clear_xla_allow_scalar_index_dynamic_ops();
  bool xla_allow_scalar_index_dynamic_ops() const;
  void set_xla_allow_scalar_index_dynamic_ops(bool value);

  // bool xla_dump_hlo_as_text = 112;
  void clear_xla_dump_hlo_as_text();
  bool xla_dump_hlo_as_text() const;
  void set_xla_dump_hlo_as_text(bool value);

  // bool xla_dump_hlo_as_proto = 113;
  void clear_xla_dump_hlo_as_proto();
  bool xla_dump_hlo_as_proto() const;
  void set_xla_dump_hlo_as_proto(bool value);

  // int32 xla_gpu_autotune_level = 123;
  void clear_xla_gpu_autotune_level();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_gpu_autotune_level() const;
  void set_xla_gpu_autotune_level(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool xla_dump_hlo_as_dot = 114;
  void clear_xla_dump_hlo_as_dot();
  bool xla_dump_hlo_as_dot() const;
  void set_xla_dump_hlo_as_dot(bool value);

  // bool xla_dump_hlo_as_url = 115;
  void clear_xla_dump_hlo_as_url();
  bool xla_dump_hlo_as_url() const;
  void set_xla_dump_hlo_as_url(bool value);

  // bool xla_dump_hlo_as_html = 116;
  void clear_xla_dump_hlo_as_html();
  bool xla_dump_hlo_as_html() const;
  void set_xla_dump_hlo_as_html(bool value);

  // bool xla_dump_fusion_visualization = 149;
  void clear_xla_dump_fusion_visualization();
  bool xla_dump_fusion_visualization() const;
  void set_xla_dump_fusion_visualization(bool value);

  // bool xla_cpu_fast_math_honor_infs = 121;
  void clear_xla_cpu_fast_math_honor_infs();
  bool xla_cpu_fast_math_honor_infs() const;
  void set_xla_cpu_fast_math_honor_infs(bool value);

  // bool xla_cpu_fast_math_honor_division = 126;
  void clear_xla_cpu_fast_math_honor_division();
  bool xla_cpu_fast_math_honor_division() const;
  void set_xla_cpu_fast_math_honor_division(bool value);

  // bool xla_cpu_fast_math_honor_functions = 129;
  void clear_xla_cpu_fast_math_honor_functions();
  bool xla_cpu_fast_math_honor_functions() const;
  void set_xla_cpu_fast_math_honor_functions(bool value);

  // bool xla_cpu_enable_fast_min_max = 140;
  void clear_xla_cpu_enable_fast_min_max();
  bool xla_cpu_enable_fast_min_max() const;
  void set_xla_cpu_enable_fast_min_max(bool value);

  // int32 xla_dump_max_hlo_modules = 132;
  void clear_xla_dump_max_hlo_modules();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_dump_max_hlo_modules() const;
  void set_xla_dump_max_hlo_modules(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool xla_gpu_force_conv_nchw = 125;
  void clear_xla_gpu_force_conv_nchw();
  bool xla_gpu_force_conv_nchw() const;
  void set_xla_gpu_force_conv_nchw(bool value);

  // bool xla_gpu_force_conv_nhwc = 146;
  void clear_xla_gpu_force_conv_nhwc();
  bool xla_gpu_force_conv_nhwc() const;
  void set_xla_gpu_force_conv_nhwc(bool value);

  // bool xla_gpu_deterministic_reductions = 130;
  void clear_xla_gpu_deterministic_reductions();
  bool xla_gpu_deterministic_reductions() const;
  void set_xla_gpu_deterministic_reductions(bool value);

  // bool xla_tpu_detect_nan = 135;
  void clear_xla_tpu_detect_nan();
  bool xla_tpu_detect_nan() const;
  void set_xla_tpu_detect_nan(bool value);

  // bool xla_dump_hlo_snapshots = 118;
  void clear_xla_dump_hlo_snapshots();
  bool xla_dump_hlo_snapshots() const;
  void set_xla_dump_hlo_snapshots(bool value);

  // bool xla_dump_include_timestamp = 131;
  void clear_xla_dump_include_timestamp();
  bool xla_dump_include_timestamp() const;
  void set_xla_dump_include_timestamp(bool value);

  // bool xla_dump_module_metadata = 144;
  void clear_xla_dump_module_metadata();
  bool xla_dump_module_metadata() const;
  void set_xla_dump_module_metadata(bool value);

  // bool xla_dump_compress_protos = 151;
  void clear_xla_dump_compress_protos();
  bool xla_dump_compress_protos() const;
  void set_xla_dump_compress_protos(bool value);

  // bool xla_tpu_detect_inf = 136;
  void clear_xla_tpu_detect_inf();
  bool xla_tpu_detect_inf() const;
  void set_xla_tpu_detect_inf(bool value);

  // bool xla_cpu_enable_xprof_traceme = 137;
  void clear_xla_cpu_enable_xprof_traceme();
  bool xla_cpu_enable_xprof_traceme() const;
  void set_xla_cpu_enable_xprof_traceme(bool value);

  // bool xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found = 138;
  void clear_xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found();
  bool xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found() const;
  void set_xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found(bool value);

  // bool xla_detailed_logging_and_dumping = 143;
  void clear_xla_detailed_logging_and_dumping();
  bool xla_detailed_logging_and_dumping() const;
  void set_xla_detailed_logging_and_dumping(bool value);

  // int32 xla_multiheap_size_constraint_per_heap = 142;
  void clear_xla_multiheap_size_constraint_per_heap();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_multiheap_size_constraint_per_heap() const;
  void set_xla_multiheap_size_constraint_per_heap(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 xla_gpu_force_compilation_parallelism = 147;
  void clear_xla_gpu_force_compilation_parallelism();
  ::PROTOBUF_NAMESPACE_ID::int32 xla_gpu_force_compilation_parallelism() const;
  void set_xla_gpu_force_compilation_parallelism(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:xla.DebugOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> xla_disable_hlo_passes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> xla_enable_hlo_passes_only_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> xla_gpu_ptx_file_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> xla_gpu_llvm_ir_file_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      DebugOptions_XlaBackendExtraOptionsEntry_DoNotUse,
      std::string, std::string,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      0 > xla_backend_extra_options_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr xla_gpu_cuda_data_dir_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr xla_dump_to_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr xla_dump_hlo_module_re_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr xla_dump_hlo_pass_re_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr xla_gpu_algorithm_denylist_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr xla_gpu_asm_extra_flags_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_backend_optimization_level_;
  bool xla_hlo_graph_addresses_;
  bool xla_hlo_profile_;
  bool xla_disable_all_hlo_passes_;
  bool xla_embed_ir_in_executable_;
  bool xla_gpu_deterministic_ops_;
  bool xla_gpu_enable_async_all_reduce_;
  bool xla_eliminate_hlo_implicit_broadcast_;
  bool xla_cpu_multi_thread_eigen_;
  bool xla_gpu_ftz_;
  bool xla_gpu_disable_multi_streaming_;
  bool xla_llvm_disable_expensive_passes_;
  bool xla_test_all_output_layouts_;
  bool xla_test_all_input_layouts_;
  bool xla_hlo_graph_sharding_color_;
  bool xla_gpu_use_random_streams_;
  bool xla_llvm_enable_alias_scope_metadata_;
  bool xla_llvm_enable_noalias_metadata_;
  bool xla_llvm_enable_invariant_load_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_gpu_max_kernel_unroll_factor_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_force_host_platform_device_count_;
  bool xla_gpu_use_cudnn_batchnorm_;
  bool xla_cpu_use_mkl_dnn_;
  bool xla_cpu_enable_fast_math_;
  bool xla_cpu_fast_math_honor_nans_;
  bool xla_gpu_enable_fast_min_max_;
  bool xla_allow_excess_precision_;
  bool xla_gpu_crash_on_verification_failures_;
  bool xla_gpu_disable_gpuasm_optimizations_;
  int xla_step_marker_location_;
  bool xla_hlo_evaluator_use_fast_path_;
  bool xla_allow_scalar_index_dynamic_ops_;
  bool xla_dump_hlo_as_text_;
  bool xla_dump_hlo_as_proto_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_gpu_autotune_level_;
  bool xla_dump_hlo_as_dot_;
  bool xla_dump_hlo_as_url_;
  bool xla_dump_hlo_as_html_;
  bool xla_dump_fusion_visualization_;
  bool xla_cpu_fast_math_honor_infs_;
  bool xla_cpu_fast_math_honor_division_;
  bool xla_cpu_fast_math_honor_functions_;
  bool xla_cpu_enable_fast_min_max_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_dump_max_hlo_modules_;
  bool xla_gpu_force_conv_nchw_;
  bool xla_gpu_force_conv_nhwc_;
  bool xla_gpu_deterministic_reductions_;
  bool xla_tpu_detect_nan_;
  bool xla_dump_hlo_snapshots_;
  bool xla_dump_include_timestamp_;
  bool xla_dump_module_metadata_;
  bool xla_dump_compress_protos_;
  bool xla_tpu_detect_inf_;
  bool xla_cpu_enable_xprof_traceme_;
  bool xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found_;
  bool xla_detailed_logging_and_dumping_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_multiheap_size_constraint_per_heap_;
  ::PROTOBUF_NAMESPACE_ID::int32 xla_gpu_force_compilation_parallelism_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ExecutionOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecutionOptions) */ {
 public:
  ExecutionOptions();
  virtual ~ExecutionOptions();

  ExecutionOptions(const ExecutionOptions& from);
  ExecutionOptions(ExecutionOptions&& from) noexcept
    : ExecutionOptions() {
    *this = ::std::move(from);
  }

  inline ExecutionOptions& operator=(const ExecutionOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutionOptions& operator=(ExecutionOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecutionOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecutionOptions* internal_default_instance() {
    return reinterpret_cast<const ExecutionOptions*>(
               &_ExecutionOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ExecutionOptions& a, ExecutionOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutionOptions* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecutionOptions* New() const final {
    return CreateMaybeMessage<ExecutionOptions>(nullptr);
  }

  ExecutionOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecutionOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecutionOptions& from);
  void MergeFrom(const ExecutionOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutionOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecutionOptions";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceHandlesFieldNumber = 5,
    kShapeWithOutputLayoutFieldNumber = 2,
    kDebugOptionsFieldNumber = 4,
    kDeviceAssignmentFieldNumber = 7,
    kSeedFieldNumber = 3,
    kNumReplicasFieldNumber = 6,
    kNumPartitionsFieldNumber = 9,
    kLaunchIdFieldNumber = 10,
    kAliasPassthroughParamsFieldNumber = 8,
    kUseSpmdPartitioningFieldNumber = 11,
    kDeduplicateHloFieldNumber = 12,
  };
  // repeated .xla.DeviceHandle device_handles = 5;
  int device_handles_size() const;
  void clear_device_handles();
  ::xla::DeviceHandle* mutable_device_handles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >*
      mutable_device_handles();
  const ::xla::DeviceHandle& device_handles(int index) const;
  ::xla::DeviceHandle* add_device_handles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >&
      device_handles() const;

  // .xla.ShapeProto shape_with_output_layout = 2;
  bool has_shape_with_output_layout() const;
  void clear_shape_with_output_layout();
  const ::xla::ShapeProto& shape_with_output_layout() const;
  ::xla::ShapeProto* release_shape_with_output_layout();
  ::xla::ShapeProto* mutable_shape_with_output_layout();
  void set_allocated_shape_with_output_layout(::xla::ShapeProto* shape_with_output_layout);

  // .xla.DebugOptions debug_options = 4;
  bool has_debug_options() const;
  void clear_debug_options();
  const ::xla::DebugOptions& debug_options() const;
  ::xla::DebugOptions* release_debug_options();
  ::xla::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::xla::DebugOptions* debug_options);

  // .xla.DeviceAssignmentProto device_assignment = 7;
  bool has_device_assignment() const;
  void clear_device_assignment();
  const ::xla::DeviceAssignmentProto& device_assignment() const;
  ::xla::DeviceAssignmentProto* release_device_assignment();
  ::xla::DeviceAssignmentProto* mutable_device_assignment();
  void set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment);

  // uint64 seed = 3;
  void clear_seed();
  ::PROTOBUF_NAMESPACE_ID::uint64 seed() const;
  void set_seed(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int32 num_replicas = 6;
  void clear_num_replicas();
  ::PROTOBUF_NAMESPACE_ID::int32 num_replicas() const;
  void set_num_replicas(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 num_partitions = 9;
  void clear_num_partitions();
  ::PROTOBUF_NAMESPACE_ID::int32 num_partitions() const;
  void set_num_partitions(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 launch_id = 10;
  void clear_launch_id();
  ::PROTOBUF_NAMESPACE_ID::int32 launch_id() const;
  void set_launch_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool alias_passthrough_params = 8;
  void clear_alias_passthrough_params();
  bool alias_passthrough_params() const;
  void set_alias_passthrough_params(bool value);

  // bool use_spmd_partitioning = 11;
  void clear_use_spmd_partitioning();
  bool use_spmd_partitioning() const;
  void set_use_spmd_partitioning(bool value);

  // bool deduplicate_hlo = 12;
  void clear_deduplicate_hlo();
  bool deduplicate_hlo() const;
  void set_deduplicate_hlo(bool value);

  // @@protoc_insertion_point(class_scope:xla.ExecutionOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle > device_handles_;
  ::xla::ShapeProto* shape_with_output_layout_;
  ::xla::DebugOptions* debug_options_;
  ::xla::DeviceAssignmentProto* device_assignment_;
  ::PROTOBUF_NAMESPACE_ID::uint64 seed_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_replicas_;
  ::PROTOBUF_NAMESPACE_ID::int32 num_partitions_;
  ::PROTOBUF_NAMESPACE_ID::int32 launch_id_;
  bool alias_passthrough_params_;
  bool use_spmd_partitioning_;
  bool deduplicate_hlo_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class GetDeviceHandlesRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GetDeviceHandlesRequest) */ {
 public:
  GetDeviceHandlesRequest();
  virtual ~GetDeviceHandlesRequest();

  GetDeviceHandlesRequest(const GetDeviceHandlesRequest& from);
  GetDeviceHandlesRequest(GetDeviceHandlesRequest&& from) noexcept
    : GetDeviceHandlesRequest() {
    *this = ::std::move(from);
  }

  inline GetDeviceHandlesRequest& operator=(const GetDeviceHandlesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetDeviceHandlesRequest& operator=(GetDeviceHandlesRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetDeviceHandlesRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetDeviceHandlesRequest* internal_default_instance() {
    return reinterpret_cast<const GetDeviceHandlesRequest*>(
               &_GetDeviceHandlesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetDeviceHandlesRequest& a, GetDeviceHandlesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetDeviceHandlesRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetDeviceHandlesRequest* New() const final {
    return CreateMaybeMessage<GetDeviceHandlesRequest>(nullptr);
  }

  GetDeviceHandlesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetDeviceHandlesRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetDeviceHandlesRequest& from);
  void MergeFrom(const GetDeviceHandlesRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetDeviceHandlesRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GetDeviceHandlesRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceCountFieldNumber = 1,
  };
  // int64 device_count = 1;
  void clear_device_count();
  ::PROTOBUF_NAMESPACE_ID::int64 device_count() const;
  void set_device_count(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.GetDeviceHandlesRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class GetDeviceHandlesResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GetDeviceHandlesResponse) */ {
 public:
  GetDeviceHandlesResponse();
  virtual ~GetDeviceHandlesResponse();

  GetDeviceHandlesResponse(const GetDeviceHandlesResponse& from);
  GetDeviceHandlesResponse(GetDeviceHandlesResponse&& from) noexcept
    : GetDeviceHandlesResponse() {
    *this = ::std::move(from);
  }

  inline GetDeviceHandlesResponse& operator=(const GetDeviceHandlesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetDeviceHandlesResponse& operator=(GetDeviceHandlesResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetDeviceHandlesResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetDeviceHandlesResponse* internal_default_instance() {
    return reinterpret_cast<const GetDeviceHandlesResponse*>(
               &_GetDeviceHandlesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetDeviceHandlesResponse& a, GetDeviceHandlesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetDeviceHandlesResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetDeviceHandlesResponse* New() const final {
    return CreateMaybeMessage<GetDeviceHandlesResponse>(nullptr);
  }

  GetDeviceHandlesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetDeviceHandlesResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetDeviceHandlesResponse& from);
  void MergeFrom(const GetDeviceHandlesResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetDeviceHandlesResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GetDeviceHandlesResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceHandlesFieldNumber = 1,
  };
  // repeated .xla.DeviceHandle device_handles = 1;
  int device_handles_size() const;
  void clear_device_handles();
  ::xla::DeviceHandle* mutable_device_handles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >*
      mutable_device_handles();
  const ::xla::DeviceHandle& device_handles(int index) const;
  ::xla::DeviceHandle* add_device_handles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >&
      device_handles() const;

  // @@protoc_insertion_point(class_scope:xla.GetDeviceHandlesResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle > device_handles_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferToClientRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferToClientRequest) */ {
 public:
  TransferToClientRequest();
  virtual ~TransferToClientRequest();

  TransferToClientRequest(const TransferToClientRequest& from);
  TransferToClientRequest(TransferToClientRequest&& from) noexcept
    : TransferToClientRequest() {
    *this = ::std::move(from);
  }

  inline TransferToClientRequest& operator=(const TransferToClientRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferToClientRequest& operator=(TransferToClientRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferToClientRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferToClientRequest* internal_default_instance() {
    return reinterpret_cast<const TransferToClientRequest*>(
               &_TransferToClientRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(TransferToClientRequest& a, TransferToClientRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferToClientRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferToClientRequest* New() const final {
    return CreateMaybeMessage<TransferToClientRequest>(nullptr);
  }

  TransferToClientRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferToClientRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferToClientRequest& from);
  void MergeFrom(const TransferToClientRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferToClientRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferToClientRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kShapeWithLayoutFieldNumber = 2,
  };
  // .xla.GlobalDataHandle data = 1;
  bool has_data() const;
  void clear_data();
  const ::xla::GlobalDataHandle& data() const;
  ::xla::GlobalDataHandle* release_data();
  ::xla::GlobalDataHandle* mutable_data();
  void set_allocated_data(::xla::GlobalDataHandle* data);

  // .xla.ShapeProto shape_with_layout = 2;
  bool has_shape_with_layout() const;
  void clear_shape_with_layout();
  const ::xla::ShapeProto& shape_with_layout() const;
  ::xla::ShapeProto* release_shape_with_layout();
  ::xla::ShapeProto* mutable_shape_with_layout();
  void set_allocated_shape_with_layout(::xla::ShapeProto* shape_with_layout);

  // @@protoc_insertion_point(class_scope:xla.TransferToClientRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* data_;
  ::xla::ShapeProto* shape_with_layout_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferToClientResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferToClientResponse) */ {
 public:
  TransferToClientResponse();
  virtual ~TransferToClientResponse();

  TransferToClientResponse(const TransferToClientResponse& from);
  TransferToClientResponse(TransferToClientResponse&& from) noexcept
    : TransferToClientResponse() {
    *this = ::std::move(from);
  }

  inline TransferToClientResponse& operator=(const TransferToClientResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferToClientResponse& operator=(TransferToClientResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferToClientResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferToClientResponse* internal_default_instance() {
    return reinterpret_cast<const TransferToClientResponse*>(
               &_TransferToClientResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TransferToClientResponse& a, TransferToClientResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferToClientResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferToClientResponse* New() const final {
    return CreateMaybeMessage<TransferToClientResponse>(nullptr);
  }

  TransferToClientResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferToClientResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferToClientResponse& from);
  void MergeFrom(const TransferToClientResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferToClientResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferToClientResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiteralFieldNumber = 1,
  };
  // .xla.LiteralProto literal = 1;
  bool has_literal() const;
  void clear_literal();
  const ::xla::LiteralProto& literal() const;
  ::xla::LiteralProto* release_literal();
  ::xla::LiteralProto* mutable_literal();
  void set_allocated_literal(::xla::LiteralProto* literal);

  // @@protoc_insertion_point(class_scope:xla.TransferToClientResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::LiteralProto* literal_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferToServerRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferToServerRequest) */ {
 public:
  TransferToServerRequest();
  virtual ~TransferToServerRequest();

  TransferToServerRequest(const TransferToServerRequest& from);
  TransferToServerRequest(TransferToServerRequest&& from) noexcept
    : TransferToServerRequest() {
    *this = ::std::move(from);
  }

  inline TransferToServerRequest& operator=(const TransferToServerRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferToServerRequest& operator=(TransferToServerRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferToServerRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferToServerRequest* internal_default_instance() {
    return reinterpret_cast<const TransferToServerRequest*>(
               &_TransferToServerRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(TransferToServerRequest& a, TransferToServerRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferToServerRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferToServerRequest* New() const final {
    return CreateMaybeMessage<TransferToServerRequest>(nullptr);
  }

  TransferToServerRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferToServerRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferToServerRequest& from);
  void MergeFrom(const TransferToServerRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferToServerRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferToServerRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiteralFieldNumber = 1,
    kDeviceHandleFieldNumber = 2,
  };
  // .xla.LiteralProto literal = 1;
  bool has_literal() const;
  void clear_literal();
  const ::xla::LiteralProto& literal() const;
  ::xla::LiteralProto* release_literal();
  ::xla::LiteralProto* mutable_literal();
  void set_allocated_literal(::xla::LiteralProto* literal);

  // .xla.DeviceHandle device_handle = 2;
  bool has_device_handle() const;
  void clear_device_handle();
  const ::xla::DeviceHandle& device_handle() const;
  ::xla::DeviceHandle* release_device_handle();
  ::xla::DeviceHandle* mutable_device_handle();
  void set_allocated_device_handle(::xla::DeviceHandle* device_handle);

  // @@protoc_insertion_point(class_scope:xla.TransferToServerRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::LiteralProto* literal_;
  ::xla::DeviceHandle* device_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferToServerResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferToServerResponse) */ {
 public:
  TransferToServerResponse();
  virtual ~TransferToServerResponse();

  TransferToServerResponse(const TransferToServerResponse& from);
  TransferToServerResponse(TransferToServerResponse&& from) noexcept
    : TransferToServerResponse() {
    *this = ::std::move(from);
  }

  inline TransferToServerResponse& operator=(const TransferToServerResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferToServerResponse& operator=(TransferToServerResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferToServerResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferToServerResponse* internal_default_instance() {
    return reinterpret_cast<const TransferToServerResponse*>(
               &_TransferToServerResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TransferToServerResponse& a, TransferToServerResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferToServerResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferToServerResponse* New() const final {
    return CreateMaybeMessage<TransferToServerResponse>(nullptr);
  }

  TransferToServerResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferToServerResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferToServerResponse& from);
  void MergeFrom(const TransferToServerResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferToServerResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferToServerResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
  };
  // .xla.GlobalDataHandle data = 1;
  bool has_data() const;
  void clear_data();
  const ::xla::GlobalDataHandle& data() const;
  ::xla::GlobalDataHandle* release_data();
  ::xla::GlobalDataHandle* mutable_data();
  void set_allocated_data(::xla::GlobalDataHandle* data);

  // @@protoc_insertion_point(class_scope:xla.TransferToServerResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferToInfeedRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferToInfeedRequest) */ {
 public:
  TransferToInfeedRequest();
  virtual ~TransferToInfeedRequest();

  TransferToInfeedRequest(const TransferToInfeedRequest& from);
  TransferToInfeedRequest(TransferToInfeedRequest&& from) noexcept
    : TransferToInfeedRequest() {
    *this = ::std::move(from);
  }

  inline TransferToInfeedRequest& operator=(const TransferToInfeedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferToInfeedRequest& operator=(TransferToInfeedRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferToInfeedRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferToInfeedRequest* internal_default_instance() {
    return reinterpret_cast<const TransferToInfeedRequest*>(
               &_TransferToInfeedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(TransferToInfeedRequest& a, TransferToInfeedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferToInfeedRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferToInfeedRequest* New() const final {
    return CreateMaybeMessage<TransferToInfeedRequest>(nullptr);
  }

  TransferToInfeedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferToInfeedRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferToInfeedRequest& from);
  void MergeFrom(const TransferToInfeedRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferToInfeedRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferToInfeedRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiteralFieldNumber = 1,
    kDeviceHandleFieldNumber = 3,
    kReplicaIdFieldNumber = 2,
  };
  // .xla.LiteralProto literal = 1;
  bool has_literal() const;
  void clear_literal();
  const ::xla::LiteralProto& literal() const;
  ::xla::LiteralProto* release_literal();
  ::xla::LiteralProto* mutable_literal();
  void set_allocated_literal(::xla::LiteralProto* literal);

  // .xla.DeviceHandle device_handle = 3;
  bool has_device_handle() const;
  void clear_device_handle();
  const ::xla::DeviceHandle& device_handle() const;
  ::xla::DeviceHandle* release_device_handle();
  ::xla::DeviceHandle* mutable_device_handle();
  void set_allocated_device_handle(::xla::DeviceHandle* device_handle);

  // int64 replica_id = 2;
  void clear_replica_id();
  ::PROTOBUF_NAMESPACE_ID::int64 replica_id() const;
  void set_replica_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.TransferToInfeedRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::LiteralProto* literal_;
  ::xla::DeviceHandle* device_handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 replica_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferToInfeedResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferToInfeedResponse) */ {
 public:
  TransferToInfeedResponse();
  virtual ~TransferToInfeedResponse();

  TransferToInfeedResponse(const TransferToInfeedResponse& from);
  TransferToInfeedResponse(TransferToInfeedResponse&& from) noexcept
    : TransferToInfeedResponse() {
    *this = ::std::move(from);
  }

  inline TransferToInfeedResponse& operator=(const TransferToInfeedResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferToInfeedResponse& operator=(TransferToInfeedResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferToInfeedResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferToInfeedResponse* internal_default_instance() {
    return reinterpret_cast<const TransferToInfeedResponse*>(
               &_TransferToInfeedResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(TransferToInfeedResponse& a, TransferToInfeedResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferToInfeedResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferToInfeedResponse* New() const final {
    return CreateMaybeMessage<TransferToInfeedResponse>(nullptr);
  }

  TransferToInfeedResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferToInfeedResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferToInfeedResponse& from);
  void MergeFrom(const TransferToInfeedResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferToInfeedResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferToInfeedResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:xla.TransferToInfeedResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferFromOutfeedRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferFromOutfeedRequest) */ {
 public:
  TransferFromOutfeedRequest();
  virtual ~TransferFromOutfeedRequest();

  TransferFromOutfeedRequest(const TransferFromOutfeedRequest& from);
  TransferFromOutfeedRequest(TransferFromOutfeedRequest&& from) noexcept
    : TransferFromOutfeedRequest() {
    *this = ::std::move(from);
  }

  inline TransferFromOutfeedRequest& operator=(const TransferFromOutfeedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferFromOutfeedRequest& operator=(TransferFromOutfeedRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferFromOutfeedRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferFromOutfeedRequest* internal_default_instance() {
    return reinterpret_cast<const TransferFromOutfeedRequest*>(
               &_TransferFromOutfeedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(TransferFromOutfeedRequest& a, TransferFromOutfeedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferFromOutfeedRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferFromOutfeedRequest* New() const final {
    return CreateMaybeMessage<TransferFromOutfeedRequest>(nullptr);
  }

  TransferFromOutfeedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferFromOutfeedRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferFromOutfeedRequest& from);
  void MergeFrom(const TransferFromOutfeedRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferFromOutfeedRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferFromOutfeedRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeWithLayoutFieldNumber = 1,
    kDeviceHandleFieldNumber = 3,
    kReplicaIdFieldNumber = 2,
  };
  // .xla.ShapeProto shape_with_layout = 1;
  bool has_shape_with_layout() const;
  void clear_shape_with_layout();
  const ::xla::ShapeProto& shape_with_layout() const;
  ::xla::ShapeProto* release_shape_with_layout();
  ::xla::ShapeProto* mutable_shape_with_layout();
  void set_allocated_shape_with_layout(::xla::ShapeProto* shape_with_layout);

  // .xla.DeviceHandle device_handle = 3;
  bool has_device_handle() const;
  void clear_device_handle();
  const ::xla::DeviceHandle& device_handle() const;
  ::xla::DeviceHandle* release_device_handle();
  ::xla::DeviceHandle* mutable_device_handle();
  void set_allocated_device_handle(::xla::DeviceHandle* device_handle);

  // int64 replica_id = 2;
  void clear_replica_id();
  ::PROTOBUF_NAMESPACE_ID::int64 replica_id() const;
  void set_replica_id(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.TransferFromOutfeedRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::ShapeProto* shape_with_layout_;
  ::xla::DeviceHandle* device_handle_;
  ::PROTOBUF_NAMESPACE_ID::int64 replica_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class TransferFromOutfeedResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.TransferFromOutfeedResponse) */ {
 public:
  TransferFromOutfeedResponse();
  virtual ~TransferFromOutfeedResponse();

  TransferFromOutfeedResponse(const TransferFromOutfeedResponse& from);
  TransferFromOutfeedResponse(TransferFromOutfeedResponse&& from) noexcept
    : TransferFromOutfeedResponse() {
    *this = ::std::move(from);
  }

  inline TransferFromOutfeedResponse& operator=(const TransferFromOutfeedResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TransferFromOutfeedResponse& operator=(TransferFromOutfeedResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TransferFromOutfeedResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TransferFromOutfeedResponse* internal_default_instance() {
    return reinterpret_cast<const TransferFromOutfeedResponse*>(
               &_TransferFromOutfeedResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(TransferFromOutfeedResponse& a, TransferFromOutfeedResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TransferFromOutfeedResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TransferFromOutfeedResponse* New() const final {
    return CreateMaybeMessage<TransferFromOutfeedResponse>(nullptr);
  }

  TransferFromOutfeedResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TransferFromOutfeedResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TransferFromOutfeedResponse& from);
  void MergeFrom(const TransferFromOutfeedResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TransferFromOutfeedResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.TransferFromOutfeedResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiteralFieldNumber = 1,
  };
  // .xla.LiteralProto literal = 1;
  bool has_literal() const;
  void clear_literal();
  const ::xla::LiteralProto& literal() const;
  ::xla::LiteralProto* release_literal();
  ::xla::LiteralProto* mutable_literal();
  void set_allocated_literal(::xla::LiteralProto* literal);

  // @@protoc_insertion_point(class_scope:xla.TransferFromOutfeedResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::LiteralProto* literal_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ResetDeviceRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ResetDeviceRequest) */ {
 public:
  ResetDeviceRequest();
  virtual ~ResetDeviceRequest();

  ResetDeviceRequest(const ResetDeviceRequest& from);
  ResetDeviceRequest(ResetDeviceRequest&& from) noexcept
    : ResetDeviceRequest() {
    *this = ::std::move(from);
  }

  inline ResetDeviceRequest& operator=(const ResetDeviceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetDeviceRequest& operator=(ResetDeviceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ResetDeviceRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResetDeviceRequest* internal_default_instance() {
    return reinterpret_cast<const ResetDeviceRequest*>(
               &_ResetDeviceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(ResetDeviceRequest& a, ResetDeviceRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetDeviceRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ResetDeviceRequest* New() const final {
    return CreateMaybeMessage<ResetDeviceRequest>(nullptr);
  }

  ResetDeviceRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ResetDeviceRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ResetDeviceRequest& from);
  void MergeFrom(const ResetDeviceRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetDeviceRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ResetDeviceRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceHandleFieldNumber = 1,
  };
  // .xla.DeviceHandle device_handle = 1;
  bool has_device_handle() const;
  void clear_device_handle();
  const ::xla::DeviceHandle& device_handle() const;
  ::xla::DeviceHandle* release_device_handle();
  ::xla::DeviceHandle* mutable_device_handle();
  void set_allocated_device_handle(::xla::DeviceHandle* device_handle);

  // @@protoc_insertion_point(class_scope:xla.ResetDeviceRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::DeviceHandle* device_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ResetDeviceResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ResetDeviceResponse) */ {
 public:
  ResetDeviceResponse();
  virtual ~ResetDeviceResponse();

  ResetDeviceResponse(const ResetDeviceResponse& from);
  ResetDeviceResponse(ResetDeviceResponse&& from) noexcept
    : ResetDeviceResponse() {
    *this = ::std::move(from);
  }

  inline ResetDeviceResponse& operator=(const ResetDeviceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResetDeviceResponse& operator=(ResetDeviceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ResetDeviceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResetDeviceResponse* internal_default_instance() {
    return reinterpret_cast<const ResetDeviceResponse*>(
               &_ResetDeviceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(ResetDeviceResponse& a, ResetDeviceResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ResetDeviceResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ResetDeviceResponse* New() const final {
    return CreateMaybeMessage<ResetDeviceResponse>(nullptr);
  }

  ResetDeviceResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ResetDeviceResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ResetDeviceResponse& from);
  void MergeFrom(const ResetDeviceResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResetDeviceResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ResetDeviceResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:xla.ResetDeviceResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ComputationGraphStatsRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ComputationGraphStatsRequest) */ {
 public:
  ComputationGraphStatsRequest();
  virtual ~ComputationGraphStatsRequest();

  ComputationGraphStatsRequest(const ComputationGraphStatsRequest& from);
  ComputationGraphStatsRequest(ComputationGraphStatsRequest&& from) noexcept
    : ComputationGraphStatsRequest() {
    *this = ::std::move(from);
  }

  inline ComputationGraphStatsRequest& operator=(const ComputationGraphStatsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputationGraphStatsRequest& operator=(ComputationGraphStatsRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ComputationGraphStatsRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputationGraphStatsRequest* internal_default_instance() {
    return reinterpret_cast<const ComputationGraphStatsRequest*>(
               &_ComputationGraphStatsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(ComputationGraphStatsRequest& a, ComputationGraphStatsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputationGraphStatsRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComputationGraphStatsRequest* New() const final {
    return CreateMaybeMessage<ComputationGraphStatsRequest>(nullptr);
  }

  ComputationGraphStatsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComputationGraphStatsRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ComputationGraphStatsRequest& from);
  void MergeFrom(const ComputationGraphStatsRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputationGraphStatsRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ComputationGraphStatsRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComputationFieldNumber = 1,
    kDebugOptionsFieldNumber = 2,
  };
  // .xla.HloModuleProto computation = 1;
  bool has_computation() const;
  void clear_computation();
  const ::xla::HloModuleProto& computation() const;
  ::xla::HloModuleProto* release_computation();
  ::xla::HloModuleProto* mutable_computation();
  void set_allocated_computation(::xla::HloModuleProto* computation);

  // .xla.DebugOptions debug_options = 2;
  bool has_debug_options() const;
  void clear_debug_options();
  const ::xla::DebugOptions& debug_options() const;
  ::xla::DebugOptions* release_debug_options();
  ::xla::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::xla::DebugOptions* debug_options);

  // @@protoc_insertion_point(class_scope:xla.ComputationGraphStatsRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::HloModuleProto* computation_;
  ::xla::DebugOptions* debug_options_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ComputationStatsResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ComputationStatsResponse) */ {
 public:
  ComputationStatsResponse();
  virtual ~ComputationStatsResponse();

  ComputationStatsResponse(const ComputationStatsResponse& from);
  ComputationStatsResponse(ComputationStatsResponse&& from) noexcept
    : ComputationStatsResponse() {
    *this = ::std::move(from);
  }

  inline ComputationStatsResponse& operator=(const ComputationStatsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputationStatsResponse& operator=(ComputationStatsResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ComputationStatsResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputationStatsResponse* internal_default_instance() {
    return reinterpret_cast<const ComputationStatsResponse*>(
               &_ComputationStatsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(ComputationStatsResponse& a, ComputationStatsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputationStatsResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComputationStatsResponse* New() const final {
    return CreateMaybeMessage<ComputationStatsResponse>(nullptr);
  }

  ComputationStatsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComputationStatsResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ComputationStatsResponse& from);
  void MergeFrom(const ComputationStatsResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputationStatsResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ComputationStatsResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStatsFieldNumber = 1,
  };
  // .xla.ComputationStats stats = 1;
  bool has_stats() const;
  void clear_stats();
  const ::xla::ComputationStats& stats() const;
  ::xla::ComputationStats* release_stats();
  ::xla::ComputationStats* mutable_stats();
  void set_allocated_stats(::xla::ComputationStats* stats);

  // @@protoc_insertion_point(class_scope:xla.ComputationStatsResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::ComputationStats* stats_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class CreateChannelHandleRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CreateChannelHandleRequest) */ {
 public:
  CreateChannelHandleRequest();
  virtual ~CreateChannelHandleRequest();

  CreateChannelHandleRequest(const CreateChannelHandleRequest& from);
  CreateChannelHandleRequest(CreateChannelHandleRequest&& from) noexcept
    : CreateChannelHandleRequest() {
    *this = ::std::move(from);
  }

  inline CreateChannelHandleRequest& operator=(const CreateChannelHandleRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateChannelHandleRequest& operator=(CreateChannelHandleRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateChannelHandleRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateChannelHandleRequest* internal_default_instance() {
    return reinterpret_cast<const CreateChannelHandleRequest*>(
               &_CreateChannelHandleRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(CreateChannelHandleRequest& a, CreateChannelHandleRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateChannelHandleRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateChannelHandleRequest* New() const final {
    return CreateMaybeMessage<CreateChannelHandleRequest>(nullptr);
  }

  CreateChannelHandleRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateChannelHandleRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateChannelHandleRequest& from);
  void MergeFrom(const CreateChannelHandleRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateChannelHandleRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CreateChannelHandleRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChannelTypeFieldNumber = 1,
  };
  // .xla.ChannelHandle.ChannelType channel_type = 1;
  void clear_channel_type();
  ::xla::ChannelHandle_ChannelType channel_type() const;
  void set_channel_type(::xla::ChannelHandle_ChannelType value);

  // @@protoc_insertion_point(class_scope:xla.CreateChannelHandleRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  int channel_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class CreateChannelHandleResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CreateChannelHandleResponse) */ {
 public:
  CreateChannelHandleResponse();
  virtual ~CreateChannelHandleResponse();

  CreateChannelHandleResponse(const CreateChannelHandleResponse& from);
  CreateChannelHandleResponse(CreateChannelHandleResponse&& from) noexcept
    : CreateChannelHandleResponse() {
    *this = ::std::move(from);
  }

  inline CreateChannelHandleResponse& operator=(const CreateChannelHandleResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateChannelHandleResponse& operator=(CreateChannelHandleResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CreateChannelHandleResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CreateChannelHandleResponse* internal_default_instance() {
    return reinterpret_cast<const CreateChannelHandleResponse*>(
               &_CreateChannelHandleResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(CreateChannelHandleResponse& a, CreateChannelHandleResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateChannelHandleResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CreateChannelHandleResponse* New() const final {
    return CreateMaybeMessage<CreateChannelHandleResponse>(nullptr);
  }

  CreateChannelHandleResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CreateChannelHandleResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CreateChannelHandleResponse& from);
  void MergeFrom(const CreateChannelHandleResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateChannelHandleResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CreateChannelHandleResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChannelFieldNumber = 1,
  };
  // .xla.ChannelHandle channel = 1;
  bool has_channel() const;
  void clear_channel();
  const ::xla::ChannelHandle& channel() const;
  ::xla::ChannelHandle* release_channel();
  ::xla::ChannelHandle* mutable_channel();
  void set_allocated_channel(::xla::ChannelHandle* channel);

  // @@protoc_insertion_point(class_scope:xla.CreateChannelHandleResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::ChannelHandle* channel_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class UnregisterRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.UnregisterRequest) */ {
 public:
  UnregisterRequest();
  virtual ~UnregisterRequest();

  UnregisterRequest(const UnregisterRequest& from);
  UnregisterRequest(UnregisterRequest&& from) noexcept
    : UnregisterRequest() {
    *this = ::std::move(from);
  }

  inline UnregisterRequest& operator=(const UnregisterRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnregisterRequest& operator=(UnregisterRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UnregisterRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UnregisterRequest* internal_default_instance() {
    return reinterpret_cast<const UnregisterRequest*>(
               &_UnregisterRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(UnregisterRequest& a, UnregisterRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UnregisterRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UnregisterRequest* New() const final {
    return CreateMaybeMessage<UnregisterRequest>(nullptr);
  }

  UnregisterRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UnregisterRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UnregisterRequest& from);
  void MergeFrom(const UnregisterRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnregisterRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.UnregisterRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
  };
  // repeated .xla.GlobalDataHandle data = 1;
  int data_size() const;
  void clear_data();
  ::xla::GlobalDataHandle* mutable_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
      mutable_data();
  const ::xla::GlobalDataHandle& data(int index) const;
  ::xla::GlobalDataHandle* add_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
      data() const;

  // @@protoc_insertion_point(class_scope:xla.UnregisterRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle > data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class UnregisterResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.UnregisterResponse) */ {
 public:
  UnregisterResponse();
  virtual ~UnregisterResponse();

  UnregisterResponse(const UnregisterResponse& from);
  UnregisterResponse(UnregisterResponse&& from) noexcept
    : UnregisterResponse() {
    *this = ::std::move(from);
  }

  inline UnregisterResponse& operator=(const UnregisterResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnregisterResponse& operator=(UnregisterResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UnregisterResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UnregisterResponse* internal_default_instance() {
    return reinterpret_cast<const UnregisterResponse*>(
               &_UnregisterResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(UnregisterResponse& a, UnregisterResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(UnregisterResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UnregisterResponse* New() const final {
    return CreateMaybeMessage<UnregisterResponse>(nullptr);
  }

  UnregisterResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UnregisterResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UnregisterResponse& from);
  void MergeFrom(const UnregisterResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnregisterResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.UnregisterResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:xla.UnregisterResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class CompileRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CompileRequest) */ {
 public:
  CompileRequest();
  virtual ~CompileRequest();

  CompileRequest(const CompileRequest& from);
  CompileRequest(CompileRequest&& from) noexcept
    : CompileRequest() {
    *this = ::std::move(from);
  }

  inline CompileRequest& operator=(const CompileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompileRequest& operator=(CompileRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompileRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompileRequest* internal_default_instance() {
    return reinterpret_cast<const CompileRequest*>(
               &_CompileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(CompileRequest& a, CompileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CompileRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompileRequest* New() const final {
    return CreateMaybeMessage<CompileRequest>(nullptr);
  }

  CompileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompileRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompileRequest& from);
  void MergeFrom(const CompileRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompileRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CompileRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputShapeWithLayoutFieldNumber = 3,
    kComputationFieldNumber = 1,
    kExecutionOptionsFieldNumber = 2,
  };
  // repeated .xla.ShapeProto input_shape_with_layout = 3;
  int input_shape_with_layout_size() const;
  void clear_input_shape_with_layout();
  ::xla::ShapeProto* mutable_input_shape_with_layout(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_input_shape_with_layout();
  const ::xla::ShapeProto& input_shape_with_layout(int index) const;
  ::xla::ShapeProto* add_input_shape_with_layout();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
      input_shape_with_layout() const;

  // .xla.HloModuleProto computation = 1;
  bool has_computation() const;
  void clear_computation();
  const ::xla::HloModuleProto& computation() const;
  ::xla::HloModuleProto* release_computation();
  ::xla::HloModuleProto* mutable_computation();
  void set_allocated_computation(::xla::HloModuleProto* computation);

  // .xla.ExecutionOptions execution_options = 2;
  bool has_execution_options() const;
  void clear_execution_options();
  const ::xla::ExecutionOptions& execution_options() const;
  ::xla::ExecutionOptions* release_execution_options();
  ::xla::ExecutionOptions* mutable_execution_options();
  void set_allocated_execution_options(::xla::ExecutionOptions* execution_options);

  // @@protoc_insertion_point(class_scope:xla.CompileRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto > input_shape_with_layout_;
  ::xla::HloModuleProto* computation_;
  ::xla::ExecutionOptions* execution_options_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class CompileResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CompileResponse) */ {
 public:
  CompileResponse();
  virtual ~CompileResponse();

  CompileResponse(const CompileResponse& from);
  CompileResponse(CompileResponse&& from) noexcept
    : CompileResponse() {
    *this = ::std::move(from);
  }

  inline CompileResponse& operator=(const CompileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompileResponse& operator=(CompileResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CompileResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CompileResponse* internal_default_instance() {
    return reinterpret_cast<const CompileResponse*>(
               &_CompileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(CompileResponse& a, CompileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CompileResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CompileResponse* New() const final {
    return CreateMaybeMessage<CompileResponse>(nullptr);
  }

  CompileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CompileResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CompileResponse& from);
  void MergeFrom(const CompileResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompileResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CompileResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleFieldNumber = 1,
  };
  // .xla.ExecutionHandle handle = 1;
  bool has_handle() const;
  void clear_handle();
  const ::xla::ExecutionHandle& handle() const;
  ::xla::ExecutionHandle* release_handle();
  ::xla::ExecutionHandle* mutable_handle();
  void set_allocated_handle(::xla::ExecutionHandle* handle);

  // @@protoc_insertion_point(class_scope:xla.CompileResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::ExecutionHandle* handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ExecuteRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecuteRequest) */ {
 public:
  ExecuteRequest();
  virtual ~ExecuteRequest();

  ExecuteRequest(const ExecuteRequest& from);
  ExecuteRequest(ExecuteRequest&& from) noexcept
    : ExecuteRequest() {
    *this = ::std::move(from);
  }

  inline ExecuteRequest& operator=(const ExecuteRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecuteRequest& operator=(ExecuteRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecuteRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecuteRequest* internal_default_instance() {
    return reinterpret_cast<const ExecuteRequest*>(
               &_ExecuteRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(ExecuteRequest& a, ExecuteRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecuteRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecuteRequest* New() const final {
    return CreateMaybeMessage<ExecuteRequest>(nullptr);
  }

  ExecuteRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecuteRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecuteRequest& from);
  void MergeFrom(const ExecuteRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecuteRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecuteRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgumentsFieldNumber = 2,
    kHandleFieldNumber = 1,
  };
  // repeated .xla.GlobalDataHandle arguments = 2;
  int arguments_size() const;
  void clear_arguments();
  ::xla::GlobalDataHandle* mutable_arguments(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
      mutable_arguments();
  const ::xla::GlobalDataHandle& arguments(int index) const;
  ::xla::GlobalDataHandle* add_arguments();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
      arguments() const;

  // .xla.ExecutionHandle handle = 1;
  bool has_handle() const;
  void clear_handle();
  const ::xla::ExecutionHandle& handle() const;
  ::xla::ExecutionHandle* release_handle();
  ::xla::ExecutionHandle* mutable_handle();
  void set_allocated_handle(::xla::ExecutionHandle* handle);

  // @@protoc_insertion_point(class_scope:xla.ExecuteRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle > arguments_;
  ::xla::ExecutionHandle* handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ExecuteGraphRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecuteGraphRequest) */ {
 public:
  ExecuteGraphRequest();
  virtual ~ExecuteGraphRequest();

  ExecuteGraphRequest(const ExecuteGraphRequest& from);
  ExecuteGraphRequest(ExecuteGraphRequest&& from) noexcept
    : ExecuteGraphRequest() {
    *this = ::std::move(from);
  }

  inline ExecuteGraphRequest& operator=(const ExecuteGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecuteGraphRequest& operator=(ExecuteGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecuteGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecuteGraphRequest* internal_default_instance() {
    return reinterpret_cast<const ExecuteGraphRequest*>(
               &_ExecuteGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(ExecuteGraphRequest& a, ExecuteGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecuteGraphRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecuteGraphRequest* New() const final {
    return CreateMaybeMessage<ExecuteGraphRequest>(nullptr);
  }

  ExecuteGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecuteGraphRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecuteGraphRequest& from);
  void MergeFrom(const ExecuteGraphRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecuteGraphRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecuteGraphRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgumentsFieldNumber = 2,
    kComputationFieldNumber = 1,
    kExecutionOptionsFieldNumber = 3,
  };
  // repeated .xla.GlobalDataHandle arguments = 2;
  int arguments_size() const;
  void clear_arguments();
  ::xla::GlobalDataHandle* mutable_arguments(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
      mutable_arguments();
  const ::xla::GlobalDataHandle& arguments(int index) const;
  ::xla::GlobalDataHandle* add_arguments();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
      arguments() const;

  // .xla.HloModuleProto computation = 1;
  bool has_computation() const;
  void clear_computation();
  const ::xla::HloModuleProto& computation() const;
  ::xla::HloModuleProto* release_computation();
  ::xla::HloModuleProto* mutable_computation();
  void set_allocated_computation(::xla::HloModuleProto* computation);

  // .xla.ExecutionOptions execution_options = 3;
  bool has_execution_options() const;
  void clear_execution_options();
  const ::xla::ExecutionOptions& execution_options() const;
  ::xla::ExecutionOptions* release_execution_options();
  ::xla::ExecutionOptions* mutable_execution_options();
  void set_allocated_execution_options(::xla::ExecutionOptions* execution_options);

  // @@protoc_insertion_point(class_scope:xla.ExecuteGraphRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle > arguments_;
  ::xla::HloModuleProto* computation_;
  ::xla::ExecutionOptions* execution_options_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ExecuteGraphParallelRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecuteGraphParallelRequest) */ {
 public:
  ExecuteGraphParallelRequest();
  virtual ~ExecuteGraphParallelRequest();

  ExecuteGraphParallelRequest(const ExecuteGraphParallelRequest& from);
  ExecuteGraphParallelRequest(ExecuteGraphParallelRequest&& from) noexcept
    : ExecuteGraphParallelRequest() {
    *this = ::std::move(from);
  }

  inline ExecuteGraphParallelRequest& operator=(const ExecuteGraphParallelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecuteGraphParallelRequest& operator=(ExecuteGraphParallelRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecuteGraphParallelRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecuteGraphParallelRequest* internal_default_instance() {
    return reinterpret_cast<const ExecuteGraphParallelRequest*>(
               &_ExecuteGraphParallelRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(ExecuteGraphParallelRequest& a, ExecuteGraphParallelRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecuteGraphParallelRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecuteGraphParallelRequest* New() const final {
    return CreateMaybeMessage<ExecuteGraphParallelRequest>(nullptr);
  }

  ExecuteGraphParallelRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecuteGraphParallelRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecuteGraphParallelRequest& from);
  void MergeFrom(const ExecuteGraphParallelRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecuteGraphParallelRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecuteGraphParallelRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRequestsFieldNumber = 1,
  };
  // repeated .xla.ExecuteGraphRequest requests = 1;
  int requests_size() const;
  void clear_requests();
  ::xla::ExecuteGraphRequest* mutable_requests(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteGraphRequest >*
      mutable_requests();
  const ::xla::ExecuteGraphRequest& requests(int index) const;
  ::xla::ExecuteGraphRequest* add_requests();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteGraphRequest >&
      requests() const;

  // @@protoc_insertion_point(class_scope:xla.ExecuteGraphParallelRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteGraphRequest > requests_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ExecuteResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecuteResponse) */ {
 public:
  ExecuteResponse();
  virtual ~ExecuteResponse();

  ExecuteResponse(const ExecuteResponse& from);
  ExecuteResponse(ExecuteResponse&& from) noexcept
    : ExecuteResponse() {
    *this = ::std::move(from);
  }

  inline ExecuteResponse& operator=(const ExecuteResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecuteResponse& operator=(ExecuteResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecuteResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecuteResponse* internal_default_instance() {
    return reinterpret_cast<const ExecuteResponse*>(
               &_ExecuteResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(ExecuteResponse& a, ExecuteResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecuteResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecuteResponse* New() const final {
    return CreateMaybeMessage<ExecuteResponse>(nullptr);
  }

  ExecuteResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecuteResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecuteResponse& from);
  void MergeFrom(const ExecuteResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecuteResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecuteResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputFieldNumber = 1,
    kProfileFieldNumber = 2,
  };
  // .xla.GlobalDataHandle output = 1;
  bool has_output() const;
  void clear_output();
  const ::xla::GlobalDataHandle& output() const;
  ::xla::GlobalDataHandle* release_output();
  ::xla::GlobalDataHandle* mutable_output();
  void set_allocated_output(::xla::GlobalDataHandle* output);

  // .xla.ExecutionProfile profile = 2;
  bool has_profile() const;
  void clear_profile();
  const ::xla::ExecutionProfile& profile() const;
  ::xla::ExecutionProfile* release_profile();
  ::xla::ExecutionProfile* mutable_profile();
  void set_allocated_profile(::xla::ExecutionProfile* profile);

  // @@protoc_insertion_point(class_scope:xla.ExecuteResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* output_;
  ::xla::ExecutionProfile* profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ExecuteParallelResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecuteParallelResponse) */ {
 public:
  ExecuteParallelResponse();
  virtual ~ExecuteParallelResponse();

  ExecuteParallelResponse(const ExecuteParallelResponse& from);
  ExecuteParallelResponse(ExecuteParallelResponse&& from) noexcept
    : ExecuteParallelResponse() {
    *this = ::std::move(from);
  }

  inline ExecuteParallelResponse& operator=(const ExecuteParallelResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecuteParallelResponse& operator=(ExecuteParallelResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ExecuteParallelResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExecuteParallelResponse* internal_default_instance() {
    return reinterpret_cast<const ExecuteParallelResponse*>(
               &_ExecuteParallelResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(ExecuteParallelResponse& a, ExecuteParallelResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecuteParallelResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ExecuteParallelResponse* New() const final {
    return CreateMaybeMessage<ExecuteParallelResponse>(nullptr);
  }

  ExecuteParallelResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ExecuteParallelResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ExecuteParallelResponse& from);
  void MergeFrom(const ExecuteParallelResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecuteParallelResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecuteParallelResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResponsesFieldNumber = 1,
  };
  // repeated .xla.ExecuteResponse responses = 1;
  int responses_size() const;
  void clear_responses();
  ::xla::ExecuteResponse* mutable_responses(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteResponse >*
      mutable_responses();
  const ::xla::ExecuteResponse& responses(int index) const;
  ::xla::ExecuteResponse* add_responses();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteResponse >&
      responses() const;

  // @@protoc_insertion_point(class_scope:xla.ExecuteParallelResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteResponse > responses_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class WaitForExecutionRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.WaitForExecutionRequest) */ {
 public:
  WaitForExecutionRequest();
  virtual ~WaitForExecutionRequest();

  WaitForExecutionRequest(const WaitForExecutionRequest& from);
  WaitForExecutionRequest(WaitForExecutionRequest&& from) noexcept
    : WaitForExecutionRequest() {
    *this = ::std::move(from);
  }

  inline WaitForExecutionRequest& operator=(const WaitForExecutionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitForExecutionRequest& operator=(WaitForExecutionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WaitForExecutionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WaitForExecutionRequest* internal_default_instance() {
    return reinterpret_cast<const WaitForExecutionRequest*>(
               &_WaitForExecutionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(WaitForExecutionRequest& a, WaitForExecutionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitForExecutionRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WaitForExecutionRequest* New() const final {
    return CreateMaybeMessage<WaitForExecutionRequest>(nullptr);
  }

  WaitForExecutionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WaitForExecutionRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WaitForExecutionRequest& from);
  void MergeFrom(const WaitForExecutionRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitForExecutionRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.WaitForExecutionRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExecutionFieldNumber = 1,
  };
  // .xla.ExecutionHandle execution = 1;
  bool has_execution() const;
  void clear_execution();
  const ::xla::ExecutionHandle& execution() const;
  ::xla::ExecutionHandle* release_execution();
  ::xla::ExecutionHandle* mutable_execution();
  void set_allocated_execution(::xla::ExecutionHandle* execution);

  // @@protoc_insertion_point(class_scope:xla.WaitForExecutionRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::ExecutionHandle* execution_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class WaitForExecutionResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.WaitForExecutionResponse) */ {
 public:
  WaitForExecutionResponse();
  virtual ~WaitForExecutionResponse();

  WaitForExecutionResponse(const WaitForExecutionResponse& from);
  WaitForExecutionResponse(WaitForExecutionResponse&& from) noexcept
    : WaitForExecutionResponse() {
    *this = ::std::move(from);
  }

  inline WaitForExecutionResponse& operator=(const WaitForExecutionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitForExecutionResponse& operator=(WaitForExecutionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const WaitForExecutionResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const WaitForExecutionResponse* internal_default_instance() {
    return reinterpret_cast<const WaitForExecutionResponse*>(
               &_WaitForExecutionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(WaitForExecutionResponse& a, WaitForExecutionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitForExecutionResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline WaitForExecutionResponse* New() const final {
    return CreateMaybeMessage<WaitForExecutionResponse>(nullptr);
  }

  WaitForExecutionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<WaitForExecutionResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const WaitForExecutionResponse& from);
  void MergeFrom(const WaitForExecutionResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitForExecutionResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.WaitForExecutionResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputFieldNumber = 1,
    kProfileFieldNumber = 2,
  };
  // .xla.GlobalDataHandle output = 1;
  bool has_output() const;
  void clear_output();
  const ::xla::GlobalDataHandle& output() const;
  ::xla::GlobalDataHandle* release_output();
  ::xla::GlobalDataHandle* mutable_output();
  void set_allocated_output(::xla::GlobalDataHandle* output);

  // .xla.ExecutionProfile profile = 2;
  bool has_profile() const;
  void clear_profile();
  const ::xla::ExecutionProfile& profile() const;
  ::xla::ExecutionProfile* release_profile();
  ::xla::ExecutionProfile* mutable_profile();
  void set_allocated_profile(::xla::ExecutionProfile* profile);

  // @@protoc_insertion_point(class_scope:xla.WaitForExecutionResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* output_;
  ::xla::ExecutionProfile* profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ComputeConstantGraphRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ComputeConstantGraphRequest) */ {
 public:
  ComputeConstantGraphRequest();
  virtual ~ComputeConstantGraphRequest();

  ComputeConstantGraphRequest(const ComputeConstantGraphRequest& from);
  ComputeConstantGraphRequest(ComputeConstantGraphRequest&& from) noexcept
    : ComputeConstantGraphRequest() {
    *this = ::std::move(from);
  }

  inline ComputeConstantGraphRequest& operator=(const ComputeConstantGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputeConstantGraphRequest& operator=(ComputeConstantGraphRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ComputeConstantGraphRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputeConstantGraphRequest* internal_default_instance() {
    return reinterpret_cast<const ComputeConstantGraphRequest*>(
               &_ComputeConstantGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(ComputeConstantGraphRequest& a, ComputeConstantGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputeConstantGraphRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComputeConstantGraphRequest* New() const final {
    return CreateMaybeMessage<ComputeConstantGraphRequest>(nullptr);
  }

  ComputeConstantGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComputeConstantGraphRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ComputeConstantGraphRequest& from);
  void MergeFrom(const ComputeConstantGraphRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputeConstantGraphRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ComputeConstantGraphRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComputationFieldNumber = 1,
    kOutputLayoutFieldNumber = 2,
  };
  // .xla.HloModuleProto computation = 1;
  bool has_computation() const;
  void clear_computation();
  const ::xla::HloModuleProto& computation() const;
  ::xla::HloModuleProto* release_computation();
  ::xla::HloModuleProto* mutable_computation();
  void set_allocated_computation(::xla::HloModuleProto* computation);

  // .xla.LayoutProto output_layout = 2;
  bool has_output_layout() const;
  void clear_output_layout();
  const ::xla::LayoutProto& output_layout() const;
  ::xla::LayoutProto* release_output_layout();
  ::xla::LayoutProto* mutable_output_layout();
  void set_allocated_output_layout(::xla::LayoutProto* output_layout);

  // @@protoc_insertion_point(class_scope:xla.ComputeConstantGraphRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::HloModuleProto* computation_;
  ::xla::LayoutProto* output_layout_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class ComputeConstantResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ComputeConstantResponse) */ {
 public:
  ComputeConstantResponse();
  virtual ~ComputeConstantResponse();

  ComputeConstantResponse(const ComputeConstantResponse& from);
  ComputeConstantResponse(ComputeConstantResponse&& from) noexcept
    : ComputeConstantResponse() {
    *this = ::std::move(from);
  }

  inline ComputeConstantResponse& operator=(const ComputeConstantResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputeConstantResponse& operator=(ComputeConstantResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ComputeConstantResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComputeConstantResponse* internal_default_instance() {
    return reinterpret_cast<const ComputeConstantResponse*>(
               &_ComputeConstantResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(ComputeConstantResponse& a, ComputeConstantResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputeConstantResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ComputeConstantResponse* New() const final {
    return CreateMaybeMessage<ComputeConstantResponse>(nullptr);
  }

  ComputeConstantResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ComputeConstantResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ComputeConstantResponse& from);
  void MergeFrom(const ComputeConstantResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputeConstantResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ComputeConstantResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLiteralFieldNumber = 1,
  };
  // .xla.LiteralProto literal = 1;
  bool has_literal() const;
  void clear_literal();
  const ::xla::LiteralProto& literal() const;
  ::xla::LiteralProto* release_literal();
  ::xla::LiteralProto* mutable_literal();
  void set_allocated_literal(::xla::LiteralProto* literal);

  // @@protoc_insertion_point(class_scope:xla.ComputeConstantResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::LiteralProto* literal_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class DeconstructTupleRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeconstructTupleRequest) */ {
 public:
  DeconstructTupleRequest();
  virtual ~DeconstructTupleRequest();

  DeconstructTupleRequest(const DeconstructTupleRequest& from);
  DeconstructTupleRequest(DeconstructTupleRequest&& from) noexcept
    : DeconstructTupleRequest() {
    *this = ::std::move(from);
  }

  inline DeconstructTupleRequest& operator=(const DeconstructTupleRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeconstructTupleRequest& operator=(DeconstructTupleRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeconstructTupleRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeconstructTupleRequest* internal_default_instance() {
    return reinterpret_cast<const DeconstructTupleRequest*>(
               &_DeconstructTupleRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(DeconstructTupleRequest& a, DeconstructTupleRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeconstructTupleRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeconstructTupleRequest* New() const final {
    return CreateMaybeMessage<DeconstructTupleRequest>(nullptr);
  }

  DeconstructTupleRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeconstructTupleRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeconstructTupleRequest& from);
  void MergeFrom(const DeconstructTupleRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeconstructTupleRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeconstructTupleRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTupleHandleFieldNumber = 2,
  };
  // .xla.GlobalDataHandle tuple_handle = 2;
  bool has_tuple_handle() const;
  void clear_tuple_handle();
  const ::xla::GlobalDataHandle& tuple_handle() const;
  ::xla::GlobalDataHandle* release_tuple_handle();
  ::xla::GlobalDataHandle* mutable_tuple_handle();
  void set_allocated_tuple_handle(::xla::GlobalDataHandle* tuple_handle);

  // @@protoc_insertion_point(class_scope:xla.DeconstructTupleRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* tuple_handle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class DeconstructTupleResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.DeconstructTupleResponse) */ {
 public:
  DeconstructTupleResponse();
  virtual ~DeconstructTupleResponse();

  DeconstructTupleResponse(const DeconstructTupleResponse& from);
  DeconstructTupleResponse(DeconstructTupleResponse&& from) noexcept
    : DeconstructTupleResponse() {
    *this = ::std::move(from);
  }

  inline DeconstructTupleResponse& operator=(const DeconstructTupleResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeconstructTupleResponse& operator=(DeconstructTupleResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const DeconstructTupleResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DeconstructTupleResponse* internal_default_instance() {
    return reinterpret_cast<const DeconstructTupleResponse*>(
               &_DeconstructTupleResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  friend void swap(DeconstructTupleResponse& a, DeconstructTupleResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeconstructTupleResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline DeconstructTupleResponse* New() const final {
    return CreateMaybeMessage<DeconstructTupleResponse>(nullptr);
  }

  DeconstructTupleResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<DeconstructTupleResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const DeconstructTupleResponse& from);
  void MergeFrom(const DeconstructTupleResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeconstructTupleResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.DeconstructTupleResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kElementHandlesFieldNumber = 1,
  };
  // repeated .xla.GlobalDataHandle element_handles = 1;
  int element_handles_size() const;
  void clear_element_handles();
  ::xla::GlobalDataHandle* mutable_element_handles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
      mutable_element_handles();
  const ::xla::GlobalDataHandle& element_handles(int index) const;
  ::xla::GlobalDataHandle* add_element_handles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
      element_handles() const;

  // @@protoc_insertion_point(class_scope:xla.DeconstructTupleResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle > element_handles_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class LoadDataRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LoadDataRequest) */ {
 public:
  LoadDataRequest();
  virtual ~LoadDataRequest();

  LoadDataRequest(const LoadDataRequest& from);
  LoadDataRequest(LoadDataRequest&& from) noexcept
    : LoadDataRequest() {
    *this = ::std::move(from);
  }

  inline LoadDataRequest& operator=(const LoadDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadDataRequest& operator=(LoadDataRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LoadDataRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoadDataRequest* internal_default_instance() {
    return reinterpret_cast<const LoadDataRequest*>(
               &_LoadDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    34;

  friend void swap(LoadDataRequest& a, LoadDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadDataRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LoadDataRequest* New() const final {
    return CreateMaybeMessage<LoadDataRequest>(nullptr);
  }

  LoadDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LoadDataRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LoadDataRequest& from);
  void MergeFrom(const LoadDataRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadDataRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LoadDataRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kColumnioTabletPathFieldNumber = 1,
    kColumnioFieldFieldNumber = 2,
    kElementShapeFieldNumber = 3,
    kOffsetFieldNumber = 4,
    kLimitFieldNumber = 5,
    kZipFieldNumber = 6,
  };
  // string columnio_tablet_path = 1;
  void clear_columnio_tablet_path();
  const std::string& columnio_tablet_path() const;
  void set_columnio_tablet_path(const std::string& value);
  void set_columnio_tablet_path(std::string&& value);
  void set_columnio_tablet_path(const char* value);
  void set_columnio_tablet_path(const char* value, size_t size);
  std::string* mutable_columnio_tablet_path();
  std::string* release_columnio_tablet_path();
  void set_allocated_columnio_tablet_path(std::string* columnio_tablet_path);

  // string columnio_field = 2;
  void clear_columnio_field();
  const std::string& columnio_field() const;
  void set_columnio_field(const std::string& value);
  void set_columnio_field(std::string&& value);
  void set_columnio_field(const char* value);
  void set_columnio_field(const char* value, size_t size);
  std::string* mutable_columnio_field();
  std::string* release_columnio_field();
  void set_allocated_columnio_field(std::string* columnio_field);

  // .xla.ShapeProto element_shape = 3;
  bool has_element_shape() const;
  void clear_element_shape();
  const ::xla::ShapeProto& element_shape() const;
  ::xla::ShapeProto* release_element_shape();
  ::xla::ShapeProto* mutable_element_shape();
  void set_allocated_element_shape(::xla::ShapeProto* element_shape);

  // int64 offset = 4;
  void clear_offset();
  ::PROTOBUF_NAMESPACE_ID::int64 offset() const;
  void set_offset(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 limit = 5;
  void clear_limit();
  ::PROTOBUF_NAMESPACE_ID::int64 limit() const;
  void set_limit(::PROTOBUF_NAMESPACE_ID::int64 value);

  // bool zip = 6;
  void clear_zip();
  bool zip() const;
  void set_zip(bool value);

  // @@protoc_insertion_point(class_scope:xla.LoadDataRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr columnio_tablet_path_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr columnio_field_;
  ::xla::ShapeProto* element_shape_;
  ::PROTOBUF_NAMESPACE_ID::int64 offset_;
  ::PROTOBUF_NAMESPACE_ID::int64 limit_;
  bool zip_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class LoadDataResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.LoadDataResponse) */ {
 public:
  LoadDataResponse();
  virtual ~LoadDataResponse();

  LoadDataResponse(const LoadDataResponse& from);
  LoadDataResponse(LoadDataResponse&& from) noexcept
    : LoadDataResponse() {
    *this = ::std::move(from);
  }

  inline LoadDataResponse& operator=(const LoadDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadDataResponse& operator=(LoadDataResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LoadDataResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoadDataResponse* internal_default_instance() {
    return reinterpret_cast<const LoadDataResponse*>(
               &_LoadDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    35;

  friend void swap(LoadDataResponse& a, LoadDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadDataResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LoadDataResponse* New() const final {
    return CreateMaybeMessage<LoadDataResponse>(nullptr);
  }

  LoadDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LoadDataResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LoadDataResponse& from);
  void MergeFrom(const LoadDataResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadDataResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.LoadDataResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kDataShapeFieldNumber = 2,
    kAvailableRowsFieldNumber = 3,
    kRowsLoadedFieldNumber = 4,
    kNanosecondsFieldNumber = 5,
  };
  // .xla.GlobalDataHandle data = 1;
  bool has_data() const;
  void clear_data();
  const ::xla::GlobalDataHandle& data() const;
  ::xla::GlobalDataHandle* release_data();
  ::xla::GlobalDataHandle* mutable_data();
  void set_allocated_data(::xla::GlobalDataHandle* data);

  // .xla.ShapeProto data_shape = 2;
  bool has_data_shape() const;
  void clear_data_shape();
  const ::xla::ShapeProto& data_shape() const;
  ::xla::ShapeProto* release_data_shape();
  ::xla::ShapeProto* mutable_data_shape();
  void set_allocated_data_shape(::xla::ShapeProto* data_shape);

  // int64 available_rows = 3;
  void clear_available_rows();
  ::PROTOBUF_NAMESPACE_ID::int64 available_rows() const;
  void set_available_rows(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 rows_loaded = 4;
  void clear_rows_loaded();
  ::PROTOBUF_NAMESPACE_ID::int64 rows_loaded() const;
  void set_rows_loaded(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 nanoseconds = 5;
  void clear_nanoseconds();
  ::PROTOBUF_NAMESPACE_ID::int64 nanoseconds() const;
  void set_nanoseconds(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:xla.LoadDataResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* data_;
  ::xla::ShapeProto* data_shape_;
  ::PROTOBUF_NAMESPACE_ID::int64 available_rows_;
  ::PROTOBUF_NAMESPACE_ID::int64 rows_loaded_;
  ::PROTOBUF_NAMESPACE_ID::int64 nanoseconds_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class GetShapeRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GetShapeRequest) */ {
 public:
  GetShapeRequest();
  virtual ~GetShapeRequest();

  GetShapeRequest(const GetShapeRequest& from);
  GetShapeRequest(GetShapeRequest&& from) noexcept
    : GetShapeRequest() {
    *this = ::std::move(from);
  }

  inline GetShapeRequest& operator=(const GetShapeRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetShapeRequest& operator=(GetShapeRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetShapeRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetShapeRequest* internal_default_instance() {
    return reinterpret_cast<const GetShapeRequest*>(
               &_GetShapeRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    36;

  friend void swap(GetShapeRequest& a, GetShapeRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetShapeRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetShapeRequest* New() const final {
    return CreateMaybeMessage<GetShapeRequest>(nullptr);
  }

  GetShapeRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetShapeRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetShapeRequest& from);
  void MergeFrom(const GetShapeRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetShapeRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GetShapeRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
  };
  // .xla.GlobalDataHandle data = 1;
  bool has_data() const;
  void clear_data();
  const ::xla::GlobalDataHandle& data() const;
  ::xla::GlobalDataHandle* release_data();
  ::xla::GlobalDataHandle* mutable_data();
  void set_allocated_data(::xla::GlobalDataHandle* data);

  // @@protoc_insertion_point(class_scope:xla.GetShapeRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class GetShapeResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GetShapeResponse) */ {
 public:
  GetShapeResponse();
  virtual ~GetShapeResponse();

  GetShapeResponse(const GetShapeResponse& from);
  GetShapeResponse(GetShapeResponse&& from) noexcept
    : GetShapeResponse() {
    *this = ::std::move(from);
  }

  inline GetShapeResponse& operator=(const GetShapeResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetShapeResponse& operator=(GetShapeResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GetShapeResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetShapeResponse* internal_default_instance() {
    return reinterpret_cast<const GetShapeResponse*>(
               &_GetShapeResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    37;

  friend void swap(GetShapeResponse& a, GetShapeResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetShapeResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GetShapeResponse* New() const final {
    return CreateMaybeMessage<GetShapeResponse>(nullptr);
  }

  GetShapeResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GetShapeResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GetShapeResponse& from);
  void MergeFrom(const GetShapeResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetShapeResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GetShapeResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
  };
  // .xla.ShapeProto shape = 1;
  bool has_shape() const;
  void clear_shape();
  const ::xla::ShapeProto& shape() const;
  ::xla::ShapeProto* release_shape();
  ::xla::ShapeProto* mutable_shape();
  void set_allocated_shape(::xla::ShapeProto* shape);

  // @@protoc_insertion_point(class_scope:xla.GetShapeResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::ShapeProto* shape_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class UnpackRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.UnpackRequest) */ {
 public:
  UnpackRequest();
  virtual ~UnpackRequest();

  UnpackRequest(const UnpackRequest& from);
  UnpackRequest(UnpackRequest&& from) noexcept
    : UnpackRequest() {
    *this = ::std::move(from);
  }

  inline UnpackRequest& operator=(const UnpackRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnpackRequest& operator=(UnpackRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UnpackRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UnpackRequest* internal_default_instance() {
    return reinterpret_cast<const UnpackRequest*>(
               &_UnpackRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    38;

  friend void swap(UnpackRequest& a, UnpackRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UnpackRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UnpackRequest* New() const final {
    return CreateMaybeMessage<UnpackRequest>(nullptr);
  }

  UnpackRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UnpackRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UnpackRequest& from);
  void MergeFrom(const UnpackRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnpackRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.UnpackRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
  };
  // .xla.GlobalDataHandle data = 1;
  bool has_data() const;
  void clear_data();
  const ::xla::GlobalDataHandle& data() const;
  ::xla::GlobalDataHandle* release_data();
  ::xla::GlobalDataHandle* mutable_data();
  void set_allocated_data(::xla::GlobalDataHandle* data);

  // @@protoc_insertion_point(class_scope:xla.UnpackRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::xla::GlobalDataHandle* data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// -------------------------------------------------------------------

class UnpackResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.UnpackResponse) */ {
 public:
  UnpackResponse();
  virtual ~UnpackResponse();

  UnpackResponse(const UnpackResponse& from);
  UnpackResponse(UnpackResponse&& from) noexcept
    : UnpackResponse() {
    *this = ::std::move(from);
  }

  inline UnpackResponse& operator=(const UnpackResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline UnpackResponse& operator=(UnpackResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const UnpackResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const UnpackResponse* internal_default_instance() {
    return reinterpret_cast<const UnpackResponse*>(
               &_UnpackResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    39;

  friend void swap(UnpackResponse& a, UnpackResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(UnpackResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline UnpackResponse* New() const final {
    return CreateMaybeMessage<UnpackResponse>(nullptr);
  }

  UnpackResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<UnpackResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const UnpackResponse& from);
  void MergeFrom(const UnpackResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UnpackResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.UnpackResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto);
    return ::descriptor_table_tensorflow_2fcompiler_2fxla_2fxla_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTiedDataFieldNumber = 1,
  };
  // repeated .xla.GlobalDataHandle tied_data = 1;
  int tied_data_size() const;
  void clear_tied_data();
  ::xla::GlobalDataHandle* mutable_tied_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
      mutable_tied_data();
  const ::xla::GlobalDataHandle& tied_data(int index) const;
  ::xla::GlobalDataHandle* add_tied_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
      tied_data() const;

  // @@protoc_insertion_point(class_scope:xla.UnpackResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle > tied_data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fxla_2fxla_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// DebugOptions

// bool xla_hlo_graph_addresses = 2;
inline void DebugOptions::clear_xla_hlo_graph_addresses() {
  xla_hlo_graph_addresses_ = false;
}
inline bool DebugOptions::xla_hlo_graph_addresses() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_hlo_graph_addresses)
  return xla_hlo_graph_addresses_;
}
inline void DebugOptions::set_xla_hlo_graph_addresses(bool value) {
  
  xla_hlo_graph_addresses_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_hlo_graph_addresses)
}

// bool xla_hlo_profile = 9;
inline void DebugOptions::clear_xla_hlo_profile() {
  xla_hlo_profile_ = false;
}
inline bool DebugOptions::xla_hlo_profile() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_hlo_profile)
  return xla_hlo_profile_;
}
inline void DebugOptions::set_xla_hlo_profile(bool value) {
  
  xla_hlo_profile_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_hlo_profile)
}

// repeated string xla_disable_hlo_passes = 30;
inline int DebugOptions::xla_disable_hlo_passes_size() const {
  return xla_disable_hlo_passes_.size();
}
inline void DebugOptions::clear_xla_disable_hlo_passes() {
  xla_disable_hlo_passes_.Clear();
}
inline const std::string& DebugOptions::xla_disable_hlo_passes(int index) const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_disable_hlo_passes)
  return xla_disable_hlo_passes_.Get(index);
}
inline std::string* DebugOptions::mutable_xla_disable_hlo_passes(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_disable_hlo_passes)
  return xla_disable_hlo_passes_.Mutable(index);
}
inline void DebugOptions::set_xla_disable_hlo_passes(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_disable_hlo_passes)
  xla_disable_hlo_passes_.Mutable(index)->assign(value);
}
inline void DebugOptions::set_xla_disable_hlo_passes(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_disable_hlo_passes)
  xla_disable_hlo_passes_.Mutable(index)->assign(std::move(value));
}
inline void DebugOptions::set_xla_disable_hlo_passes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_disable_hlo_passes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_disable_hlo_passes)
}
inline void DebugOptions::set_xla_disable_hlo_passes(int index, const char* value, size_t size) {
  xla_disable_hlo_passes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_disable_hlo_passes)
}
inline std::string* DebugOptions::add_xla_disable_hlo_passes() {
  // @@protoc_insertion_point(field_add_mutable:xla.DebugOptions.xla_disable_hlo_passes)
  return xla_disable_hlo_passes_.Add();
}
inline void DebugOptions::add_xla_disable_hlo_passes(const std::string& value) {
  xla_disable_hlo_passes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_disable_hlo_passes)
}
inline void DebugOptions::add_xla_disable_hlo_passes(std::string&& value) {
  xla_disable_hlo_passes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_disable_hlo_passes)
}
inline void DebugOptions::add_xla_disable_hlo_passes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_disable_hlo_passes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.DebugOptions.xla_disable_hlo_passes)
}
inline void DebugOptions::add_xla_disable_hlo_passes(const char* value, size_t size) {
  xla_disable_hlo_passes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.DebugOptions.xla_disable_hlo_passes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugOptions::xla_disable_hlo_passes() const {
  // @@protoc_insertion_point(field_list:xla.DebugOptions.xla_disable_hlo_passes)
  return xla_disable_hlo_passes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugOptions::mutable_xla_disable_hlo_passes() {
  // @@protoc_insertion_point(field_mutable_list:xla.DebugOptions.xla_disable_hlo_passes)
  return &xla_disable_hlo_passes_;
}

// repeated string xla_enable_hlo_passes_only = 124;
inline int DebugOptions::xla_enable_hlo_passes_only_size() const {
  return xla_enable_hlo_passes_only_.size();
}
inline void DebugOptions::clear_xla_enable_hlo_passes_only() {
  xla_enable_hlo_passes_only_.Clear();
}
inline const std::string& DebugOptions::xla_enable_hlo_passes_only(int index) const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_enable_hlo_passes_only)
  return xla_enable_hlo_passes_only_.Get(index);
}
inline std::string* DebugOptions::mutable_xla_enable_hlo_passes_only(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_enable_hlo_passes_only)
  return xla_enable_hlo_passes_only_.Mutable(index);
}
inline void DebugOptions::set_xla_enable_hlo_passes_only(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_enable_hlo_passes_only)
  xla_enable_hlo_passes_only_.Mutable(index)->assign(value);
}
inline void DebugOptions::set_xla_enable_hlo_passes_only(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_enable_hlo_passes_only)
  xla_enable_hlo_passes_only_.Mutable(index)->assign(std::move(value));
}
inline void DebugOptions::set_xla_enable_hlo_passes_only(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_enable_hlo_passes_only_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_enable_hlo_passes_only)
}
inline void DebugOptions::set_xla_enable_hlo_passes_only(int index, const char* value, size_t size) {
  xla_enable_hlo_passes_only_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_enable_hlo_passes_only)
}
inline std::string* DebugOptions::add_xla_enable_hlo_passes_only() {
  // @@protoc_insertion_point(field_add_mutable:xla.DebugOptions.xla_enable_hlo_passes_only)
  return xla_enable_hlo_passes_only_.Add();
}
inline void DebugOptions::add_xla_enable_hlo_passes_only(const std::string& value) {
  xla_enable_hlo_passes_only_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_enable_hlo_passes_only)
}
inline void DebugOptions::add_xla_enable_hlo_passes_only(std::string&& value) {
  xla_enable_hlo_passes_only_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_enable_hlo_passes_only)
}
inline void DebugOptions::add_xla_enable_hlo_passes_only(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_enable_hlo_passes_only_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.DebugOptions.xla_enable_hlo_passes_only)
}
inline void DebugOptions::add_xla_enable_hlo_passes_only(const char* value, size_t size) {
  xla_enable_hlo_passes_only_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.DebugOptions.xla_enable_hlo_passes_only)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugOptions::xla_enable_hlo_passes_only() const {
  // @@protoc_insertion_point(field_list:xla.DebugOptions.xla_enable_hlo_passes_only)
  return xla_enable_hlo_passes_only_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugOptions::mutable_xla_enable_hlo_passes_only() {
  // @@protoc_insertion_point(field_mutable_list:xla.DebugOptions.xla_enable_hlo_passes_only)
  return &xla_enable_hlo_passes_only_;
}

// bool xla_disable_all_hlo_passes = 104;
inline void DebugOptions::clear_xla_disable_all_hlo_passes() {
  xla_disable_all_hlo_passes_ = false;
}
inline bool DebugOptions::xla_disable_all_hlo_passes() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_disable_all_hlo_passes)
  return xla_disable_all_hlo_passes_;
}
inline void DebugOptions::set_xla_disable_all_hlo_passes(bool value) {
  
  xla_disable_all_hlo_passes_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_disable_all_hlo_passes)
}

// int32 xla_backend_optimization_level = 31;
inline void DebugOptions::clear_xla_backend_optimization_level() {
  xla_backend_optimization_level_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_backend_optimization_level() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_backend_optimization_level)
  return xla_backend_optimization_level_;
}
inline void DebugOptions::set_xla_backend_optimization_level(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_backend_optimization_level_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_backend_optimization_level)
}

// bool xla_embed_ir_in_executable = 33;
inline void DebugOptions::clear_xla_embed_ir_in_executable() {
  xla_embed_ir_in_executable_ = false;
}
inline bool DebugOptions::xla_embed_ir_in_executable() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_embed_ir_in_executable)
  return xla_embed_ir_in_executable_;
}
inline void DebugOptions::set_xla_embed_ir_in_executable(bool value) {
  
  xla_embed_ir_in_executable_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_embed_ir_in_executable)
}

// bool xla_eliminate_hlo_implicit_broadcast = 35;
inline void DebugOptions::clear_xla_eliminate_hlo_implicit_broadcast() {
  xla_eliminate_hlo_implicit_broadcast_ = false;
}
inline bool DebugOptions::xla_eliminate_hlo_implicit_broadcast() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_eliminate_hlo_implicit_broadcast)
  return xla_eliminate_hlo_implicit_broadcast_;
}
inline void DebugOptions::set_xla_eliminate_hlo_implicit_broadcast(bool value) {
  
  xla_eliminate_hlo_implicit_broadcast_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_eliminate_hlo_implicit_broadcast)
}

// bool xla_cpu_multi_thread_eigen = 60;
inline void DebugOptions::clear_xla_cpu_multi_thread_eigen() {
  xla_cpu_multi_thread_eigen_ = false;
}
inline bool DebugOptions::xla_cpu_multi_thread_eigen() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_multi_thread_eigen)
  return xla_cpu_multi_thread_eigen_;
}
inline void DebugOptions::set_xla_cpu_multi_thread_eigen(bool value) {
  
  xla_cpu_multi_thread_eigen_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_multi_thread_eigen)
}

// string xla_gpu_cuda_data_dir = 61;
inline void DebugOptions::clear_xla_gpu_cuda_data_dir() {
  xla_gpu_cuda_data_dir_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebugOptions::xla_gpu_cuda_data_dir() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_cuda_data_dir)
  return xla_gpu_cuda_data_dir_.GetNoArena();
}
inline void DebugOptions::set_xla_gpu_cuda_data_dir(const std::string& value) {
  
  xla_gpu_cuda_data_dir_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_cuda_data_dir)
}
inline void DebugOptions::set_xla_gpu_cuda_data_dir(std::string&& value) {
  
  xla_gpu_cuda_data_dir_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.DebugOptions.xla_gpu_cuda_data_dir)
}
inline void DebugOptions::set_xla_gpu_cuda_data_dir(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  xla_gpu_cuda_data_dir_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_gpu_cuda_data_dir)
}
inline void DebugOptions::set_xla_gpu_cuda_data_dir(const char* value, size_t size) {
  
  xla_gpu_cuda_data_dir_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_gpu_cuda_data_dir)
}
inline std::string* DebugOptions::mutable_xla_gpu_cuda_data_dir() {
  
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_gpu_cuda_data_dir)
  return xla_gpu_cuda_data_dir_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebugOptions::release_xla_gpu_cuda_data_dir() {
  // @@protoc_insertion_point(field_release:xla.DebugOptions.xla_gpu_cuda_data_dir)
  
  return xla_gpu_cuda_data_dir_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebugOptions::set_allocated_xla_gpu_cuda_data_dir(std::string* xla_gpu_cuda_data_dir) {
  if (xla_gpu_cuda_data_dir != nullptr) {
    
  } else {
    
  }
  xla_gpu_cuda_data_dir_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), xla_gpu_cuda_data_dir);
  // @@protoc_insertion_point(field_set_allocated:xla.DebugOptions.xla_gpu_cuda_data_dir)
}

// bool xla_gpu_ftz = 62;
inline void DebugOptions::clear_xla_gpu_ftz() {
  xla_gpu_ftz_ = false;
}
inline bool DebugOptions::xla_gpu_ftz() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_ftz)
  return xla_gpu_ftz_;
}
inline void DebugOptions::set_xla_gpu_ftz(bool value) {
  
  xla_gpu_ftz_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_ftz)
}

// bool xla_gpu_disable_multi_streaming = 63;
inline void DebugOptions::clear_xla_gpu_disable_multi_streaming() {
  xla_gpu_disable_multi_streaming_ = false;
}
inline bool DebugOptions::xla_gpu_disable_multi_streaming() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_disable_multi_streaming)
  return xla_gpu_disable_multi_streaming_;
}
inline void DebugOptions::set_xla_gpu_disable_multi_streaming(bool value) {
  
  xla_gpu_disable_multi_streaming_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_disable_multi_streaming)
}

// bool xla_gpu_use_random_streams = 134;
inline void DebugOptions::clear_xla_gpu_use_random_streams() {
  xla_gpu_use_random_streams_ = false;
}
inline bool DebugOptions::xla_gpu_use_random_streams() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_use_random_streams)
  return xla_gpu_use_random_streams_;
}
inline void DebugOptions::set_xla_gpu_use_random_streams(bool value) {
  
  xla_gpu_use_random_streams_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_use_random_streams)
}

// bool xla_llvm_enable_alias_scope_metadata = 70;
inline void DebugOptions::clear_xla_llvm_enable_alias_scope_metadata() {
  xla_llvm_enable_alias_scope_metadata_ = false;
}
inline bool DebugOptions::xla_llvm_enable_alias_scope_metadata() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_llvm_enable_alias_scope_metadata)
  return xla_llvm_enable_alias_scope_metadata_;
}
inline void DebugOptions::set_xla_llvm_enable_alias_scope_metadata(bool value) {
  
  xla_llvm_enable_alias_scope_metadata_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_llvm_enable_alias_scope_metadata)
}

// bool xla_llvm_enable_noalias_metadata = 71;
inline void DebugOptions::clear_xla_llvm_enable_noalias_metadata() {
  xla_llvm_enable_noalias_metadata_ = false;
}
inline bool DebugOptions::xla_llvm_enable_noalias_metadata() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_llvm_enable_noalias_metadata)
  return xla_llvm_enable_noalias_metadata_;
}
inline void DebugOptions::set_xla_llvm_enable_noalias_metadata(bool value) {
  
  xla_llvm_enable_noalias_metadata_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_llvm_enable_noalias_metadata)
}

// bool xla_llvm_enable_invariant_load_metadata = 72;
inline void DebugOptions::clear_xla_llvm_enable_invariant_load_metadata() {
  xla_llvm_enable_invariant_load_metadata_ = false;
}
inline bool DebugOptions::xla_llvm_enable_invariant_load_metadata() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_llvm_enable_invariant_load_metadata)
  return xla_llvm_enable_invariant_load_metadata_;
}
inline void DebugOptions::set_xla_llvm_enable_invariant_load_metadata(bool value) {
  
  xla_llvm_enable_invariant_load_metadata_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_llvm_enable_invariant_load_metadata)
}

// bool xla_llvm_disable_expensive_passes = 73;
inline void DebugOptions::clear_xla_llvm_disable_expensive_passes() {
  xla_llvm_disable_expensive_passes_ = false;
}
inline bool DebugOptions::xla_llvm_disable_expensive_passes() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_llvm_disable_expensive_passes)
  return xla_llvm_disable_expensive_passes_;
}
inline void DebugOptions::set_xla_llvm_disable_expensive_passes(bool value) {
  
  xla_llvm_disable_expensive_passes_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_llvm_disable_expensive_passes)
}

// bool xla_test_all_output_layouts = 90;
inline void DebugOptions::clear_xla_test_all_output_layouts() {
  xla_test_all_output_layouts_ = false;
}
inline bool DebugOptions::xla_test_all_output_layouts() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_test_all_output_layouts)
  return xla_test_all_output_layouts_;
}
inline void DebugOptions::set_xla_test_all_output_layouts(bool value) {
  
  xla_test_all_output_layouts_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_test_all_output_layouts)
}

// bool xla_test_all_input_layouts = 91;
inline void DebugOptions::clear_xla_test_all_input_layouts() {
  xla_test_all_input_layouts_ = false;
}
inline bool DebugOptions::xla_test_all_input_layouts() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_test_all_input_layouts)
  return xla_test_all_input_layouts_;
}
inline void DebugOptions::set_xla_test_all_input_layouts(bool value) {
  
  xla_test_all_input_layouts_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_test_all_input_layouts)
}

// bool xla_hlo_graph_sharding_color = 92;
inline void DebugOptions::clear_xla_hlo_graph_sharding_color() {
  xla_hlo_graph_sharding_color_ = false;
}
inline bool DebugOptions::xla_hlo_graph_sharding_color() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_hlo_graph_sharding_color)
  return xla_hlo_graph_sharding_color_;
}
inline void DebugOptions::set_xla_hlo_graph_sharding_color(bool value) {
  
  xla_hlo_graph_sharding_color_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_hlo_graph_sharding_color)
}

// bool xla_gpu_use_cudnn_batchnorm = 94;
inline void DebugOptions::clear_xla_gpu_use_cudnn_batchnorm() {
  xla_gpu_use_cudnn_batchnorm_ = false;
}
inline bool DebugOptions::xla_gpu_use_cudnn_batchnorm() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_use_cudnn_batchnorm)
  return xla_gpu_use_cudnn_batchnorm_;
}
inline void DebugOptions::set_xla_gpu_use_cudnn_batchnorm(bool value) {
  
  xla_gpu_use_cudnn_batchnorm_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_use_cudnn_batchnorm)
}

// bool xla_cpu_use_mkl_dnn = 97;
inline void DebugOptions::clear_xla_cpu_use_mkl_dnn() {
  xla_cpu_use_mkl_dnn_ = false;
}
inline bool DebugOptions::xla_cpu_use_mkl_dnn() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_use_mkl_dnn)
  return xla_cpu_use_mkl_dnn_;
}
inline void DebugOptions::set_xla_cpu_use_mkl_dnn(bool value) {
  
  xla_cpu_use_mkl_dnn_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_use_mkl_dnn)
}

// int32 xla_gpu_max_kernel_unroll_factor = 98;
inline void DebugOptions::clear_xla_gpu_max_kernel_unroll_factor() {
  xla_gpu_max_kernel_unroll_factor_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_gpu_max_kernel_unroll_factor() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_max_kernel_unroll_factor)
  return xla_gpu_max_kernel_unroll_factor_;
}
inline void DebugOptions::set_xla_gpu_max_kernel_unroll_factor(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_gpu_max_kernel_unroll_factor_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_max_kernel_unroll_factor)
}

// bool xla_cpu_enable_fast_math = 99;
inline void DebugOptions::clear_xla_cpu_enable_fast_math() {
  xla_cpu_enable_fast_math_ = false;
}
inline bool DebugOptions::xla_cpu_enable_fast_math() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_enable_fast_math)
  return xla_cpu_enable_fast_math_;
}
inline void DebugOptions::set_xla_cpu_enable_fast_math(bool value) {
  
  xla_cpu_enable_fast_math_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_enable_fast_math)
}

// bool xla_cpu_fast_math_honor_nans = 120;
inline void DebugOptions::clear_xla_cpu_fast_math_honor_nans() {
  xla_cpu_fast_math_honor_nans_ = false;
}
inline bool DebugOptions::xla_cpu_fast_math_honor_nans() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_fast_math_honor_nans)
  return xla_cpu_fast_math_honor_nans_;
}
inline void DebugOptions::set_xla_cpu_fast_math_honor_nans(bool value) {
  
  xla_cpu_fast_math_honor_nans_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_fast_math_honor_nans)
}

// bool xla_cpu_fast_math_honor_infs = 121;
inline void DebugOptions::clear_xla_cpu_fast_math_honor_infs() {
  xla_cpu_fast_math_honor_infs_ = false;
}
inline bool DebugOptions::xla_cpu_fast_math_honor_infs() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_fast_math_honor_infs)
  return xla_cpu_fast_math_honor_infs_;
}
inline void DebugOptions::set_xla_cpu_fast_math_honor_infs(bool value) {
  
  xla_cpu_fast_math_honor_infs_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_fast_math_honor_infs)
}

// bool xla_cpu_fast_math_honor_division = 126;
inline void DebugOptions::clear_xla_cpu_fast_math_honor_division() {
  xla_cpu_fast_math_honor_division_ = false;
}
inline bool DebugOptions::xla_cpu_fast_math_honor_division() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_fast_math_honor_division)
  return xla_cpu_fast_math_honor_division_;
}
inline void DebugOptions::set_xla_cpu_fast_math_honor_division(bool value) {
  
  xla_cpu_fast_math_honor_division_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_fast_math_honor_division)
}

// bool xla_cpu_fast_math_honor_functions = 129;
inline void DebugOptions::clear_xla_cpu_fast_math_honor_functions() {
  xla_cpu_fast_math_honor_functions_ = false;
}
inline bool DebugOptions::xla_cpu_fast_math_honor_functions() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_fast_math_honor_functions)
  return xla_cpu_fast_math_honor_functions_;
}
inline void DebugOptions::set_xla_cpu_fast_math_honor_functions(bool value) {
  
  xla_cpu_fast_math_honor_functions_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_fast_math_honor_functions)
}

// bool xla_cpu_enable_fast_min_max = 140;
inline void DebugOptions::clear_xla_cpu_enable_fast_min_max() {
  xla_cpu_enable_fast_min_max_ = false;
}
inline bool DebugOptions::xla_cpu_enable_fast_min_max() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_enable_fast_min_max)
  return xla_cpu_enable_fast_min_max_;
}
inline void DebugOptions::set_xla_cpu_enable_fast_min_max(bool value) {
  
  xla_cpu_enable_fast_min_max_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_enable_fast_min_max)
}

// bool xla_gpu_enable_fast_min_max = 100;
inline void DebugOptions::clear_xla_gpu_enable_fast_min_max() {
  xla_gpu_enable_fast_min_max_ = false;
}
inline bool DebugOptions::xla_gpu_enable_fast_min_max() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_enable_fast_min_max)
  return xla_gpu_enable_fast_min_max_;
}
inline void DebugOptions::set_xla_gpu_enable_fast_min_max(bool value) {
  
  xla_gpu_enable_fast_min_max_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_enable_fast_min_max)
}

// bool xla_allow_excess_precision = 122;
inline void DebugOptions::clear_xla_allow_excess_precision() {
  xla_allow_excess_precision_ = false;
}
inline bool DebugOptions::xla_allow_excess_precision() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_allow_excess_precision)
  return xla_allow_excess_precision_;
}
inline void DebugOptions::set_xla_allow_excess_precision(bool value) {
  
  xla_allow_excess_precision_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_allow_excess_precision)
}

// bool xla_gpu_crash_on_verification_failures = 101;
inline void DebugOptions::clear_xla_gpu_crash_on_verification_failures() {
  xla_gpu_crash_on_verification_failures_ = false;
}
inline bool DebugOptions::xla_gpu_crash_on_verification_failures() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_crash_on_verification_failures)
  return xla_gpu_crash_on_verification_failures_;
}
inline void DebugOptions::set_xla_gpu_crash_on_verification_failures(bool value) {
  
  xla_gpu_crash_on_verification_failures_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_crash_on_verification_failures)
}

// int32 xla_gpu_autotune_level = 123;
inline void DebugOptions::clear_xla_gpu_autotune_level() {
  xla_gpu_autotune_level_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_gpu_autotune_level() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_autotune_level)
  return xla_gpu_autotune_level_;
}
inline void DebugOptions::set_xla_gpu_autotune_level(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_gpu_autotune_level_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_autotune_level)
}

// int32 xla_force_host_platform_device_count = 102;
inline void DebugOptions::clear_xla_force_host_platform_device_count() {
  xla_force_host_platform_device_count_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_force_host_platform_device_count() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_force_host_platform_device_count)
  return xla_force_host_platform_device_count_;
}
inline void DebugOptions::set_xla_force_host_platform_device_count(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_force_host_platform_device_count_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_force_host_platform_device_count)
}

// bool xla_gpu_disable_gpuasm_optimizations = 103;
inline void DebugOptions::clear_xla_gpu_disable_gpuasm_optimizations() {
  xla_gpu_disable_gpuasm_optimizations_ = false;
}
inline bool DebugOptions::xla_gpu_disable_gpuasm_optimizations() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_disable_gpuasm_optimizations)
  return xla_gpu_disable_gpuasm_optimizations_;
}
inline void DebugOptions::set_xla_gpu_disable_gpuasm_optimizations(bool value) {
  
  xla_gpu_disable_gpuasm_optimizations_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_disable_gpuasm_optimizations)
}

// bool xla_hlo_evaluator_use_fast_path = 106;
inline void DebugOptions::clear_xla_hlo_evaluator_use_fast_path() {
  xla_hlo_evaluator_use_fast_path_ = false;
}
inline bool DebugOptions::xla_hlo_evaluator_use_fast_path() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_hlo_evaluator_use_fast_path)
  return xla_hlo_evaluator_use_fast_path_;
}
inline void DebugOptions::set_xla_hlo_evaluator_use_fast_path(bool value) {
  
  xla_hlo_evaluator_use_fast_path_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_hlo_evaluator_use_fast_path)
}

// bool xla_allow_scalar_index_dynamic_ops = 107;
inline void DebugOptions::clear_xla_allow_scalar_index_dynamic_ops() {
  xla_allow_scalar_index_dynamic_ops_ = false;
}
inline bool DebugOptions::xla_allow_scalar_index_dynamic_ops() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_allow_scalar_index_dynamic_ops)
  return xla_allow_scalar_index_dynamic_ops_;
}
inline void DebugOptions::set_xla_allow_scalar_index_dynamic_ops(bool value) {
  
  xla_allow_scalar_index_dynamic_ops_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_allow_scalar_index_dynamic_ops)
}

// .xla.DebugOptions.StepMarkerLocation xla_step_marker_location = 108;
inline void DebugOptions::clear_xla_step_marker_location() {
  xla_step_marker_location_ = 0;
}
inline ::xla::DebugOptions_StepMarkerLocation DebugOptions::xla_step_marker_location() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_step_marker_location)
  return static_cast< ::xla::DebugOptions_StepMarkerLocation >(xla_step_marker_location_);
}
inline void DebugOptions::set_xla_step_marker_location(::xla::DebugOptions_StepMarkerLocation value) {
  
  xla_step_marker_location_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_step_marker_location)
}

// string xla_dump_to = 109;
inline void DebugOptions::clear_xla_dump_to() {
  xla_dump_to_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebugOptions::xla_dump_to() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_to)
  return xla_dump_to_.GetNoArena();
}
inline void DebugOptions::set_xla_dump_to(const std::string& value) {
  
  xla_dump_to_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_to)
}
inline void DebugOptions::set_xla_dump_to(std::string&& value) {
  
  xla_dump_to_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.DebugOptions.xla_dump_to)
}
inline void DebugOptions::set_xla_dump_to(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  xla_dump_to_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_dump_to)
}
inline void DebugOptions::set_xla_dump_to(const char* value, size_t size) {
  
  xla_dump_to_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_dump_to)
}
inline std::string* DebugOptions::mutable_xla_dump_to() {
  
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_dump_to)
  return xla_dump_to_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebugOptions::release_xla_dump_to() {
  // @@protoc_insertion_point(field_release:xla.DebugOptions.xla_dump_to)
  
  return xla_dump_to_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebugOptions::set_allocated_xla_dump_to(std::string* xla_dump_to) {
  if (xla_dump_to != nullptr) {
    
  } else {
    
  }
  xla_dump_to_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), xla_dump_to);
  // @@protoc_insertion_point(field_set_allocated:xla.DebugOptions.xla_dump_to)
}

// string xla_dump_hlo_module_re = 110;
inline void DebugOptions::clear_xla_dump_hlo_module_re() {
  xla_dump_hlo_module_re_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebugOptions::xla_dump_hlo_module_re() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_module_re)
  return xla_dump_hlo_module_re_.GetNoArena();
}
inline void DebugOptions::set_xla_dump_hlo_module_re(const std::string& value) {
  
  xla_dump_hlo_module_re_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_module_re)
}
inline void DebugOptions::set_xla_dump_hlo_module_re(std::string&& value) {
  
  xla_dump_hlo_module_re_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.DebugOptions.xla_dump_hlo_module_re)
}
inline void DebugOptions::set_xla_dump_hlo_module_re(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  xla_dump_hlo_module_re_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_dump_hlo_module_re)
}
inline void DebugOptions::set_xla_dump_hlo_module_re(const char* value, size_t size) {
  
  xla_dump_hlo_module_re_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_dump_hlo_module_re)
}
inline std::string* DebugOptions::mutable_xla_dump_hlo_module_re() {
  
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_dump_hlo_module_re)
  return xla_dump_hlo_module_re_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebugOptions::release_xla_dump_hlo_module_re() {
  // @@protoc_insertion_point(field_release:xla.DebugOptions.xla_dump_hlo_module_re)
  
  return xla_dump_hlo_module_re_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebugOptions::set_allocated_xla_dump_hlo_module_re(std::string* xla_dump_hlo_module_re) {
  if (xla_dump_hlo_module_re != nullptr) {
    
  } else {
    
  }
  xla_dump_hlo_module_re_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), xla_dump_hlo_module_re);
  // @@protoc_insertion_point(field_set_allocated:xla.DebugOptions.xla_dump_hlo_module_re)
}

// string xla_dump_hlo_pass_re = 111;
inline void DebugOptions::clear_xla_dump_hlo_pass_re() {
  xla_dump_hlo_pass_re_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebugOptions::xla_dump_hlo_pass_re() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_pass_re)
  return xla_dump_hlo_pass_re_.GetNoArena();
}
inline void DebugOptions::set_xla_dump_hlo_pass_re(const std::string& value) {
  
  xla_dump_hlo_pass_re_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_pass_re)
}
inline void DebugOptions::set_xla_dump_hlo_pass_re(std::string&& value) {
  
  xla_dump_hlo_pass_re_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.DebugOptions.xla_dump_hlo_pass_re)
}
inline void DebugOptions::set_xla_dump_hlo_pass_re(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  xla_dump_hlo_pass_re_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_dump_hlo_pass_re)
}
inline void DebugOptions::set_xla_dump_hlo_pass_re(const char* value, size_t size) {
  
  xla_dump_hlo_pass_re_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_dump_hlo_pass_re)
}
inline std::string* DebugOptions::mutable_xla_dump_hlo_pass_re() {
  
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_dump_hlo_pass_re)
  return xla_dump_hlo_pass_re_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebugOptions::release_xla_dump_hlo_pass_re() {
  // @@protoc_insertion_point(field_release:xla.DebugOptions.xla_dump_hlo_pass_re)
  
  return xla_dump_hlo_pass_re_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebugOptions::set_allocated_xla_dump_hlo_pass_re(std::string* xla_dump_hlo_pass_re) {
  if (xla_dump_hlo_pass_re != nullptr) {
    
  } else {
    
  }
  xla_dump_hlo_pass_re_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), xla_dump_hlo_pass_re);
  // @@protoc_insertion_point(field_set_allocated:xla.DebugOptions.xla_dump_hlo_pass_re)
}

// bool xla_dump_hlo_as_text = 112;
inline void DebugOptions::clear_xla_dump_hlo_as_text() {
  xla_dump_hlo_as_text_ = false;
}
inline bool DebugOptions::xla_dump_hlo_as_text() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_as_text)
  return xla_dump_hlo_as_text_;
}
inline void DebugOptions::set_xla_dump_hlo_as_text(bool value) {
  
  xla_dump_hlo_as_text_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_as_text)
}

// bool xla_dump_hlo_as_proto = 113;
inline void DebugOptions::clear_xla_dump_hlo_as_proto() {
  xla_dump_hlo_as_proto_ = false;
}
inline bool DebugOptions::xla_dump_hlo_as_proto() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_as_proto)
  return xla_dump_hlo_as_proto_;
}
inline void DebugOptions::set_xla_dump_hlo_as_proto(bool value) {
  
  xla_dump_hlo_as_proto_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_as_proto)
}

// bool xla_dump_hlo_as_dot = 114;
inline void DebugOptions::clear_xla_dump_hlo_as_dot() {
  xla_dump_hlo_as_dot_ = false;
}
inline bool DebugOptions::xla_dump_hlo_as_dot() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_as_dot)
  return xla_dump_hlo_as_dot_;
}
inline void DebugOptions::set_xla_dump_hlo_as_dot(bool value) {
  
  xla_dump_hlo_as_dot_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_as_dot)
}

// bool xla_dump_hlo_as_url = 115;
inline void DebugOptions::clear_xla_dump_hlo_as_url() {
  xla_dump_hlo_as_url_ = false;
}
inline bool DebugOptions::xla_dump_hlo_as_url() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_as_url)
  return xla_dump_hlo_as_url_;
}
inline void DebugOptions::set_xla_dump_hlo_as_url(bool value) {
  
  xla_dump_hlo_as_url_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_as_url)
}

// bool xla_dump_hlo_as_html = 116;
inline void DebugOptions::clear_xla_dump_hlo_as_html() {
  xla_dump_hlo_as_html_ = false;
}
inline bool DebugOptions::xla_dump_hlo_as_html() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_as_html)
  return xla_dump_hlo_as_html_;
}
inline void DebugOptions::set_xla_dump_hlo_as_html(bool value) {
  
  xla_dump_hlo_as_html_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_as_html)
}

// bool xla_dump_fusion_visualization = 149;
inline void DebugOptions::clear_xla_dump_fusion_visualization() {
  xla_dump_fusion_visualization_ = false;
}
inline bool DebugOptions::xla_dump_fusion_visualization() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_fusion_visualization)
  return xla_dump_fusion_visualization_;
}
inline void DebugOptions::set_xla_dump_fusion_visualization(bool value) {
  
  xla_dump_fusion_visualization_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_fusion_visualization)
}

// bool xla_dump_hlo_snapshots = 118;
inline void DebugOptions::clear_xla_dump_hlo_snapshots() {
  xla_dump_hlo_snapshots_ = false;
}
inline bool DebugOptions::xla_dump_hlo_snapshots() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_hlo_snapshots)
  return xla_dump_hlo_snapshots_;
}
inline void DebugOptions::set_xla_dump_hlo_snapshots(bool value) {
  
  xla_dump_hlo_snapshots_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_hlo_snapshots)
}

// bool xla_dump_include_timestamp = 131;
inline void DebugOptions::clear_xla_dump_include_timestamp() {
  xla_dump_include_timestamp_ = false;
}
inline bool DebugOptions::xla_dump_include_timestamp() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_include_timestamp)
  return xla_dump_include_timestamp_;
}
inline void DebugOptions::set_xla_dump_include_timestamp(bool value) {
  
  xla_dump_include_timestamp_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_include_timestamp)
}

// int32 xla_dump_max_hlo_modules = 132;
inline void DebugOptions::clear_xla_dump_max_hlo_modules() {
  xla_dump_max_hlo_modules_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_dump_max_hlo_modules() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_max_hlo_modules)
  return xla_dump_max_hlo_modules_;
}
inline void DebugOptions::set_xla_dump_max_hlo_modules(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_dump_max_hlo_modules_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_max_hlo_modules)
}

// bool xla_dump_module_metadata = 144;
inline void DebugOptions::clear_xla_dump_module_metadata() {
  xla_dump_module_metadata_ = false;
}
inline bool DebugOptions::xla_dump_module_metadata() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_module_metadata)
  return xla_dump_module_metadata_;
}
inline void DebugOptions::set_xla_dump_module_metadata(bool value) {
  
  xla_dump_module_metadata_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_module_metadata)
}

// bool xla_dump_compress_protos = 151;
inline void DebugOptions::clear_xla_dump_compress_protos() {
  xla_dump_compress_protos_ = false;
}
inline bool DebugOptions::xla_dump_compress_protos() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_dump_compress_protos)
  return xla_dump_compress_protos_;
}
inline void DebugOptions::set_xla_dump_compress_protos(bool value) {
  
  xla_dump_compress_protos_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_dump_compress_protos)
}

// bool xla_gpu_force_conv_nchw = 125;
inline void DebugOptions::clear_xla_gpu_force_conv_nchw() {
  xla_gpu_force_conv_nchw_ = false;
}
inline bool DebugOptions::xla_gpu_force_conv_nchw() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_force_conv_nchw)
  return xla_gpu_force_conv_nchw_;
}
inline void DebugOptions::set_xla_gpu_force_conv_nchw(bool value) {
  
  xla_gpu_force_conv_nchw_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_force_conv_nchw)
}

// bool xla_gpu_force_conv_nhwc = 146;
inline void DebugOptions::clear_xla_gpu_force_conv_nhwc() {
  xla_gpu_force_conv_nhwc_ = false;
}
inline bool DebugOptions::xla_gpu_force_conv_nhwc() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_force_conv_nhwc)
  return xla_gpu_force_conv_nhwc_;
}
inline void DebugOptions::set_xla_gpu_force_conv_nhwc(bool value) {
  
  xla_gpu_force_conv_nhwc_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_force_conv_nhwc)
}

// repeated string xla_gpu_ptx_file = 127;
inline int DebugOptions::xla_gpu_ptx_file_size() const {
  return xla_gpu_ptx_file_.size();
}
inline void DebugOptions::clear_xla_gpu_ptx_file() {
  xla_gpu_ptx_file_.Clear();
}
inline const std::string& DebugOptions::xla_gpu_ptx_file(int index) const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_ptx_file)
  return xla_gpu_ptx_file_.Get(index);
}
inline std::string* DebugOptions::mutable_xla_gpu_ptx_file(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_gpu_ptx_file)
  return xla_gpu_ptx_file_.Mutable(index);
}
inline void DebugOptions::set_xla_gpu_ptx_file(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_ptx_file)
  xla_gpu_ptx_file_.Mutable(index)->assign(value);
}
inline void DebugOptions::set_xla_gpu_ptx_file(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_ptx_file)
  xla_gpu_ptx_file_.Mutable(index)->assign(std::move(value));
}
inline void DebugOptions::set_xla_gpu_ptx_file(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_gpu_ptx_file_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_gpu_ptx_file)
}
inline void DebugOptions::set_xla_gpu_ptx_file(int index, const char* value, size_t size) {
  xla_gpu_ptx_file_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_gpu_ptx_file)
}
inline std::string* DebugOptions::add_xla_gpu_ptx_file() {
  // @@protoc_insertion_point(field_add_mutable:xla.DebugOptions.xla_gpu_ptx_file)
  return xla_gpu_ptx_file_.Add();
}
inline void DebugOptions::add_xla_gpu_ptx_file(const std::string& value) {
  xla_gpu_ptx_file_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_gpu_ptx_file)
}
inline void DebugOptions::add_xla_gpu_ptx_file(std::string&& value) {
  xla_gpu_ptx_file_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_gpu_ptx_file)
}
inline void DebugOptions::add_xla_gpu_ptx_file(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_gpu_ptx_file_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.DebugOptions.xla_gpu_ptx_file)
}
inline void DebugOptions::add_xla_gpu_ptx_file(const char* value, size_t size) {
  xla_gpu_ptx_file_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.DebugOptions.xla_gpu_ptx_file)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugOptions::xla_gpu_ptx_file() const {
  // @@protoc_insertion_point(field_list:xla.DebugOptions.xla_gpu_ptx_file)
  return xla_gpu_ptx_file_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugOptions::mutable_xla_gpu_ptx_file() {
  // @@protoc_insertion_point(field_mutable_list:xla.DebugOptions.xla_gpu_ptx_file)
  return &xla_gpu_ptx_file_;
}

// string xla_gpu_algorithm_denylist_path = 128;
inline void DebugOptions::clear_xla_gpu_algorithm_denylist_path() {
  xla_gpu_algorithm_denylist_path_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebugOptions::xla_gpu_algorithm_denylist_path() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
  return xla_gpu_algorithm_denylist_path_.GetNoArena();
}
inline void DebugOptions::set_xla_gpu_algorithm_denylist_path(const std::string& value) {
  
  xla_gpu_algorithm_denylist_path_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
}
inline void DebugOptions::set_xla_gpu_algorithm_denylist_path(std::string&& value) {
  
  xla_gpu_algorithm_denylist_path_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
}
inline void DebugOptions::set_xla_gpu_algorithm_denylist_path(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  xla_gpu_algorithm_denylist_path_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
}
inline void DebugOptions::set_xla_gpu_algorithm_denylist_path(const char* value, size_t size) {
  
  xla_gpu_algorithm_denylist_path_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
}
inline std::string* DebugOptions::mutable_xla_gpu_algorithm_denylist_path() {
  
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
  return xla_gpu_algorithm_denylist_path_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebugOptions::release_xla_gpu_algorithm_denylist_path() {
  // @@protoc_insertion_point(field_release:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
  
  return xla_gpu_algorithm_denylist_path_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebugOptions::set_allocated_xla_gpu_algorithm_denylist_path(std::string* xla_gpu_algorithm_denylist_path) {
  if (xla_gpu_algorithm_denylist_path != nullptr) {
    
  } else {
    
  }
  xla_gpu_algorithm_denylist_path_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), xla_gpu_algorithm_denylist_path);
  // @@protoc_insertion_point(field_set_allocated:xla.DebugOptions.xla_gpu_algorithm_denylist_path)
}

// bool xla_gpu_deterministic_reductions = 130;
inline void DebugOptions::clear_xla_gpu_deterministic_reductions() {
  xla_gpu_deterministic_reductions_ = false;
}
inline bool DebugOptions::xla_gpu_deterministic_reductions() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_deterministic_reductions)
  return xla_gpu_deterministic_reductions_;
}
inline void DebugOptions::set_xla_gpu_deterministic_reductions(bool value) {
  
  xla_gpu_deterministic_reductions_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_deterministic_reductions)
}

// bool xla_tpu_detect_nan = 135;
inline void DebugOptions::clear_xla_tpu_detect_nan() {
  xla_tpu_detect_nan_ = false;
}
inline bool DebugOptions::xla_tpu_detect_nan() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_tpu_detect_nan)
  return xla_tpu_detect_nan_;
}
inline void DebugOptions::set_xla_tpu_detect_nan(bool value) {
  
  xla_tpu_detect_nan_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_tpu_detect_nan)
}

// bool xla_tpu_detect_inf = 136;
inline void DebugOptions::clear_xla_tpu_detect_inf() {
  xla_tpu_detect_inf_ = false;
}
inline bool DebugOptions::xla_tpu_detect_inf() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_tpu_detect_inf)
  return xla_tpu_detect_inf_;
}
inline void DebugOptions::set_xla_tpu_detect_inf(bool value) {
  
  xla_tpu_detect_inf_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_tpu_detect_inf)
}

// bool xla_cpu_enable_xprof_traceme = 137;
inline void DebugOptions::clear_xla_cpu_enable_xprof_traceme() {
  xla_cpu_enable_xprof_traceme_ = false;
}
inline bool DebugOptions::xla_cpu_enable_xprof_traceme() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_cpu_enable_xprof_traceme)
  return xla_cpu_enable_xprof_traceme_;
}
inline void DebugOptions::set_xla_cpu_enable_xprof_traceme(bool value) {
  
  xla_cpu_enable_xprof_traceme_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_cpu_enable_xprof_traceme)
}

// bool xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found = 138;
inline void DebugOptions::clear_xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found() {
  xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found_ = false;
}
inline bool DebugOptions::xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found)
  return xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found_;
}
inline void DebugOptions::set_xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found(bool value) {
  
  xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_unsafe_fallback_to_driver_on_ptxas_not_found)
}

// string xla_gpu_asm_extra_flags = 141;
inline void DebugOptions::clear_xla_gpu_asm_extra_flags() {
  xla_gpu_asm_extra_flags_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& DebugOptions::xla_gpu_asm_extra_flags() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_asm_extra_flags)
  return xla_gpu_asm_extra_flags_.GetNoArena();
}
inline void DebugOptions::set_xla_gpu_asm_extra_flags(const std::string& value) {
  
  xla_gpu_asm_extra_flags_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_asm_extra_flags)
}
inline void DebugOptions::set_xla_gpu_asm_extra_flags(std::string&& value) {
  
  xla_gpu_asm_extra_flags_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.DebugOptions.xla_gpu_asm_extra_flags)
}
inline void DebugOptions::set_xla_gpu_asm_extra_flags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  xla_gpu_asm_extra_flags_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_gpu_asm_extra_flags)
}
inline void DebugOptions::set_xla_gpu_asm_extra_flags(const char* value, size_t size) {
  
  xla_gpu_asm_extra_flags_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_gpu_asm_extra_flags)
}
inline std::string* DebugOptions::mutable_xla_gpu_asm_extra_flags() {
  
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_gpu_asm_extra_flags)
  return xla_gpu_asm_extra_flags_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* DebugOptions::release_xla_gpu_asm_extra_flags() {
  // @@protoc_insertion_point(field_release:xla.DebugOptions.xla_gpu_asm_extra_flags)
  
  return xla_gpu_asm_extra_flags_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void DebugOptions::set_allocated_xla_gpu_asm_extra_flags(std::string* xla_gpu_asm_extra_flags) {
  if (xla_gpu_asm_extra_flags != nullptr) {
    
  } else {
    
  }
  xla_gpu_asm_extra_flags_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), xla_gpu_asm_extra_flags);
  // @@protoc_insertion_point(field_set_allocated:xla.DebugOptions.xla_gpu_asm_extra_flags)
}

// int32 xla_multiheap_size_constraint_per_heap = 142;
inline void DebugOptions::clear_xla_multiheap_size_constraint_per_heap() {
  xla_multiheap_size_constraint_per_heap_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_multiheap_size_constraint_per_heap() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_multiheap_size_constraint_per_heap)
  return xla_multiheap_size_constraint_per_heap_;
}
inline void DebugOptions::set_xla_multiheap_size_constraint_per_heap(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_multiheap_size_constraint_per_heap_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_multiheap_size_constraint_per_heap)
}

// bool xla_detailed_logging_and_dumping = 143;
inline void DebugOptions::clear_xla_detailed_logging_and_dumping() {
  xla_detailed_logging_and_dumping_ = false;
}
inline bool DebugOptions::xla_detailed_logging_and_dumping() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_detailed_logging_and_dumping)
  return xla_detailed_logging_and_dumping_;
}
inline void DebugOptions::set_xla_detailed_logging_and_dumping(bool value) {
  
  xla_detailed_logging_and_dumping_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_detailed_logging_and_dumping)
}

// int32 xla_gpu_force_compilation_parallelism = 147;
inline void DebugOptions::clear_xla_gpu_force_compilation_parallelism() {
  xla_gpu_force_compilation_parallelism_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 DebugOptions::xla_gpu_force_compilation_parallelism() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_force_compilation_parallelism)
  return xla_gpu_force_compilation_parallelism_;
}
inline void DebugOptions::set_xla_gpu_force_compilation_parallelism(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  xla_gpu_force_compilation_parallelism_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_force_compilation_parallelism)
}

// bool xla_gpu_deterministic_ops = 148;
inline void DebugOptions::clear_xla_gpu_deterministic_ops() {
  xla_gpu_deterministic_ops_ = false;
}
inline bool DebugOptions::xla_gpu_deterministic_ops() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_deterministic_ops)
  return xla_gpu_deterministic_ops_;
}
inline void DebugOptions::set_xla_gpu_deterministic_ops(bool value) {
  
  xla_gpu_deterministic_ops_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_deterministic_ops)
}

// repeated string xla_gpu_llvm_ir_file = 150;
inline int DebugOptions::xla_gpu_llvm_ir_file_size() const {
  return xla_gpu_llvm_ir_file_.size();
}
inline void DebugOptions::clear_xla_gpu_llvm_ir_file() {
  xla_gpu_llvm_ir_file_.Clear();
}
inline const std::string& DebugOptions::xla_gpu_llvm_ir_file(int index) const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_llvm_ir_file)
  return xla_gpu_llvm_ir_file_.Get(index);
}
inline std::string* DebugOptions::mutable_xla_gpu_llvm_ir_file(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DebugOptions.xla_gpu_llvm_ir_file)
  return xla_gpu_llvm_ir_file_.Mutable(index);
}
inline void DebugOptions::set_xla_gpu_llvm_ir_file(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_llvm_ir_file)
  xla_gpu_llvm_ir_file_.Mutable(index)->assign(value);
}
inline void DebugOptions::set_xla_gpu_llvm_ir_file(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_llvm_ir_file)
  xla_gpu_llvm_ir_file_.Mutable(index)->assign(std::move(value));
}
inline void DebugOptions::set_xla_gpu_llvm_ir_file(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_gpu_llvm_ir_file_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.DebugOptions.xla_gpu_llvm_ir_file)
}
inline void DebugOptions::set_xla_gpu_llvm_ir_file(int index, const char* value, size_t size) {
  xla_gpu_llvm_ir_file_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.DebugOptions.xla_gpu_llvm_ir_file)
}
inline std::string* DebugOptions::add_xla_gpu_llvm_ir_file() {
  // @@protoc_insertion_point(field_add_mutable:xla.DebugOptions.xla_gpu_llvm_ir_file)
  return xla_gpu_llvm_ir_file_.Add();
}
inline void DebugOptions::add_xla_gpu_llvm_ir_file(const std::string& value) {
  xla_gpu_llvm_ir_file_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_gpu_llvm_ir_file)
}
inline void DebugOptions::add_xla_gpu_llvm_ir_file(std::string&& value) {
  xla_gpu_llvm_ir_file_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.DebugOptions.xla_gpu_llvm_ir_file)
}
inline void DebugOptions::add_xla_gpu_llvm_ir_file(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  xla_gpu_llvm_ir_file_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.DebugOptions.xla_gpu_llvm_ir_file)
}
inline void DebugOptions::add_xla_gpu_llvm_ir_file(const char* value, size_t size) {
  xla_gpu_llvm_ir_file_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.DebugOptions.xla_gpu_llvm_ir_file)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugOptions::xla_gpu_llvm_ir_file() const {
  // @@protoc_insertion_point(field_list:xla.DebugOptions.xla_gpu_llvm_ir_file)
  return xla_gpu_llvm_ir_file_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugOptions::mutable_xla_gpu_llvm_ir_file() {
  // @@protoc_insertion_point(field_mutable_list:xla.DebugOptions.xla_gpu_llvm_ir_file)
  return &xla_gpu_llvm_ir_file_;
}

// bool xla_gpu_enable_async_all_reduce = 152;
inline void DebugOptions::clear_xla_gpu_enable_async_all_reduce() {
  xla_gpu_enable_async_all_reduce_ = false;
}
inline bool DebugOptions::xla_gpu_enable_async_all_reduce() const {
  // @@protoc_insertion_point(field_get:xla.DebugOptions.xla_gpu_enable_async_all_reduce)
  return xla_gpu_enable_async_all_reduce_;
}
inline void DebugOptions::set_xla_gpu_enable_async_all_reduce(bool value) {
  
  xla_gpu_enable_async_all_reduce_ = value;
  // @@protoc_insertion_point(field_set:xla.DebugOptions.xla_gpu_enable_async_all_reduce)
}

// map<string, string> xla_backend_extra_options = 500;
inline int DebugOptions::xla_backend_extra_options_size() const {
  return xla_backend_extra_options_.size();
}
inline void DebugOptions::clear_xla_backend_extra_options() {
  xla_backend_extra_options_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
DebugOptions::xla_backend_extra_options() const {
  // @@protoc_insertion_point(field_map:xla.DebugOptions.xla_backend_extra_options)
  return xla_backend_extra_options_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
DebugOptions::mutable_xla_backend_extra_options() {
  // @@protoc_insertion_point(field_mutable_map:xla.DebugOptions.xla_backend_extra_options)
  return xla_backend_extra_options_.MutableMap();
}

// -------------------------------------------------------------------

// ExecutionOptions

// .xla.ShapeProto shape_with_output_layout = 2;
inline bool ExecutionOptions::has_shape_with_output_layout() const {
  return this != internal_default_instance() && shape_with_output_layout_ != nullptr;
}
inline const ::xla::ShapeProto& ExecutionOptions::shape_with_output_layout() const {
  const ::xla::ShapeProto* p = shape_with_output_layout_;
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.shape_with_output_layout)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* ExecutionOptions::release_shape_with_output_layout() {
  // @@protoc_insertion_point(field_release:xla.ExecutionOptions.shape_with_output_layout)
  
  ::xla::ShapeProto* temp = shape_with_output_layout_;
  shape_with_output_layout_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* ExecutionOptions::mutable_shape_with_output_layout() {
  
  if (shape_with_output_layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_with_output_layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecutionOptions.shape_with_output_layout)
  return shape_with_output_layout_;
}
inline void ExecutionOptions::set_allocated_shape_with_output_layout(::xla::ShapeProto* shape_with_output_layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_with_output_layout_);
  }
  if (shape_with_output_layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_with_output_layout)->GetArena();
    if (message_arena != submessage_arena) {
      shape_with_output_layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape_with_output_layout, submessage_arena);
    }
    
  } else {
    
  }
  shape_with_output_layout_ = shape_with_output_layout;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutionOptions.shape_with_output_layout)
}

// uint64 seed = 3;
inline void ExecutionOptions::clear_seed() {
  seed_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ExecutionOptions::seed() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.seed)
  return seed_;
}
inline void ExecutionOptions::set_seed(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  seed_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.seed)
}

// .xla.DebugOptions debug_options = 4;
inline bool ExecutionOptions::has_debug_options() const {
  return this != internal_default_instance() && debug_options_ != nullptr;
}
inline void ExecutionOptions::clear_debug_options() {
  if (GetArenaNoVirtual() == nullptr && debug_options_ != nullptr) {
    delete debug_options_;
  }
  debug_options_ = nullptr;
}
inline const ::xla::DebugOptions& ExecutionOptions::debug_options() const {
  const ::xla::DebugOptions* p = debug_options_;
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.debug_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DebugOptions*>(
      &::xla::_DebugOptions_default_instance_);
}
inline ::xla::DebugOptions* ExecutionOptions::release_debug_options() {
  // @@protoc_insertion_point(field_release:xla.ExecutionOptions.debug_options)
  
  ::xla::DebugOptions* temp = debug_options_;
  debug_options_ = nullptr;
  return temp;
}
inline ::xla::DebugOptions* ExecutionOptions::mutable_debug_options() {
  
  if (debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DebugOptions>(GetArenaNoVirtual());
    debug_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecutionOptions.debug_options)
  return debug_options_;
}
inline void ExecutionOptions::set_allocated_debug_options(::xla::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete debug_options_;
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutionOptions.debug_options)
}

// repeated .xla.DeviceHandle device_handles = 5;
inline int ExecutionOptions::device_handles_size() const {
  return device_handles_.size();
}
inline ::xla::DeviceHandle* ExecutionOptions::mutable_device_handles(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ExecutionOptions.device_handles)
  return device_handles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >*
ExecutionOptions::mutable_device_handles() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecutionOptions.device_handles)
  return &device_handles_;
}
inline const ::xla::DeviceHandle& ExecutionOptions::device_handles(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.device_handles)
  return device_handles_.Get(index);
}
inline ::xla::DeviceHandle* ExecutionOptions::add_device_handles() {
  // @@protoc_insertion_point(field_add:xla.ExecutionOptions.device_handles)
  return device_handles_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >&
ExecutionOptions::device_handles() const {
  // @@protoc_insertion_point(field_list:xla.ExecutionOptions.device_handles)
  return device_handles_;
}

// int32 num_replicas = 6;
inline void ExecutionOptions::clear_num_replicas() {
  num_replicas_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ExecutionOptions::num_replicas() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.num_replicas)
  return num_replicas_;
}
inline void ExecutionOptions::set_num_replicas(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_replicas_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.num_replicas)
}

// .xla.DeviceAssignmentProto device_assignment = 7;
inline bool ExecutionOptions::has_device_assignment() const {
  return this != internal_default_instance() && device_assignment_ != nullptr;
}
inline const ::xla::DeviceAssignmentProto& ExecutionOptions::device_assignment() const {
  const ::xla::DeviceAssignmentProto* p = device_assignment_;
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.device_assignment)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DeviceAssignmentProto*>(
      &::xla::_DeviceAssignmentProto_default_instance_);
}
inline ::xla::DeviceAssignmentProto* ExecutionOptions::release_device_assignment() {
  // @@protoc_insertion_point(field_release:xla.ExecutionOptions.device_assignment)
  
  ::xla::DeviceAssignmentProto* temp = device_assignment_;
  device_assignment_ = nullptr;
  return temp;
}
inline ::xla::DeviceAssignmentProto* ExecutionOptions::mutable_device_assignment() {
  
  if (device_assignment_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceAssignmentProto>(GetArenaNoVirtual());
    device_assignment_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecutionOptions.device_assignment)
  return device_assignment_;
}
inline void ExecutionOptions::set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_assignment_);
  }
  if (device_assignment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_assignment)->GetArena();
    if (message_arena != submessage_arena) {
      device_assignment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_assignment, submessage_arena);
    }
    
  } else {
    
  }
  device_assignment_ = device_assignment;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutionOptions.device_assignment)
}

// bool alias_passthrough_params = 8;
inline void ExecutionOptions::clear_alias_passthrough_params() {
  alias_passthrough_params_ = false;
}
inline bool ExecutionOptions::alias_passthrough_params() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.alias_passthrough_params)
  return alias_passthrough_params_;
}
inline void ExecutionOptions::set_alias_passthrough_params(bool value) {
  
  alias_passthrough_params_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.alias_passthrough_params)
}

// int32 num_partitions = 9;
inline void ExecutionOptions::clear_num_partitions() {
  num_partitions_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ExecutionOptions::num_partitions() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.num_partitions)
  return num_partitions_;
}
inline void ExecutionOptions::set_num_partitions(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  num_partitions_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.num_partitions)
}

// int32 launch_id = 10;
inline void ExecutionOptions::clear_launch_id() {
  launch_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 ExecutionOptions::launch_id() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.launch_id)
  return launch_id_;
}
inline void ExecutionOptions::set_launch_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  launch_id_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.launch_id)
}

// bool use_spmd_partitioning = 11;
inline void ExecutionOptions::clear_use_spmd_partitioning() {
  use_spmd_partitioning_ = false;
}
inline bool ExecutionOptions::use_spmd_partitioning() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.use_spmd_partitioning)
  return use_spmd_partitioning_;
}
inline void ExecutionOptions::set_use_spmd_partitioning(bool value) {
  
  use_spmd_partitioning_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.use_spmd_partitioning)
}

// bool deduplicate_hlo = 12;
inline void ExecutionOptions::clear_deduplicate_hlo() {
  deduplicate_hlo_ = false;
}
inline bool ExecutionOptions::deduplicate_hlo() const {
  // @@protoc_insertion_point(field_get:xla.ExecutionOptions.deduplicate_hlo)
  return deduplicate_hlo_;
}
inline void ExecutionOptions::set_deduplicate_hlo(bool value) {
  
  deduplicate_hlo_ = value;
  // @@protoc_insertion_point(field_set:xla.ExecutionOptions.deduplicate_hlo)
}

// -------------------------------------------------------------------

// GetDeviceHandlesRequest

// int64 device_count = 1;
inline void GetDeviceHandlesRequest::clear_device_count() {
  device_count_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GetDeviceHandlesRequest::device_count() const {
  // @@protoc_insertion_point(field_get:xla.GetDeviceHandlesRequest.device_count)
  return device_count_;
}
inline void GetDeviceHandlesRequest::set_device_count(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_count_ = value;
  // @@protoc_insertion_point(field_set:xla.GetDeviceHandlesRequest.device_count)
}

// -------------------------------------------------------------------

// GetDeviceHandlesResponse

// repeated .xla.DeviceHandle device_handles = 1;
inline int GetDeviceHandlesResponse::device_handles_size() const {
  return device_handles_.size();
}
inline ::xla::DeviceHandle* GetDeviceHandlesResponse::mutable_device_handles(int index) {
  // @@protoc_insertion_point(field_mutable:xla.GetDeviceHandlesResponse.device_handles)
  return device_handles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >*
GetDeviceHandlesResponse::mutable_device_handles() {
  // @@protoc_insertion_point(field_mutable_list:xla.GetDeviceHandlesResponse.device_handles)
  return &device_handles_;
}
inline const ::xla::DeviceHandle& GetDeviceHandlesResponse::device_handles(int index) const {
  // @@protoc_insertion_point(field_get:xla.GetDeviceHandlesResponse.device_handles)
  return device_handles_.Get(index);
}
inline ::xla::DeviceHandle* GetDeviceHandlesResponse::add_device_handles() {
  // @@protoc_insertion_point(field_add:xla.GetDeviceHandlesResponse.device_handles)
  return device_handles_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::DeviceHandle >&
GetDeviceHandlesResponse::device_handles() const {
  // @@protoc_insertion_point(field_list:xla.GetDeviceHandlesResponse.device_handles)
  return device_handles_;
}

// -------------------------------------------------------------------

// TransferToClientRequest

// .xla.GlobalDataHandle data = 1;
inline bool TransferToClientRequest::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline const ::xla::GlobalDataHandle& TransferToClientRequest::data() const {
  const ::xla::GlobalDataHandle* p = data_;
  // @@protoc_insertion_point(field_get:xla.TransferToClientRequest.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* TransferToClientRequest::release_data() {
  // @@protoc_insertion_point(field_release:xla.TransferToClientRequest.data)
  
  ::xla::GlobalDataHandle* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* TransferToClientRequest::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToClientRequest.data)
  return data_;
}
inline void TransferToClientRequest::set_allocated_data(::xla::GlobalDataHandle* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToClientRequest.data)
}

// .xla.ShapeProto shape_with_layout = 2;
inline bool TransferToClientRequest::has_shape_with_layout() const {
  return this != internal_default_instance() && shape_with_layout_ != nullptr;
}
inline const ::xla::ShapeProto& TransferToClientRequest::shape_with_layout() const {
  const ::xla::ShapeProto* p = shape_with_layout_;
  // @@protoc_insertion_point(field_get:xla.TransferToClientRequest.shape_with_layout)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* TransferToClientRequest::release_shape_with_layout() {
  // @@protoc_insertion_point(field_release:xla.TransferToClientRequest.shape_with_layout)
  
  ::xla::ShapeProto* temp = shape_with_layout_;
  shape_with_layout_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* TransferToClientRequest::mutable_shape_with_layout() {
  
  if (shape_with_layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_with_layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToClientRequest.shape_with_layout)
  return shape_with_layout_;
}
inline void TransferToClientRequest::set_allocated_shape_with_layout(::xla::ShapeProto* shape_with_layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_with_layout_);
  }
  if (shape_with_layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_with_layout)->GetArena();
    if (message_arena != submessage_arena) {
      shape_with_layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape_with_layout, submessage_arena);
    }
    
  } else {
    
  }
  shape_with_layout_ = shape_with_layout;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToClientRequest.shape_with_layout)
}

// -------------------------------------------------------------------

// TransferToClientResponse

// .xla.LiteralProto literal = 1;
inline bool TransferToClientResponse::has_literal() const {
  return this != internal_default_instance() && literal_ != nullptr;
}
inline const ::xla::LiteralProto& TransferToClientResponse::literal() const {
  const ::xla::LiteralProto* p = literal_;
  // @@protoc_insertion_point(field_get:xla.TransferToClientResponse.literal)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* TransferToClientResponse::release_literal() {
  // @@protoc_insertion_point(field_release:xla.TransferToClientResponse.literal)
  
  ::xla::LiteralProto* temp = literal_;
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* TransferToClientResponse::mutable_literal() {
  
  if (literal_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    literal_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToClientResponse.literal)
  return literal_;
}
inline void TransferToClientResponse::set_allocated_literal(::xla::LiteralProto* literal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal_);
  }
  if (literal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal)->GetArena();
    if (message_arena != submessage_arena) {
      literal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, literal, submessage_arena);
    }
    
  } else {
    
  }
  literal_ = literal;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToClientResponse.literal)
}

// -------------------------------------------------------------------

// TransferToServerRequest

// .xla.LiteralProto literal = 1;
inline bool TransferToServerRequest::has_literal() const {
  return this != internal_default_instance() && literal_ != nullptr;
}
inline const ::xla::LiteralProto& TransferToServerRequest::literal() const {
  const ::xla::LiteralProto* p = literal_;
  // @@protoc_insertion_point(field_get:xla.TransferToServerRequest.literal)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* TransferToServerRequest::release_literal() {
  // @@protoc_insertion_point(field_release:xla.TransferToServerRequest.literal)
  
  ::xla::LiteralProto* temp = literal_;
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* TransferToServerRequest::mutable_literal() {
  
  if (literal_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    literal_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToServerRequest.literal)
  return literal_;
}
inline void TransferToServerRequest::set_allocated_literal(::xla::LiteralProto* literal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal_);
  }
  if (literal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal)->GetArena();
    if (message_arena != submessage_arena) {
      literal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, literal, submessage_arena);
    }
    
  } else {
    
  }
  literal_ = literal;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToServerRequest.literal)
}

// .xla.DeviceHandle device_handle = 2;
inline bool TransferToServerRequest::has_device_handle() const {
  return this != internal_default_instance() && device_handle_ != nullptr;
}
inline const ::xla::DeviceHandle& TransferToServerRequest::device_handle() const {
  const ::xla::DeviceHandle* p = device_handle_;
  // @@protoc_insertion_point(field_get:xla.TransferToServerRequest.device_handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DeviceHandle*>(
      &::xla::_DeviceHandle_default_instance_);
}
inline ::xla::DeviceHandle* TransferToServerRequest::release_device_handle() {
  // @@protoc_insertion_point(field_release:xla.TransferToServerRequest.device_handle)
  
  ::xla::DeviceHandle* temp = device_handle_;
  device_handle_ = nullptr;
  return temp;
}
inline ::xla::DeviceHandle* TransferToServerRequest::mutable_device_handle() {
  
  if (device_handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceHandle>(GetArenaNoVirtual());
    device_handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToServerRequest.device_handle)
  return device_handle_;
}
inline void TransferToServerRequest::set_allocated_device_handle(::xla::DeviceHandle* device_handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle_);
  }
  if (device_handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle)->GetArena();
    if (message_arena != submessage_arena) {
      device_handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_handle, submessage_arena);
    }
    
  } else {
    
  }
  device_handle_ = device_handle;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToServerRequest.device_handle)
}

// -------------------------------------------------------------------

// TransferToServerResponse

// .xla.GlobalDataHandle data = 1;
inline bool TransferToServerResponse::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline const ::xla::GlobalDataHandle& TransferToServerResponse::data() const {
  const ::xla::GlobalDataHandle* p = data_;
  // @@protoc_insertion_point(field_get:xla.TransferToServerResponse.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* TransferToServerResponse::release_data() {
  // @@protoc_insertion_point(field_release:xla.TransferToServerResponse.data)
  
  ::xla::GlobalDataHandle* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* TransferToServerResponse::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToServerResponse.data)
  return data_;
}
inline void TransferToServerResponse::set_allocated_data(::xla::GlobalDataHandle* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToServerResponse.data)
}

// -------------------------------------------------------------------

// TransferToInfeedRequest

// .xla.LiteralProto literal = 1;
inline bool TransferToInfeedRequest::has_literal() const {
  return this != internal_default_instance() && literal_ != nullptr;
}
inline const ::xla::LiteralProto& TransferToInfeedRequest::literal() const {
  const ::xla::LiteralProto* p = literal_;
  // @@protoc_insertion_point(field_get:xla.TransferToInfeedRequest.literal)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* TransferToInfeedRequest::release_literal() {
  // @@protoc_insertion_point(field_release:xla.TransferToInfeedRequest.literal)
  
  ::xla::LiteralProto* temp = literal_;
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* TransferToInfeedRequest::mutable_literal() {
  
  if (literal_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    literal_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToInfeedRequest.literal)
  return literal_;
}
inline void TransferToInfeedRequest::set_allocated_literal(::xla::LiteralProto* literal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal_);
  }
  if (literal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal)->GetArena();
    if (message_arena != submessage_arena) {
      literal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, literal, submessage_arena);
    }
    
  } else {
    
  }
  literal_ = literal;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToInfeedRequest.literal)
}

// int64 replica_id = 2;
inline void TransferToInfeedRequest::clear_replica_id() {
  replica_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TransferToInfeedRequest::replica_id() const {
  // @@protoc_insertion_point(field_get:xla.TransferToInfeedRequest.replica_id)
  return replica_id_;
}
inline void TransferToInfeedRequest::set_replica_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  replica_id_ = value;
  // @@protoc_insertion_point(field_set:xla.TransferToInfeedRequest.replica_id)
}

// .xla.DeviceHandle device_handle = 3;
inline bool TransferToInfeedRequest::has_device_handle() const {
  return this != internal_default_instance() && device_handle_ != nullptr;
}
inline const ::xla::DeviceHandle& TransferToInfeedRequest::device_handle() const {
  const ::xla::DeviceHandle* p = device_handle_;
  // @@protoc_insertion_point(field_get:xla.TransferToInfeedRequest.device_handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DeviceHandle*>(
      &::xla::_DeviceHandle_default_instance_);
}
inline ::xla::DeviceHandle* TransferToInfeedRequest::release_device_handle() {
  // @@protoc_insertion_point(field_release:xla.TransferToInfeedRequest.device_handle)
  
  ::xla::DeviceHandle* temp = device_handle_;
  device_handle_ = nullptr;
  return temp;
}
inline ::xla::DeviceHandle* TransferToInfeedRequest::mutable_device_handle() {
  
  if (device_handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceHandle>(GetArenaNoVirtual());
    device_handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferToInfeedRequest.device_handle)
  return device_handle_;
}
inline void TransferToInfeedRequest::set_allocated_device_handle(::xla::DeviceHandle* device_handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle_);
  }
  if (device_handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle)->GetArena();
    if (message_arena != submessage_arena) {
      device_handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_handle, submessage_arena);
    }
    
  } else {
    
  }
  device_handle_ = device_handle;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferToInfeedRequest.device_handle)
}

// -------------------------------------------------------------------

// TransferToInfeedResponse

// -------------------------------------------------------------------

// TransferFromOutfeedRequest

// .xla.ShapeProto shape_with_layout = 1;
inline bool TransferFromOutfeedRequest::has_shape_with_layout() const {
  return this != internal_default_instance() && shape_with_layout_ != nullptr;
}
inline const ::xla::ShapeProto& TransferFromOutfeedRequest::shape_with_layout() const {
  const ::xla::ShapeProto* p = shape_with_layout_;
  // @@protoc_insertion_point(field_get:xla.TransferFromOutfeedRequest.shape_with_layout)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* TransferFromOutfeedRequest::release_shape_with_layout() {
  // @@protoc_insertion_point(field_release:xla.TransferFromOutfeedRequest.shape_with_layout)
  
  ::xla::ShapeProto* temp = shape_with_layout_;
  shape_with_layout_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* TransferFromOutfeedRequest::mutable_shape_with_layout() {
  
  if (shape_with_layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_with_layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferFromOutfeedRequest.shape_with_layout)
  return shape_with_layout_;
}
inline void TransferFromOutfeedRequest::set_allocated_shape_with_layout(::xla::ShapeProto* shape_with_layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_with_layout_);
  }
  if (shape_with_layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_with_layout)->GetArena();
    if (message_arena != submessage_arena) {
      shape_with_layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape_with_layout, submessage_arena);
    }
    
  } else {
    
  }
  shape_with_layout_ = shape_with_layout;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferFromOutfeedRequest.shape_with_layout)
}

// int64 replica_id = 2;
inline void TransferFromOutfeedRequest::clear_replica_id() {
  replica_id_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 TransferFromOutfeedRequest::replica_id() const {
  // @@protoc_insertion_point(field_get:xla.TransferFromOutfeedRequest.replica_id)
  return replica_id_;
}
inline void TransferFromOutfeedRequest::set_replica_id(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  replica_id_ = value;
  // @@protoc_insertion_point(field_set:xla.TransferFromOutfeedRequest.replica_id)
}

// .xla.DeviceHandle device_handle = 3;
inline bool TransferFromOutfeedRequest::has_device_handle() const {
  return this != internal_default_instance() && device_handle_ != nullptr;
}
inline const ::xla::DeviceHandle& TransferFromOutfeedRequest::device_handle() const {
  const ::xla::DeviceHandle* p = device_handle_;
  // @@protoc_insertion_point(field_get:xla.TransferFromOutfeedRequest.device_handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DeviceHandle*>(
      &::xla::_DeviceHandle_default_instance_);
}
inline ::xla::DeviceHandle* TransferFromOutfeedRequest::release_device_handle() {
  // @@protoc_insertion_point(field_release:xla.TransferFromOutfeedRequest.device_handle)
  
  ::xla::DeviceHandle* temp = device_handle_;
  device_handle_ = nullptr;
  return temp;
}
inline ::xla::DeviceHandle* TransferFromOutfeedRequest::mutable_device_handle() {
  
  if (device_handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceHandle>(GetArenaNoVirtual());
    device_handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferFromOutfeedRequest.device_handle)
  return device_handle_;
}
inline void TransferFromOutfeedRequest::set_allocated_device_handle(::xla::DeviceHandle* device_handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle_);
  }
  if (device_handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle)->GetArena();
    if (message_arena != submessage_arena) {
      device_handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_handle, submessage_arena);
    }
    
  } else {
    
  }
  device_handle_ = device_handle;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferFromOutfeedRequest.device_handle)
}

// -------------------------------------------------------------------

// TransferFromOutfeedResponse

// .xla.LiteralProto literal = 1;
inline bool TransferFromOutfeedResponse::has_literal() const {
  return this != internal_default_instance() && literal_ != nullptr;
}
inline const ::xla::LiteralProto& TransferFromOutfeedResponse::literal() const {
  const ::xla::LiteralProto* p = literal_;
  // @@protoc_insertion_point(field_get:xla.TransferFromOutfeedResponse.literal)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* TransferFromOutfeedResponse::release_literal() {
  // @@protoc_insertion_point(field_release:xla.TransferFromOutfeedResponse.literal)
  
  ::xla::LiteralProto* temp = literal_;
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* TransferFromOutfeedResponse::mutable_literal() {
  
  if (literal_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    literal_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.TransferFromOutfeedResponse.literal)
  return literal_;
}
inline void TransferFromOutfeedResponse::set_allocated_literal(::xla::LiteralProto* literal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal_);
  }
  if (literal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal)->GetArena();
    if (message_arena != submessage_arena) {
      literal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, literal, submessage_arena);
    }
    
  } else {
    
  }
  literal_ = literal;
  // @@protoc_insertion_point(field_set_allocated:xla.TransferFromOutfeedResponse.literal)
}

// -------------------------------------------------------------------

// ResetDeviceRequest

// .xla.DeviceHandle device_handle = 1;
inline bool ResetDeviceRequest::has_device_handle() const {
  return this != internal_default_instance() && device_handle_ != nullptr;
}
inline const ::xla::DeviceHandle& ResetDeviceRequest::device_handle() const {
  const ::xla::DeviceHandle* p = device_handle_;
  // @@protoc_insertion_point(field_get:xla.ResetDeviceRequest.device_handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DeviceHandle*>(
      &::xla::_DeviceHandle_default_instance_);
}
inline ::xla::DeviceHandle* ResetDeviceRequest::release_device_handle() {
  // @@protoc_insertion_point(field_release:xla.ResetDeviceRequest.device_handle)
  
  ::xla::DeviceHandle* temp = device_handle_;
  device_handle_ = nullptr;
  return temp;
}
inline ::xla::DeviceHandle* ResetDeviceRequest::mutable_device_handle() {
  
  if (device_handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceHandle>(GetArenaNoVirtual());
    device_handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ResetDeviceRequest.device_handle)
  return device_handle_;
}
inline void ResetDeviceRequest::set_allocated_device_handle(::xla::DeviceHandle* device_handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle_);
  }
  if (device_handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_handle)->GetArena();
    if (message_arena != submessage_arena) {
      device_handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_handle, submessage_arena);
    }
    
  } else {
    
  }
  device_handle_ = device_handle;
  // @@protoc_insertion_point(field_set_allocated:xla.ResetDeviceRequest.device_handle)
}

// -------------------------------------------------------------------

// ResetDeviceResponse

// -------------------------------------------------------------------

// ComputationGraphStatsRequest

// .xla.HloModuleProto computation = 1;
inline bool ComputationGraphStatsRequest::has_computation() const {
  return this != internal_default_instance() && computation_ != nullptr;
}
inline const ::xla::HloModuleProto& ComputationGraphStatsRequest::computation() const {
  const ::xla::HloModuleProto* p = computation_;
  // @@protoc_insertion_point(field_get:xla.ComputationGraphStatsRequest.computation)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloModuleProto*>(
      &::xla::_HloModuleProto_default_instance_);
}
inline ::xla::HloModuleProto* ComputationGraphStatsRequest::release_computation() {
  // @@protoc_insertion_point(field_release:xla.ComputationGraphStatsRequest.computation)
  
  ::xla::HloModuleProto* temp = computation_;
  computation_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* ComputationGraphStatsRequest::mutable_computation() {
  
  if (computation_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaNoVirtual());
    computation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ComputationGraphStatsRequest.computation)
  return computation_;
}
inline void ComputationGraphStatsRequest::set_allocated_computation(::xla::HloModuleProto* computation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation_);
  }
  if (computation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation)->GetArena();
    if (message_arena != submessage_arena) {
      computation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, computation, submessage_arena);
    }
    
  } else {
    
  }
  computation_ = computation;
  // @@protoc_insertion_point(field_set_allocated:xla.ComputationGraphStatsRequest.computation)
}

// .xla.DebugOptions debug_options = 2;
inline bool ComputationGraphStatsRequest::has_debug_options() const {
  return this != internal_default_instance() && debug_options_ != nullptr;
}
inline void ComputationGraphStatsRequest::clear_debug_options() {
  if (GetArenaNoVirtual() == nullptr && debug_options_ != nullptr) {
    delete debug_options_;
  }
  debug_options_ = nullptr;
}
inline const ::xla::DebugOptions& ComputationGraphStatsRequest::debug_options() const {
  const ::xla::DebugOptions* p = debug_options_;
  // @@protoc_insertion_point(field_get:xla.ComputationGraphStatsRequest.debug_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::DebugOptions*>(
      &::xla::_DebugOptions_default_instance_);
}
inline ::xla::DebugOptions* ComputationGraphStatsRequest::release_debug_options() {
  // @@protoc_insertion_point(field_release:xla.ComputationGraphStatsRequest.debug_options)
  
  ::xla::DebugOptions* temp = debug_options_;
  debug_options_ = nullptr;
  return temp;
}
inline ::xla::DebugOptions* ComputationGraphStatsRequest::mutable_debug_options() {
  
  if (debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DebugOptions>(GetArenaNoVirtual());
    debug_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ComputationGraphStatsRequest.debug_options)
  return debug_options_;
}
inline void ComputationGraphStatsRequest::set_allocated_debug_options(::xla::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete debug_options_;
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:xla.ComputationGraphStatsRequest.debug_options)
}

// -------------------------------------------------------------------

// ComputationStatsResponse

// .xla.ComputationStats stats = 1;
inline bool ComputationStatsResponse::has_stats() const {
  return this != internal_default_instance() && stats_ != nullptr;
}
inline const ::xla::ComputationStats& ComputationStatsResponse::stats() const {
  const ::xla::ComputationStats* p = stats_;
  // @@protoc_insertion_point(field_get:xla.ComputationStatsResponse.stats)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ComputationStats*>(
      &::xla::_ComputationStats_default_instance_);
}
inline ::xla::ComputationStats* ComputationStatsResponse::release_stats() {
  // @@protoc_insertion_point(field_release:xla.ComputationStatsResponse.stats)
  
  ::xla::ComputationStats* temp = stats_;
  stats_ = nullptr;
  return temp;
}
inline ::xla::ComputationStats* ComputationStatsResponse::mutable_stats() {
  
  if (stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ComputationStats>(GetArenaNoVirtual());
    stats_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ComputationStatsResponse.stats)
  return stats_;
}
inline void ComputationStatsResponse::set_allocated_stats(::xla::ComputationStats* stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(stats_);
  }
  if (stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(stats)->GetArena();
    if (message_arena != submessage_arena) {
      stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stats, submessage_arena);
    }
    
  } else {
    
  }
  stats_ = stats;
  // @@protoc_insertion_point(field_set_allocated:xla.ComputationStatsResponse.stats)
}

// -------------------------------------------------------------------

// CreateChannelHandleRequest

// .xla.ChannelHandle.ChannelType channel_type = 1;
inline void CreateChannelHandleRequest::clear_channel_type() {
  channel_type_ = 0;
}
inline ::xla::ChannelHandle_ChannelType CreateChannelHandleRequest::channel_type() const {
  // @@protoc_insertion_point(field_get:xla.CreateChannelHandleRequest.channel_type)
  return static_cast< ::xla::ChannelHandle_ChannelType >(channel_type_);
}
inline void CreateChannelHandleRequest::set_channel_type(::xla::ChannelHandle_ChannelType value) {
  
  channel_type_ = value;
  // @@protoc_insertion_point(field_set:xla.CreateChannelHandleRequest.channel_type)
}

// -------------------------------------------------------------------

// CreateChannelHandleResponse

// .xla.ChannelHandle channel = 1;
inline bool CreateChannelHandleResponse::has_channel() const {
  return this != internal_default_instance() && channel_ != nullptr;
}
inline const ::xla::ChannelHandle& CreateChannelHandleResponse::channel() const {
  const ::xla::ChannelHandle* p = channel_;
  // @@protoc_insertion_point(field_get:xla.CreateChannelHandleResponse.channel)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ChannelHandle*>(
      &::xla::_ChannelHandle_default_instance_);
}
inline ::xla::ChannelHandle* CreateChannelHandleResponse::release_channel() {
  // @@protoc_insertion_point(field_release:xla.CreateChannelHandleResponse.channel)
  
  ::xla::ChannelHandle* temp = channel_;
  channel_ = nullptr;
  return temp;
}
inline ::xla::ChannelHandle* CreateChannelHandleResponse::mutable_channel() {
  
  if (channel_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ChannelHandle>(GetArenaNoVirtual());
    channel_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.CreateChannelHandleResponse.channel)
  return channel_;
}
inline void CreateChannelHandleResponse::set_allocated_channel(::xla::ChannelHandle* channel) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(channel_);
  }
  if (channel) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(channel)->GetArena();
    if (message_arena != submessage_arena) {
      channel = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, channel, submessage_arena);
    }
    
  } else {
    
  }
  channel_ = channel;
  // @@protoc_insertion_point(field_set_allocated:xla.CreateChannelHandleResponse.channel)
}

// -------------------------------------------------------------------

// UnregisterRequest

// repeated .xla.GlobalDataHandle data = 1;
inline int UnregisterRequest::data_size() const {
  return data_.size();
}
inline ::xla::GlobalDataHandle* UnregisterRequest::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:xla.UnregisterRequest.data)
  return data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
UnregisterRequest::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:xla.UnregisterRequest.data)
  return &data_;
}
inline const ::xla::GlobalDataHandle& UnregisterRequest::data(int index) const {
  // @@protoc_insertion_point(field_get:xla.UnregisterRequest.data)
  return data_.Get(index);
}
inline ::xla::GlobalDataHandle* UnregisterRequest::add_data() {
  // @@protoc_insertion_point(field_add:xla.UnregisterRequest.data)
  return data_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
UnregisterRequest::data() const {
  // @@protoc_insertion_point(field_list:xla.UnregisterRequest.data)
  return data_;
}

// -------------------------------------------------------------------

// UnregisterResponse

// -------------------------------------------------------------------

// CompileRequest

// .xla.HloModuleProto computation = 1;
inline bool CompileRequest::has_computation() const {
  return this != internal_default_instance() && computation_ != nullptr;
}
inline const ::xla::HloModuleProto& CompileRequest::computation() const {
  const ::xla::HloModuleProto* p = computation_;
  // @@protoc_insertion_point(field_get:xla.CompileRequest.computation)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloModuleProto*>(
      &::xla::_HloModuleProto_default_instance_);
}
inline ::xla::HloModuleProto* CompileRequest::release_computation() {
  // @@protoc_insertion_point(field_release:xla.CompileRequest.computation)
  
  ::xla::HloModuleProto* temp = computation_;
  computation_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* CompileRequest::mutable_computation() {
  
  if (computation_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaNoVirtual());
    computation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.CompileRequest.computation)
  return computation_;
}
inline void CompileRequest::set_allocated_computation(::xla::HloModuleProto* computation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation_);
  }
  if (computation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation)->GetArena();
    if (message_arena != submessage_arena) {
      computation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, computation, submessage_arena);
    }
    
  } else {
    
  }
  computation_ = computation;
  // @@protoc_insertion_point(field_set_allocated:xla.CompileRequest.computation)
}

// .xla.ExecutionOptions execution_options = 2;
inline bool CompileRequest::has_execution_options() const {
  return this != internal_default_instance() && execution_options_ != nullptr;
}
inline void CompileRequest::clear_execution_options() {
  if (GetArenaNoVirtual() == nullptr && execution_options_ != nullptr) {
    delete execution_options_;
  }
  execution_options_ = nullptr;
}
inline const ::xla::ExecutionOptions& CompileRequest::execution_options() const {
  const ::xla::ExecutionOptions* p = execution_options_;
  // @@protoc_insertion_point(field_get:xla.CompileRequest.execution_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionOptions*>(
      &::xla::_ExecutionOptions_default_instance_);
}
inline ::xla::ExecutionOptions* CompileRequest::release_execution_options() {
  // @@protoc_insertion_point(field_release:xla.CompileRequest.execution_options)
  
  ::xla::ExecutionOptions* temp = execution_options_;
  execution_options_ = nullptr;
  return temp;
}
inline ::xla::ExecutionOptions* CompileRequest::mutable_execution_options() {
  
  if (execution_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionOptions>(GetArenaNoVirtual());
    execution_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.CompileRequest.execution_options)
  return execution_options_;
}
inline void CompileRequest::set_allocated_execution_options(::xla::ExecutionOptions* execution_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete execution_options_;
  }
  if (execution_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      execution_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, execution_options, submessage_arena);
    }
    
  } else {
    
  }
  execution_options_ = execution_options;
  // @@protoc_insertion_point(field_set_allocated:xla.CompileRequest.execution_options)
}

// repeated .xla.ShapeProto input_shape_with_layout = 3;
inline int CompileRequest::input_shape_with_layout_size() const {
  return input_shape_with_layout_.size();
}
inline ::xla::ShapeProto* CompileRequest::mutable_input_shape_with_layout(int index) {
  // @@protoc_insertion_point(field_mutable:xla.CompileRequest.input_shape_with_layout)
  return input_shape_with_layout_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
CompileRequest::mutable_input_shape_with_layout() {
  // @@protoc_insertion_point(field_mutable_list:xla.CompileRequest.input_shape_with_layout)
  return &input_shape_with_layout_;
}
inline const ::xla::ShapeProto& CompileRequest::input_shape_with_layout(int index) const {
  // @@protoc_insertion_point(field_get:xla.CompileRequest.input_shape_with_layout)
  return input_shape_with_layout_.Get(index);
}
inline ::xla::ShapeProto* CompileRequest::add_input_shape_with_layout() {
  // @@protoc_insertion_point(field_add:xla.CompileRequest.input_shape_with_layout)
  return input_shape_with_layout_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
CompileRequest::input_shape_with_layout() const {
  // @@protoc_insertion_point(field_list:xla.CompileRequest.input_shape_with_layout)
  return input_shape_with_layout_;
}

// -------------------------------------------------------------------

// CompileResponse

// .xla.ExecutionHandle handle = 1;
inline bool CompileResponse::has_handle() const {
  return this != internal_default_instance() && handle_ != nullptr;
}
inline const ::xla::ExecutionHandle& CompileResponse::handle() const {
  const ::xla::ExecutionHandle* p = handle_;
  // @@protoc_insertion_point(field_get:xla.CompileResponse.handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionHandle*>(
      &::xla::_ExecutionHandle_default_instance_);
}
inline ::xla::ExecutionHandle* CompileResponse::release_handle() {
  // @@protoc_insertion_point(field_release:xla.CompileResponse.handle)
  
  ::xla::ExecutionHandle* temp = handle_;
  handle_ = nullptr;
  return temp;
}
inline ::xla::ExecutionHandle* CompileResponse::mutable_handle() {
  
  if (handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionHandle>(GetArenaNoVirtual());
    handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.CompileResponse.handle)
  return handle_;
}
inline void CompileResponse::set_allocated_handle(::xla::ExecutionHandle* handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(handle_);
  }
  if (handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(handle)->GetArena();
    if (message_arena != submessage_arena) {
      handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, handle, submessage_arena);
    }
    
  } else {
    
  }
  handle_ = handle;
  // @@protoc_insertion_point(field_set_allocated:xla.CompileResponse.handle)
}

// -------------------------------------------------------------------

// ExecuteRequest

// .xla.ExecutionHandle handle = 1;
inline bool ExecuteRequest::has_handle() const {
  return this != internal_default_instance() && handle_ != nullptr;
}
inline const ::xla::ExecutionHandle& ExecuteRequest::handle() const {
  const ::xla::ExecutionHandle* p = handle_;
  // @@protoc_insertion_point(field_get:xla.ExecuteRequest.handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionHandle*>(
      &::xla::_ExecutionHandle_default_instance_);
}
inline ::xla::ExecutionHandle* ExecuteRequest::release_handle() {
  // @@protoc_insertion_point(field_release:xla.ExecuteRequest.handle)
  
  ::xla::ExecutionHandle* temp = handle_;
  handle_ = nullptr;
  return temp;
}
inline ::xla::ExecutionHandle* ExecuteRequest::mutable_handle() {
  
  if (handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionHandle>(GetArenaNoVirtual());
    handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecuteRequest.handle)
  return handle_;
}
inline void ExecuteRequest::set_allocated_handle(::xla::ExecutionHandle* handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(handle_);
  }
  if (handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(handle)->GetArena();
    if (message_arena != submessage_arena) {
      handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, handle, submessage_arena);
    }
    
  } else {
    
  }
  handle_ = handle;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecuteRequest.handle)
}

// repeated .xla.GlobalDataHandle arguments = 2;
inline int ExecuteRequest::arguments_size() const {
  return arguments_.size();
}
inline ::xla::GlobalDataHandle* ExecuteRequest::mutable_arguments(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ExecuteRequest.arguments)
  return arguments_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
ExecuteRequest::mutable_arguments() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecuteRequest.arguments)
  return &arguments_;
}
inline const ::xla::GlobalDataHandle& ExecuteRequest::arguments(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecuteRequest.arguments)
  return arguments_.Get(index);
}
inline ::xla::GlobalDataHandle* ExecuteRequest::add_arguments() {
  // @@protoc_insertion_point(field_add:xla.ExecuteRequest.arguments)
  return arguments_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
ExecuteRequest::arguments() const {
  // @@protoc_insertion_point(field_list:xla.ExecuteRequest.arguments)
  return arguments_;
}

// -------------------------------------------------------------------

// ExecuteGraphRequest

// .xla.HloModuleProto computation = 1;
inline bool ExecuteGraphRequest::has_computation() const {
  return this != internal_default_instance() && computation_ != nullptr;
}
inline const ::xla::HloModuleProto& ExecuteGraphRequest::computation() const {
  const ::xla::HloModuleProto* p = computation_;
  // @@protoc_insertion_point(field_get:xla.ExecuteGraphRequest.computation)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloModuleProto*>(
      &::xla::_HloModuleProto_default_instance_);
}
inline ::xla::HloModuleProto* ExecuteGraphRequest::release_computation() {
  // @@protoc_insertion_point(field_release:xla.ExecuteGraphRequest.computation)
  
  ::xla::HloModuleProto* temp = computation_;
  computation_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* ExecuteGraphRequest::mutable_computation() {
  
  if (computation_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaNoVirtual());
    computation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecuteGraphRequest.computation)
  return computation_;
}
inline void ExecuteGraphRequest::set_allocated_computation(::xla::HloModuleProto* computation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation_);
  }
  if (computation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation)->GetArena();
    if (message_arena != submessage_arena) {
      computation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, computation, submessage_arena);
    }
    
  } else {
    
  }
  computation_ = computation;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecuteGraphRequest.computation)
}

// repeated .xla.GlobalDataHandle arguments = 2;
inline int ExecuteGraphRequest::arguments_size() const {
  return arguments_.size();
}
inline ::xla::GlobalDataHandle* ExecuteGraphRequest::mutable_arguments(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ExecuteGraphRequest.arguments)
  return arguments_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
ExecuteGraphRequest::mutable_arguments() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecuteGraphRequest.arguments)
  return &arguments_;
}
inline const ::xla::GlobalDataHandle& ExecuteGraphRequest::arguments(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecuteGraphRequest.arguments)
  return arguments_.Get(index);
}
inline ::xla::GlobalDataHandle* ExecuteGraphRequest::add_arguments() {
  // @@protoc_insertion_point(field_add:xla.ExecuteGraphRequest.arguments)
  return arguments_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
ExecuteGraphRequest::arguments() const {
  // @@protoc_insertion_point(field_list:xla.ExecuteGraphRequest.arguments)
  return arguments_;
}

// .xla.ExecutionOptions execution_options = 3;
inline bool ExecuteGraphRequest::has_execution_options() const {
  return this != internal_default_instance() && execution_options_ != nullptr;
}
inline void ExecuteGraphRequest::clear_execution_options() {
  if (GetArenaNoVirtual() == nullptr && execution_options_ != nullptr) {
    delete execution_options_;
  }
  execution_options_ = nullptr;
}
inline const ::xla::ExecutionOptions& ExecuteGraphRequest::execution_options() const {
  const ::xla::ExecutionOptions* p = execution_options_;
  // @@protoc_insertion_point(field_get:xla.ExecuteGraphRequest.execution_options)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionOptions*>(
      &::xla::_ExecutionOptions_default_instance_);
}
inline ::xla::ExecutionOptions* ExecuteGraphRequest::release_execution_options() {
  // @@protoc_insertion_point(field_release:xla.ExecuteGraphRequest.execution_options)
  
  ::xla::ExecutionOptions* temp = execution_options_;
  execution_options_ = nullptr;
  return temp;
}
inline ::xla::ExecutionOptions* ExecuteGraphRequest::mutable_execution_options() {
  
  if (execution_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionOptions>(GetArenaNoVirtual());
    execution_options_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecuteGraphRequest.execution_options)
  return execution_options_;
}
inline void ExecuteGraphRequest::set_allocated_execution_options(::xla::ExecutionOptions* execution_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete execution_options_;
  }
  if (execution_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      execution_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, execution_options, submessage_arena);
    }
    
  } else {
    
  }
  execution_options_ = execution_options;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecuteGraphRequest.execution_options)
}

// -------------------------------------------------------------------

// ExecuteGraphParallelRequest

// repeated .xla.ExecuteGraphRequest requests = 1;
inline int ExecuteGraphParallelRequest::requests_size() const {
  return requests_.size();
}
inline void ExecuteGraphParallelRequest::clear_requests() {
  requests_.Clear();
}
inline ::xla::ExecuteGraphRequest* ExecuteGraphParallelRequest::mutable_requests(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ExecuteGraphParallelRequest.requests)
  return requests_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteGraphRequest >*
ExecuteGraphParallelRequest::mutable_requests() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecuteGraphParallelRequest.requests)
  return &requests_;
}
inline const ::xla::ExecuteGraphRequest& ExecuteGraphParallelRequest::requests(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecuteGraphParallelRequest.requests)
  return requests_.Get(index);
}
inline ::xla::ExecuteGraphRequest* ExecuteGraphParallelRequest::add_requests() {
  // @@protoc_insertion_point(field_add:xla.ExecuteGraphParallelRequest.requests)
  return requests_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteGraphRequest >&
ExecuteGraphParallelRequest::requests() const {
  // @@protoc_insertion_point(field_list:xla.ExecuteGraphParallelRequest.requests)
  return requests_;
}

// -------------------------------------------------------------------

// ExecuteResponse

// .xla.GlobalDataHandle output = 1;
inline bool ExecuteResponse::has_output() const {
  return this != internal_default_instance() && output_ != nullptr;
}
inline const ::xla::GlobalDataHandle& ExecuteResponse::output() const {
  const ::xla::GlobalDataHandle* p = output_;
  // @@protoc_insertion_point(field_get:xla.ExecuteResponse.output)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* ExecuteResponse::release_output() {
  // @@protoc_insertion_point(field_release:xla.ExecuteResponse.output)
  
  ::xla::GlobalDataHandle* temp = output_;
  output_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* ExecuteResponse::mutable_output() {
  
  if (output_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    output_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecuteResponse.output)
  return output_;
}
inline void ExecuteResponse::set_allocated_output(::xla::GlobalDataHandle* output) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_);
  }
  if (output) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(output)->GetArena();
    if (message_arena != submessage_arena) {
      output = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output, submessage_arena);
    }
    
  } else {
    
  }
  output_ = output;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecuteResponse.output)
}

// .xla.ExecutionProfile profile = 2;
inline bool ExecuteResponse::has_profile() const {
  return this != internal_default_instance() && profile_ != nullptr;
}
inline const ::xla::ExecutionProfile& ExecuteResponse::profile() const {
  const ::xla::ExecutionProfile* p = profile_;
  // @@protoc_insertion_point(field_get:xla.ExecuteResponse.profile)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionProfile*>(
      &::xla::_ExecutionProfile_default_instance_);
}
inline ::xla::ExecutionProfile* ExecuteResponse::release_profile() {
  // @@protoc_insertion_point(field_release:xla.ExecuteResponse.profile)
  
  ::xla::ExecutionProfile* temp = profile_;
  profile_ = nullptr;
  return temp;
}
inline ::xla::ExecutionProfile* ExecuteResponse::mutable_profile() {
  
  if (profile_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionProfile>(GetArenaNoVirtual());
    profile_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ExecuteResponse.profile)
  return profile_;
}
inline void ExecuteResponse::set_allocated_profile(::xla::ExecutionProfile* profile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  if (profile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile)->GetArena();
    if (message_arena != submessage_arena) {
      profile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, profile, submessage_arena);
    }
    
  } else {
    
  }
  profile_ = profile;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecuteResponse.profile)
}

// -------------------------------------------------------------------

// ExecuteParallelResponse

// repeated .xla.ExecuteResponse responses = 1;
inline int ExecuteParallelResponse::responses_size() const {
  return responses_.size();
}
inline void ExecuteParallelResponse::clear_responses() {
  responses_.Clear();
}
inline ::xla::ExecuteResponse* ExecuteParallelResponse::mutable_responses(int index) {
  // @@protoc_insertion_point(field_mutable:xla.ExecuteParallelResponse.responses)
  return responses_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteResponse >*
ExecuteParallelResponse::mutable_responses() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecuteParallelResponse.responses)
  return &responses_;
}
inline const ::xla::ExecuteResponse& ExecuteParallelResponse::responses(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecuteParallelResponse.responses)
  return responses_.Get(index);
}
inline ::xla::ExecuteResponse* ExecuteParallelResponse::add_responses() {
  // @@protoc_insertion_point(field_add:xla.ExecuteParallelResponse.responses)
  return responses_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ExecuteResponse >&
ExecuteParallelResponse::responses() const {
  // @@protoc_insertion_point(field_list:xla.ExecuteParallelResponse.responses)
  return responses_;
}

// -------------------------------------------------------------------

// WaitForExecutionRequest

// .xla.ExecutionHandle execution = 1;
inline bool WaitForExecutionRequest::has_execution() const {
  return this != internal_default_instance() && execution_ != nullptr;
}
inline const ::xla::ExecutionHandle& WaitForExecutionRequest::execution() const {
  const ::xla::ExecutionHandle* p = execution_;
  // @@protoc_insertion_point(field_get:xla.WaitForExecutionRequest.execution)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionHandle*>(
      &::xla::_ExecutionHandle_default_instance_);
}
inline ::xla::ExecutionHandle* WaitForExecutionRequest::release_execution() {
  // @@protoc_insertion_point(field_release:xla.WaitForExecutionRequest.execution)
  
  ::xla::ExecutionHandle* temp = execution_;
  execution_ = nullptr;
  return temp;
}
inline ::xla::ExecutionHandle* WaitForExecutionRequest::mutable_execution() {
  
  if (execution_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionHandle>(GetArenaNoVirtual());
    execution_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.WaitForExecutionRequest.execution)
  return execution_;
}
inline void WaitForExecutionRequest::set_allocated_execution(::xla::ExecutionHandle* execution) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(execution_);
  }
  if (execution) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(execution)->GetArena();
    if (message_arena != submessage_arena) {
      execution = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, execution, submessage_arena);
    }
    
  } else {
    
  }
  execution_ = execution;
  // @@protoc_insertion_point(field_set_allocated:xla.WaitForExecutionRequest.execution)
}

// -------------------------------------------------------------------

// WaitForExecutionResponse

// .xla.GlobalDataHandle output = 1;
inline bool WaitForExecutionResponse::has_output() const {
  return this != internal_default_instance() && output_ != nullptr;
}
inline const ::xla::GlobalDataHandle& WaitForExecutionResponse::output() const {
  const ::xla::GlobalDataHandle* p = output_;
  // @@protoc_insertion_point(field_get:xla.WaitForExecutionResponse.output)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* WaitForExecutionResponse::release_output() {
  // @@protoc_insertion_point(field_release:xla.WaitForExecutionResponse.output)
  
  ::xla::GlobalDataHandle* temp = output_;
  output_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* WaitForExecutionResponse::mutable_output() {
  
  if (output_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    output_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.WaitForExecutionResponse.output)
  return output_;
}
inline void WaitForExecutionResponse::set_allocated_output(::xla::GlobalDataHandle* output) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_);
  }
  if (output) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(output)->GetArena();
    if (message_arena != submessage_arena) {
      output = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output, submessage_arena);
    }
    
  } else {
    
  }
  output_ = output;
  // @@protoc_insertion_point(field_set_allocated:xla.WaitForExecutionResponse.output)
}

// .xla.ExecutionProfile profile = 2;
inline bool WaitForExecutionResponse::has_profile() const {
  return this != internal_default_instance() && profile_ != nullptr;
}
inline const ::xla::ExecutionProfile& WaitForExecutionResponse::profile() const {
  const ::xla::ExecutionProfile* p = profile_;
  // @@protoc_insertion_point(field_get:xla.WaitForExecutionResponse.profile)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ExecutionProfile*>(
      &::xla::_ExecutionProfile_default_instance_);
}
inline ::xla::ExecutionProfile* WaitForExecutionResponse::release_profile() {
  // @@protoc_insertion_point(field_release:xla.WaitForExecutionResponse.profile)
  
  ::xla::ExecutionProfile* temp = profile_;
  profile_ = nullptr;
  return temp;
}
inline ::xla::ExecutionProfile* WaitForExecutionResponse::mutable_profile() {
  
  if (profile_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutionProfile>(GetArenaNoVirtual());
    profile_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.WaitForExecutionResponse.profile)
  return profile_;
}
inline void WaitForExecutionResponse::set_allocated_profile(::xla::ExecutionProfile* profile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile_);
  }
  if (profile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(profile)->GetArena();
    if (message_arena != submessage_arena) {
      profile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, profile, submessage_arena);
    }
    
  } else {
    
  }
  profile_ = profile;
  // @@protoc_insertion_point(field_set_allocated:xla.WaitForExecutionResponse.profile)
}

// -------------------------------------------------------------------

// ComputeConstantGraphRequest

// .xla.HloModuleProto computation = 1;
inline bool ComputeConstantGraphRequest::has_computation() const {
  return this != internal_default_instance() && computation_ != nullptr;
}
inline const ::xla::HloModuleProto& ComputeConstantGraphRequest::computation() const {
  const ::xla::HloModuleProto* p = computation_;
  // @@protoc_insertion_point(field_get:xla.ComputeConstantGraphRequest.computation)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::HloModuleProto*>(
      &::xla::_HloModuleProto_default_instance_);
}
inline ::xla::HloModuleProto* ComputeConstantGraphRequest::release_computation() {
  // @@protoc_insertion_point(field_release:xla.ComputeConstantGraphRequest.computation)
  
  ::xla::HloModuleProto* temp = computation_;
  computation_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* ComputeConstantGraphRequest::mutable_computation() {
  
  if (computation_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaNoVirtual());
    computation_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ComputeConstantGraphRequest.computation)
  return computation_;
}
inline void ComputeConstantGraphRequest::set_allocated_computation(::xla::HloModuleProto* computation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation_);
  }
  if (computation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(computation)->GetArena();
    if (message_arena != submessage_arena) {
      computation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, computation, submessage_arena);
    }
    
  } else {
    
  }
  computation_ = computation;
  // @@protoc_insertion_point(field_set_allocated:xla.ComputeConstantGraphRequest.computation)
}

// .xla.LayoutProto output_layout = 2;
inline bool ComputeConstantGraphRequest::has_output_layout() const {
  return this != internal_default_instance() && output_layout_ != nullptr;
}
inline const ::xla::LayoutProto& ComputeConstantGraphRequest::output_layout() const {
  const ::xla::LayoutProto* p = output_layout_;
  // @@protoc_insertion_point(field_get:xla.ComputeConstantGraphRequest.output_layout)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LayoutProto*>(
      &::xla::_LayoutProto_default_instance_);
}
inline ::xla::LayoutProto* ComputeConstantGraphRequest::release_output_layout() {
  // @@protoc_insertion_point(field_release:xla.ComputeConstantGraphRequest.output_layout)
  
  ::xla::LayoutProto* temp = output_layout_;
  output_layout_ = nullptr;
  return temp;
}
inline ::xla::LayoutProto* ComputeConstantGraphRequest::mutable_output_layout() {
  
  if (output_layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LayoutProto>(GetArenaNoVirtual());
    output_layout_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ComputeConstantGraphRequest.output_layout)
  return output_layout_;
}
inline void ComputeConstantGraphRequest::set_allocated_output_layout(::xla::LayoutProto* output_layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_layout_);
  }
  if (output_layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_layout)->GetArena();
    if (message_arena != submessage_arena) {
      output_layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output_layout, submessage_arena);
    }
    
  } else {
    
  }
  output_layout_ = output_layout;
  // @@protoc_insertion_point(field_set_allocated:xla.ComputeConstantGraphRequest.output_layout)
}

// -------------------------------------------------------------------

// ComputeConstantResponse

// .xla.LiteralProto literal = 1;
inline bool ComputeConstantResponse::has_literal() const {
  return this != internal_default_instance() && literal_ != nullptr;
}
inline const ::xla::LiteralProto& ComputeConstantResponse::literal() const {
  const ::xla::LiteralProto* p = literal_;
  // @@protoc_insertion_point(field_get:xla.ComputeConstantResponse.literal)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::LiteralProto*>(
      &::xla::_LiteralProto_default_instance_);
}
inline ::xla::LiteralProto* ComputeConstantResponse::release_literal() {
  // @@protoc_insertion_point(field_release:xla.ComputeConstantResponse.literal)
  
  ::xla::LiteralProto* temp = literal_;
  literal_ = nullptr;
  return temp;
}
inline ::xla::LiteralProto* ComputeConstantResponse::mutable_literal() {
  
  if (literal_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::LiteralProto>(GetArenaNoVirtual());
    literal_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.ComputeConstantResponse.literal)
  return literal_;
}
inline void ComputeConstantResponse::set_allocated_literal(::xla::LiteralProto* literal) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal_);
  }
  if (literal) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(literal)->GetArena();
    if (message_arena != submessage_arena) {
      literal = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, literal, submessage_arena);
    }
    
  } else {
    
  }
  literal_ = literal;
  // @@protoc_insertion_point(field_set_allocated:xla.ComputeConstantResponse.literal)
}

// -------------------------------------------------------------------

// DeconstructTupleRequest

// .xla.GlobalDataHandle tuple_handle = 2;
inline bool DeconstructTupleRequest::has_tuple_handle() const {
  return this != internal_default_instance() && tuple_handle_ != nullptr;
}
inline const ::xla::GlobalDataHandle& DeconstructTupleRequest::tuple_handle() const {
  const ::xla::GlobalDataHandle* p = tuple_handle_;
  // @@protoc_insertion_point(field_get:xla.DeconstructTupleRequest.tuple_handle)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* DeconstructTupleRequest::release_tuple_handle() {
  // @@protoc_insertion_point(field_release:xla.DeconstructTupleRequest.tuple_handle)
  
  ::xla::GlobalDataHandle* temp = tuple_handle_;
  tuple_handle_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* DeconstructTupleRequest::mutable_tuple_handle() {
  
  if (tuple_handle_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    tuple_handle_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.DeconstructTupleRequest.tuple_handle)
  return tuple_handle_;
}
inline void DeconstructTupleRequest::set_allocated_tuple_handle(::xla::GlobalDataHandle* tuple_handle) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(tuple_handle_);
  }
  if (tuple_handle) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tuple_handle)->GetArena();
    if (message_arena != submessage_arena) {
      tuple_handle = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tuple_handle, submessage_arena);
    }
    
  } else {
    
  }
  tuple_handle_ = tuple_handle;
  // @@protoc_insertion_point(field_set_allocated:xla.DeconstructTupleRequest.tuple_handle)
}

// -------------------------------------------------------------------

// DeconstructTupleResponse

// repeated .xla.GlobalDataHandle element_handles = 1;
inline int DeconstructTupleResponse::element_handles_size() const {
  return element_handles_.size();
}
inline ::xla::GlobalDataHandle* DeconstructTupleResponse::mutable_element_handles(int index) {
  // @@protoc_insertion_point(field_mutable:xla.DeconstructTupleResponse.element_handles)
  return element_handles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
DeconstructTupleResponse::mutable_element_handles() {
  // @@protoc_insertion_point(field_mutable_list:xla.DeconstructTupleResponse.element_handles)
  return &element_handles_;
}
inline const ::xla::GlobalDataHandle& DeconstructTupleResponse::element_handles(int index) const {
  // @@protoc_insertion_point(field_get:xla.DeconstructTupleResponse.element_handles)
  return element_handles_.Get(index);
}
inline ::xla::GlobalDataHandle* DeconstructTupleResponse::add_element_handles() {
  // @@protoc_insertion_point(field_add:xla.DeconstructTupleResponse.element_handles)
  return element_handles_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
DeconstructTupleResponse::element_handles() const {
  // @@protoc_insertion_point(field_list:xla.DeconstructTupleResponse.element_handles)
  return element_handles_;
}

// -------------------------------------------------------------------

// LoadDataRequest

// string columnio_tablet_path = 1;
inline void LoadDataRequest::clear_columnio_tablet_path() {
  columnio_tablet_path_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& LoadDataRequest::columnio_tablet_path() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataRequest.columnio_tablet_path)
  return columnio_tablet_path_.GetNoArena();
}
inline void LoadDataRequest::set_columnio_tablet_path(const std::string& value) {
  
  columnio_tablet_path_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.LoadDataRequest.columnio_tablet_path)
}
inline void LoadDataRequest::set_columnio_tablet_path(std::string&& value) {
  
  columnio_tablet_path_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.LoadDataRequest.columnio_tablet_path)
}
inline void LoadDataRequest::set_columnio_tablet_path(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  columnio_tablet_path_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.LoadDataRequest.columnio_tablet_path)
}
inline void LoadDataRequest::set_columnio_tablet_path(const char* value, size_t size) {
  
  columnio_tablet_path_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.LoadDataRequest.columnio_tablet_path)
}
inline std::string* LoadDataRequest::mutable_columnio_tablet_path() {
  
  // @@protoc_insertion_point(field_mutable:xla.LoadDataRequest.columnio_tablet_path)
  return columnio_tablet_path_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* LoadDataRequest::release_columnio_tablet_path() {
  // @@protoc_insertion_point(field_release:xla.LoadDataRequest.columnio_tablet_path)
  
  return columnio_tablet_path_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void LoadDataRequest::set_allocated_columnio_tablet_path(std::string* columnio_tablet_path) {
  if (columnio_tablet_path != nullptr) {
    
  } else {
    
  }
  columnio_tablet_path_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), columnio_tablet_path);
  // @@protoc_insertion_point(field_set_allocated:xla.LoadDataRequest.columnio_tablet_path)
}

// string columnio_field = 2;
inline void LoadDataRequest::clear_columnio_field() {
  columnio_field_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& LoadDataRequest::columnio_field() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataRequest.columnio_field)
  return columnio_field_.GetNoArena();
}
inline void LoadDataRequest::set_columnio_field(const std::string& value) {
  
  columnio_field_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:xla.LoadDataRequest.columnio_field)
}
inline void LoadDataRequest::set_columnio_field(std::string&& value) {
  
  columnio_field_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:xla.LoadDataRequest.columnio_field)
}
inline void LoadDataRequest::set_columnio_field(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  columnio_field_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:xla.LoadDataRequest.columnio_field)
}
inline void LoadDataRequest::set_columnio_field(const char* value, size_t size) {
  
  columnio_field_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:xla.LoadDataRequest.columnio_field)
}
inline std::string* LoadDataRequest::mutable_columnio_field() {
  
  // @@protoc_insertion_point(field_mutable:xla.LoadDataRequest.columnio_field)
  return columnio_field_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* LoadDataRequest::release_columnio_field() {
  // @@protoc_insertion_point(field_release:xla.LoadDataRequest.columnio_field)
  
  return columnio_field_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void LoadDataRequest::set_allocated_columnio_field(std::string* columnio_field) {
  if (columnio_field != nullptr) {
    
  } else {
    
  }
  columnio_field_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), columnio_field);
  // @@protoc_insertion_point(field_set_allocated:xla.LoadDataRequest.columnio_field)
}

// .xla.ShapeProto element_shape = 3;
inline bool LoadDataRequest::has_element_shape() const {
  return this != internal_default_instance() && element_shape_ != nullptr;
}
inline const ::xla::ShapeProto& LoadDataRequest::element_shape() const {
  const ::xla::ShapeProto* p = element_shape_;
  // @@protoc_insertion_point(field_get:xla.LoadDataRequest.element_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* LoadDataRequest::release_element_shape() {
  // @@protoc_insertion_point(field_release:xla.LoadDataRequest.element_shape)
  
  ::xla::ShapeProto* temp = element_shape_;
  element_shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* LoadDataRequest::mutable_element_shape() {
  
  if (element_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    element_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.LoadDataRequest.element_shape)
  return element_shape_;
}
inline void LoadDataRequest::set_allocated_element_shape(::xla::ShapeProto* element_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(element_shape_);
  }
  if (element_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(element_shape)->GetArena();
    if (message_arena != submessage_arena) {
      element_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, element_shape, submessage_arena);
    }
    
  } else {
    
  }
  element_shape_ = element_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.LoadDataRequest.element_shape)
}

// int64 offset = 4;
inline void LoadDataRequest::clear_offset() {
  offset_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LoadDataRequest::offset() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataRequest.offset)
  return offset_;
}
inline void LoadDataRequest::set_offset(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  offset_ = value;
  // @@protoc_insertion_point(field_set:xla.LoadDataRequest.offset)
}

// int64 limit = 5;
inline void LoadDataRequest::clear_limit() {
  limit_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LoadDataRequest::limit() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataRequest.limit)
  return limit_;
}
inline void LoadDataRequest::set_limit(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  limit_ = value;
  // @@protoc_insertion_point(field_set:xla.LoadDataRequest.limit)
}

// bool zip = 6;
inline void LoadDataRequest::clear_zip() {
  zip_ = false;
}
inline bool LoadDataRequest::zip() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataRequest.zip)
  return zip_;
}
inline void LoadDataRequest::set_zip(bool value) {
  
  zip_ = value;
  // @@protoc_insertion_point(field_set:xla.LoadDataRequest.zip)
}

// -------------------------------------------------------------------

// LoadDataResponse

// .xla.GlobalDataHandle data = 1;
inline bool LoadDataResponse::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline const ::xla::GlobalDataHandle& LoadDataResponse::data() const {
  const ::xla::GlobalDataHandle* p = data_;
  // @@protoc_insertion_point(field_get:xla.LoadDataResponse.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* LoadDataResponse::release_data() {
  // @@protoc_insertion_point(field_release:xla.LoadDataResponse.data)
  
  ::xla::GlobalDataHandle* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* LoadDataResponse::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.LoadDataResponse.data)
  return data_;
}
inline void LoadDataResponse::set_allocated_data(::xla::GlobalDataHandle* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:xla.LoadDataResponse.data)
}

// .xla.ShapeProto data_shape = 2;
inline bool LoadDataResponse::has_data_shape() const {
  return this != internal_default_instance() && data_shape_ != nullptr;
}
inline const ::xla::ShapeProto& LoadDataResponse::data_shape() const {
  const ::xla::ShapeProto* p = data_shape_;
  // @@protoc_insertion_point(field_get:xla.LoadDataResponse.data_shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* LoadDataResponse::release_data_shape() {
  // @@protoc_insertion_point(field_release:xla.LoadDataResponse.data_shape)
  
  ::xla::ShapeProto* temp = data_shape_;
  data_shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* LoadDataResponse::mutable_data_shape() {
  
  if (data_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    data_shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.LoadDataResponse.data_shape)
  return data_shape_;
}
inline void LoadDataResponse::set_allocated_data_shape(::xla::ShapeProto* data_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_shape_);
  }
  if (data_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_shape)->GetArena();
    if (message_arena != submessage_arena) {
      data_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data_shape, submessage_arena);
    }
    
  } else {
    
  }
  data_shape_ = data_shape;
  // @@protoc_insertion_point(field_set_allocated:xla.LoadDataResponse.data_shape)
}

// int64 available_rows = 3;
inline void LoadDataResponse::clear_available_rows() {
  available_rows_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LoadDataResponse::available_rows() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataResponse.available_rows)
  return available_rows_;
}
inline void LoadDataResponse::set_available_rows(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  available_rows_ = value;
  // @@protoc_insertion_point(field_set:xla.LoadDataResponse.available_rows)
}

// int64 rows_loaded = 4;
inline void LoadDataResponse::clear_rows_loaded() {
  rows_loaded_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LoadDataResponse::rows_loaded() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataResponse.rows_loaded)
  return rows_loaded_;
}
inline void LoadDataResponse::set_rows_loaded(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  rows_loaded_ = value;
  // @@protoc_insertion_point(field_set:xla.LoadDataResponse.rows_loaded)
}

// int64 nanoseconds = 5;
inline void LoadDataResponse::clear_nanoseconds() {
  nanoseconds_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 LoadDataResponse::nanoseconds() const {
  // @@protoc_insertion_point(field_get:xla.LoadDataResponse.nanoseconds)
  return nanoseconds_;
}
inline void LoadDataResponse::set_nanoseconds(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  nanoseconds_ = value;
  // @@protoc_insertion_point(field_set:xla.LoadDataResponse.nanoseconds)
}

// -------------------------------------------------------------------

// GetShapeRequest

// .xla.GlobalDataHandle data = 1;
inline bool GetShapeRequest::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline const ::xla::GlobalDataHandle& GetShapeRequest::data() const {
  const ::xla::GlobalDataHandle* p = data_;
  // @@protoc_insertion_point(field_get:xla.GetShapeRequest.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* GetShapeRequest::release_data() {
  // @@protoc_insertion_point(field_release:xla.GetShapeRequest.data)
  
  ::xla::GlobalDataHandle* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* GetShapeRequest::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.GetShapeRequest.data)
  return data_;
}
inline void GetShapeRequest::set_allocated_data(::xla::GlobalDataHandle* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:xla.GetShapeRequest.data)
}

// -------------------------------------------------------------------

// GetShapeResponse

// .xla.ShapeProto shape = 1;
inline bool GetShapeResponse::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::xla::ShapeProto& GetShapeResponse::shape() const {
  const ::xla::ShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:xla.GetShapeResponse.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::ShapeProto*>(
      &::xla::_ShapeProto_default_instance_);
}
inline ::xla::ShapeProto* GetShapeResponse::release_shape() {
  // @@protoc_insertion_point(field_release:xla.GetShapeResponse.shape)
  
  ::xla::ShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* GetShapeResponse::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.GetShapeResponse.shape)
  return shape_;
}
inline void GetShapeResponse::set_allocated_shape(::xla::ShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:xla.GetShapeResponse.shape)
}

// -------------------------------------------------------------------

// UnpackRequest

// .xla.GlobalDataHandle data = 1;
inline bool UnpackRequest::has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline const ::xla::GlobalDataHandle& UnpackRequest::data() const {
  const ::xla::GlobalDataHandle* p = data_;
  // @@protoc_insertion_point(field_get:xla.UnpackRequest.data)
  return p != nullptr ? *p : *reinterpret_cast<const ::xla::GlobalDataHandle*>(
      &::xla::_GlobalDataHandle_default_instance_);
}
inline ::xla::GlobalDataHandle* UnpackRequest::release_data() {
  // @@protoc_insertion_point(field_release:xla.UnpackRequest.data)
  
  ::xla::GlobalDataHandle* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::xla::GlobalDataHandle* UnpackRequest::mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::GlobalDataHandle>(GetArenaNoVirtual());
    data_ = p;
  }
  // @@protoc_insertion_point(field_mutable:xla.UnpackRequest.data)
  return data_;
}
inline void UnpackRequest::set_allocated_data(::xla::GlobalDataHandle* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data)->GetArena();
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:xla.UnpackRequest.data)
}

// -------------------------------------------------------------------

// UnpackResponse

// repeated .xla.GlobalDataHandle tied_data = 1;
inline int UnpackResponse::tied_data_size() const {
  return tied_data_.size();
}
inline ::xla::GlobalDataHandle* UnpackResponse::mutable_tied_data(int index) {
  // @@protoc_insertion_point(field_mutable:xla.UnpackResponse.tied_data)
  return tied_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >*
UnpackResponse::mutable_tied_data() {
  // @@protoc_insertion_point(field_mutable_list:xla.UnpackResponse.tied_data)
  return &tied_data_;
}
inline const ::xla::GlobalDataHandle& UnpackResponse::tied_data(int index) const {
  // @@protoc_insertion_point(field_get:xla.UnpackResponse.tied_data)
  return tied_data_.Get(index);
}
inline ::xla::GlobalDataHandle* UnpackResponse::add_tied_data() {
  // @@protoc_insertion_point(field_add:xla.UnpackResponse.tied_data)
  return tied_data_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::GlobalDataHandle >&
UnpackResponse::tied_data() const {
  // @@protoc_insertion_point(field_list:xla.UnpackResponse.tied_data)
  return tied_data_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::DebugOptions_StepMarkerLocation> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::DebugOptions_StepMarkerLocation>() {
  return ::xla::DebugOptions_StepMarkerLocation_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fxla_2fxla_2eproto
