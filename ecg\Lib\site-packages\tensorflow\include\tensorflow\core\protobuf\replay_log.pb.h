// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/replay_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/master.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto;
namespace tensorflow {
class NewReplaySession;
class NewReplaySessionDefaultTypeInternal;
extern NewReplaySessionDefaultTypeInternal _NewReplaySession_default_instance_;
class ReplayOp;
class ReplayOpDefaultTypeInternal;
extern ReplayOpDefaultTypeInternal _ReplayOp_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::NewReplaySession* Arena::CreateMaybeMessage<::tensorflow::NewReplaySession>(Arena*);
template<> ::tensorflow::ReplayOp* Arena::CreateMaybeMessage<::tensorflow::ReplayOp>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class NewReplaySession :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NewReplaySession) */ {
 public:
  NewReplaySession();
  virtual ~NewReplaySession();

  NewReplaySession(const NewReplaySession& from);
  NewReplaySession(NewReplaySession&& from) noexcept
    : NewReplaySession() {
    *this = ::std::move(from);
  }

  inline NewReplaySession& operator=(const NewReplaySession& from) {
    CopyFrom(from);
    return *this;
  }
  inline NewReplaySession& operator=(NewReplaySession&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NewReplaySession& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const NewReplaySession* internal_default_instance() {
    return reinterpret_cast<const NewReplaySession*>(
               &_NewReplaySession_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(NewReplaySession& a, NewReplaySession& b) {
    a.Swap(&b);
  }
  inline void Swap(NewReplaySession* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NewReplaySession* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NewReplaySession* New() const final {
    return CreateMaybeMessage<NewReplaySession>(nullptr);
  }

  NewReplaySession* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NewReplaySession>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NewReplaySession& from);
  void MergeFrom(const NewReplaySession& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NewReplaySession* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NewReplaySession";
  }
  protected:
  explicit NewReplaySession(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 2,
    kDevicesFieldNumber = 1,
  };
  // string session_handle = 2;
  void clear_session_handle();
  const std::string& session_handle() const;
  void set_session_handle(const std::string& value);
  void set_session_handle(std::string&& value);
  void set_session_handle(const char* value);
  void set_session_handle(const char* value, size_t size);
  std::string* mutable_session_handle();
  std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_session_handle();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_session_handle(
      std::string* session_handle);

  // .tensorflow.ListDevicesResponse devices = 1;
  bool has_devices() const;
  void clear_devices();
  const ::tensorflow::ListDevicesResponse& devices() const;
  ::tensorflow::ListDevicesResponse* release_devices();
  ::tensorflow::ListDevicesResponse* mutable_devices();
  void set_allocated_devices(::tensorflow::ListDevicesResponse* devices);
  void unsafe_arena_set_allocated_devices(
      ::tensorflow::ListDevicesResponse* devices);
  ::tensorflow::ListDevicesResponse* unsafe_arena_release_devices();

  // @@protoc_insertion_point(class_scope:tensorflow.NewReplaySession)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
  ::tensorflow::ListDevicesResponse* devices_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto;
};
// -------------------------------------------------------------------

class ReplayOp :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReplayOp) */ {
 public:
  ReplayOp();
  virtual ~ReplayOp();

  ReplayOp(const ReplayOp& from);
  ReplayOp(ReplayOp&& from) noexcept
    : ReplayOp() {
    *this = ::std::move(from);
  }

  inline ReplayOp& operator=(const ReplayOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReplayOp& operator=(ReplayOp&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ReplayOp& default_instance();

  enum OpCase {
    kCreateSession = 1,
    kExtendSession = 2,
    kPartialRunSetup = 3,
    kRunStep = 4,
    kCloseSession = 5,
    kListDevices = 6,
    kResetRequest = 7,
    kMakeCallable = 8,
    kRunCallable = 9,
    kReleaseCallable = 10,
    kNewReplaySession = 11,
    OP_NOT_SET = 0,
  };

  enum ResponseCase {
    kCreateSessionResponse = 21,
    kExtendSessionResponse = 22,
    kPartialRunSetupResponse = 23,
    kRunStepResponse = 24,
    kCloseSessionResponse = 25,
    kListDevicesResponse = 26,
    kResetRequestResponse = 27,
    kMakeCallableResponse = 28,
    kRunCallableResponse = 29,
    kReleaseCallableResponse = 30,
    RESPONSE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReplayOp* internal_default_instance() {
    return reinterpret_cast<const ReplayOp*>(
               &_ReplayOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ReplayOp& a, ReplayOp& b) {
    a.Swap(&b);
  }
  inline void Swap(ReplayOp* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReplayOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ReplayOp* New() const final {
    return CreateMaybeMessage<ReplayOp>(nullptr);
  }

  ReplayOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ReplayOp>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ReplayOp& from);
  void MergeFrom(const ReplayOp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReplayOp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReplayOp";
  }
  protected:
  explicit ReplayOp(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartTimeUsFieldNumber = 31,
    kEndTimeUsFieldNumber = 32,
    kCreateSessionFieldNumber = 1,
    kExtendSessionFieldNumber = 2,
    kPartialRunSetupFieldNumber = 3,
    kRunStepFieldNumber = 4,
    kCloseSessionFieldNumber = 5,
    kListDevicesFieldNumber = 6,
    kResetRequestFieldNumber = 7,
    kMakeCallableFieldNumber = 8,
    kRunCallableFieldNumber = 9,
    kReleaseCallableFieldNumber = 10,
    kNewReplaySessionFieldNumber = 11,
    kCreateSessionResponseFieldNumber = 21,
    kExtendSessionResponseFieldNumber = 22,
    kPartialRunSetupResponseFieldNumber = 23,
    kRunStepResponseFieldNumber = 24,
    kCloseSessionResponseFieldNumber = 25,
    kListDevicesResponseFieldNumber = 26,
    kResetRequestResponseFieldNumber = 27,
    kMakeCallableResponseFieldNumber = 28,
    kRunCallableResponseFieldNumber = 29,
    kReleaseCallableResponseFieldNumber = 30,
  };
  // double start_time_us = 31;
  void clear_start_time_us();
  double start_time_us() const;
  void set_start_time_us(double value);

  // double end_time_us = 32;
  void clear_end_time_us();
  double end_time_us() const;
  void set_end_time_us(double value);

  // .tensorflow.CreateSessionRequest create_session = 1;
  bool has_create_session() const;
  void clear_create_session();
  const ::tensorflow::CreateSessionRequest& create_session() const;
  ::tensorflow::CreateSessionRequest* release_create_session();
  ::tensorflow::CreateSessionRequest* mutable_create_session();
  void set_allocated_create_session(::tensorflow::CreateSessionRequest* create_session);
  void unsafe_arena_set_allocated_create_session(
      ::tensorflow::CreateSessionRequest* create_session);
  ::tensorflow::CreateSessionRequest* unsafe_arena_release_create_session();

  // .tensorflow.ExtendSessionRequest extend_session = 2;
  bool has_extend_session() const;
  void clear_extend_session();
  const ::tensorflow::ExtendSessionRequest& extend_session() const;
  ::tensorflow::ExtendSessionRequest* release_extend_session();
  ::tensorflow::ExtendSessionRequest* mutable_extend_session();
  void set_allocated_extend_session(::tensorflow::ExtendSessionRequest* extend_session);
  void unsafe_arena_set_allocated_extend_session(
      ::tensorflow::ExtendSessionRequest* extend_session);
  ::tensorflow::ExtendSessionRequest* unsafe_arena_release_extend_session();

  // .tensorflow.PartialRunSetupRequest partial_run_setup = 3;
  bool has_partial_run_setup() const;
  void clear_partial_run_setup();
  const ::tensorflow::PartialRunSetupRequest& partial_run_setup() const;
  ::tensorflow::PartialRunSetupRequest* release_partial_run_setup();
  ::tensorflow::PartialRunSetupRequest* mutable_partial_run_setup();
  void set_allocated_partial_run_setup(::tensorflow::PartialRunSetupRequest* partial_run_setup);
  void unsafe_arena_set_allocated_partial_run_setup(
      ::tensorflow::PartialRunSetupRequest* partial_run_setup);
  ::tensorflow::PartialRunSetupRequest* unsafe_arena_release_partial_run_setup();

  // .tensorflow.RunStepRequest run_step = 4;
  bool has_run_step() const;
  void clear_run_step();
  const ::tensorflow::RunStepRequest& run_step() const;
  ::tensorflow::RunStepRequest* release_run_step();
  ::tensorflow::RunStepRequest* mutable_run_step();
  void set_allocated_run_step(::tensorflow::RunStepRequest* run_step);
  void unsafe_arena_set_allocated_run_step(
      ::tensorflow::RunStepRequest* run_step);
  ::tensorflow::RunStepRequest* unsafe_arena_release_run_step();

  // .tensorflow.CloseSessionRequest close_session = 5;
  bool has_close_session() const;
  void clear_close_session();
  const ::tensorflow::CloseSessionRequest& close_session() const;
  ::tensorflow::CloseSessionRequest* release_close_session();
  ::tensorflow::CloseSessionRequest* mutable_close_session();
  void set_allocated_close_session(::tensorflow::CloseSessionRequest* close_session);
  void unsafe_arena_set_allocated_close_session(
      ::tensorflow::CloseSessionRequest* close_session);
  ::tensorflow::CloseSessionRequest* unsafe_arena_release_close_session();

  // .tensorflow.ListDevicesRequest list_devices = 6;
  bool has_list_devices() const;
  void clear_list_devices();
  const ::tensorflow::ListDevicesRequest& list_devices() const;
  ::tensorflow::ListDevicesRequest* release_list_devices();
  ::tensorflow::ListDevicesRequest* mutable_list_devices();
  void set_allocated_list_devices(::tensorflow::ListDevicesRequest* list_devices);
  void unsafe_arena_set_allocated_list_devices(
      ::tensorflow::ListDevicesRequest* list_devices);
  ::tensorflow::ListDevicesRequest* unsafe_arena_release_list_devices();

  // .tensorflow.ResetRequest reset_request = 7;
  bool has_reset_request() const;
  void clear_reset_request();
  const ::tensorflow::ResetRequest& reset_request() const;
  ::tensorflow::ResetRequest* release_reset_request();
  ::tensorflow::ResetRequest* mutable_reset_request();
  void set_allocated_reset_request(::tensorflow::ResetRequest* reset_request);
  void unsafe_arena_set_allocated_reset_request(
      ::tensorflow::ResetRequest* reset_request);
  ::tensorflow::ResetRequest* unsafe_arena_release_reset_request();

  // .tensorflow.MakeCallableRequest make_callable = 8;
  bool has_make_callable() const;
  void clear_make_callable();
  const ::tensorflow::MakeCallableRequest& make_callable() const;
  ::tensorflow::MakeCallableRequest* release_make_callable();
  ::tensorflow::MakeCallableRequest* mutable_make_callable();
  void set_allocated_make_callable(::tensorflow::MakeCallableRequest* make_callable);
  void unsafe_arena_set_allocated_make_callable(
      ::tensorflow::MakeCallableRequest* make_callable);
  ::tensorflow::MakeCallableRequest* unsafe_arena_release_make_callable();

  // .tensorflow.RunCallableRequest run_callable = 9;
  bool has_run_callable() const;
  void clear_run_callable();
  const ::tensorflow::RunCallableRequest& run_callable() const;
  ::tensorflow::RunCallableRequest* release_run_callable();
  ::tensorflow::RunCallableRequest* mutable_run_callable();
  void set_allocated_run_callable(::tensorflow::RunCallableRequest* run_callable);
  void unsafe_arena_set_allocated_run_callable(
      ::tensorflow::RunCallableRequest* run_callable);
  ::tensorflow::RunCallableRequest* unsafe_arena_release_run_callable();

  // .tensorflow.ReleaseCallableRequest release_callable = 10;
  bool has_release_callable() const;
  void clear_release_callable();
  const ::tensorflow::ReleaseCallableRequest& release_callable() const;
  ::tensorflow::ReleaseCallableRequest* release_release_callable();
  ::tensorflow::ReleaseCallableRequest* mutable_release_callable();
  void set_allocated_release_callable(::tensorflow::ReleaseCallableRequest* release_callable);
  void unsafe_arena_set_allocated_release_callable(
      ::tensorflow::ReleaseCallableRequest* release_callable);
  ::tensorflow::ReleaseCallableRequest* unsafe_arena_release_release_callable();

  // .tensorflow.NewReplaySession new_replay_session = 11;
  bool has_new_replay_session() const;
  void clear_new_replay_session();
  const ::tensorflow::NewReplaySession& new_replay_session() const;
  ::tensorflow::NewReplaySession* release_new_replay_session();
  ::tensorflow::NewReplaySession* mutable_new_replay_session();
  void set_allocated_new_replay_session(::tensorflow::NewReplaySession* new_replay_session);
  void unsafe_arena_set_allocated_new_replay_session(
      ::tensorflow::NewReplaySession* new_replay_session);
  ::tensorflow::NewReplaySession* unsafe_arena_release_new_replay_session();

  // .tensorflow.CreateSessionResponse create_session_response = 21;
  bool has_create_session_response() const;
  void clear_create_session_response();
  const ::tensorflow::CreateSessionResponse& create_session_response() const;
  ::tensorflow::CreateSessionResponse* release_create_session_response();
  ::tensorflow::CreateSessionResponse* mutable_create_session_response();
  void set_allocated_create_session_response(::tensorflow::CreateSessionResponse* create_session_response);
  void unsafe_arena_set_allocated_create_session_response(
      ::tensorflow::CreateSessionResponse* create_session_response);
  ::tensorflow::CreateSessionResponse* unsafe_arena_release_create_session_response();

  // .tensorflow.ExtendSessionResponse extend_session_response = 22;
  bool has_extend_session_response() const;
  void clear_extend_session_response();
  const ::tensorflow::ExtendSessionResponse& extend_session_response() const;
  ::tensorflow::ExtendSessionResponse* release_extend_session_response();
  ::tensorflow::ExtendSessionResponse* mutable_extend_session_response();
  void set_allocated_extend_session_response(::tensorflow::ExtendSessionResponse* extend_session_response);
  void unsafe_arena_set_allocated_extend_session_response(
      ::tensorflow::ExtendSessionResponse* extend_session_response);
  ::tensorflow::ExtendSessionResponse* unsafe_arena_release_extend_session_response();

  // .tensorflow.PartialRunSetupResponse partial_run_setup_response = 23;
  bool has_partial_run_setup_response() const;
  void clear_partial_run_setup_response();
  const ::tensorflow::PartialRunSetupResponse& partial_run_setup_response() const;
  ::tensorflow::PartialRunSetupResponse* release_partial_run_setup_response();
  ::tensorflow::PartialRunSetupResponse* mutable_partial_run_setup_response();
  void set_allocated_partial_run_setup_response(::tensorflow::PartialRunSetupResponse* partial_run_setup_response);
  void unsafe_arena_set_allocated_partial_run_setup_response(
      ::tensorflow::PartialRunSetupResponse* partial_run_setup_response);
  ::tensorflow::PartialRunSetupResponse* unsafe_arena_release_partial_run_setup_response();

  // .tensorflow.RunStepResponse run_step_response = 24;
  bool has_run_step_response() const;
  void clear_run_step_response();
  const ::tensorflow::RunStepResponse& run_step_response() const;
  ::tensorflow::RunStepResponse* release_run_step_response();
  ::tensorflow::RunStepResponse* mutable_run_step_response();
  void set_allocated_run_step_response(::tensorflow::RunStepResponse* run_step_response);
  void unsafe_arena_set_allocated_run_step_response(
      ::tensorflow::RunStepResponse* run_step_response);
  ::tensorflow::RunStepResponse* unsafe_arena_release_run_step_response();

  // .tensorflow.CloseSessionResponse close_session_response = 25;
  bool has_close_session_response() const;
  void clear_close_session_response();
  const ::tensorflow::CloseSessionResponse& close_session_response() const;
  ::tensorflow::CloseSessionResponse* release_close_session_response();
  ::tensorflow::CloseSessionResponse* mutable_close_session_response();
  void set_allocated_close_session_response(::tensorflow::CloseSessionResponse* close_session_response);
  void unsafe_arena_set_allocated_close_session_response(
      ::tensorflow::CloseSessionResponse* close_session_response);
  ::tensorflow::CloseSessionResponse* unsafe_arena_release_close_session_response();

  // .tensorflow.ListDevicesResponse list_devices_response = 26;
  bool has_list_devices_response() const;
  void clear_list_devices_response();
  const ::tensorflow::ListDevicesResponse& list_devices_response() const;
  ::tensorflow::ListDevicesResponse* release_list_devices_response();
  ::tensorflow::ListDevicesResponse* mutable_list_devices_response();
  void set_allocated_list_devices_response(::tensorflow::ListDevicesResponse* list_devices_response);
  void unsafe_arena_set_allocated_list_devices_response(
      ::tensorflow::ListDevicesResponse* list_devices_response);
  ::tensorflow::ListDevicesResponse* unsafe_arena_release_list_devices_response();

  // .tensorflow.ResetResponse reset_request_response = 27;
  bool has_reset_request_response() const;
  void clear_reset_request_response();
  const ::tensorflow::ResetResponse& reset_request_response() const;
  ::tensorflow::ResetResponse* release_reset_request_response();
  ::tensorflow::ResetResponse* mutable_reset_request_response();
  void set_allocated_reset_request_response(::tensorflow::ResetResponse* reset_request_response);
  void unsafe_arena_set_allocated_reset_request_response(
      ::tensorflow::ResetResponse* reset_request_response);
  ::tensorflow::ResetResponse* unsafe_arena_release_reset_request_response();

  // .tensorflow.MakeCallableResponse make_callable_response = 28;
  bool has_make_callable_response() const;
  void clear_make_callable_response();
  const ::tensorflow::MakeCallableResponse& make_callable_response() const;
  ::tensorflow::MakeCallableResponse* release_make_callable_response();
  ::tensorflow::MakeCallableResponse* mutable_make_callable_response();
  void set_allocated_make_callable_response(::tensorflow::MakeCallableResponse* make_callable_response);
  void unsafe_arena_set_allocated_make_callable_response(
      ::tensorflow::MakeCallableResponse* make_callable_response);
  ::tensorflow::MakeCallableResponse* unsafe_arena_release_make_callable_response();

  // .tensorflow.RunCallableResponse run_callable_response = 29;
  bool has_run_callable_response() const;
  void clear_run_callable_response();
  const ::tensorflow::RunCallableResponse& run_callable_response() const;
  ::tensorflow::RunCallableResponse* release_run_callable_response();
  ::tensorflow::RunCallableResponse* mutable_run_callable_response();
  void set_allocated_run_callable_response(::tensorflow::RunCallableResponse* run_callable_response);
  void unsafe_arena_set_allocated_run_callable_response(
      ::tensorflow::RunCallableResponse* run_callable_response);
  ::tensorflow::RunCallableResponse* unsafe_arena_release_run_callable_response();

  // .tensorflow.ReleaseCallableResponse release_callable_response = 30;
  bool has_release_callable_response() const;
  void clear_release_callable_response();
  const ::tensorflow::ReleaseCallableResponse& release_callable_response() const;
  ::tensorflow::ReleaseCallableResponse* release_release_callable_response();
  ::tensorflow::ReleaseCallableResponse* mutable_release_callable_response();
  void set_allocated_release_callable_response(::tensorflow::ReleaseCallableResponse* release_callable_response);
  void unsafe_arena_set_allocated_release_callable_response(
      ::tensorflow::ReleaseCallableResponse* release_callable_response);
  ::tensorflow::ReleaseCallableResponse* unsafe_arena_release_release_callable_response();

  void clear_op();
  OpCase op_case() const;
  void clear_response();
  ResponseCase response_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.ReplayOp)
 private:
  class _Internal;
  void set_has_create_session();
  void set_has_extend_session();
  void set_has_partial_run_setup();
  void set_has_run_step();
  void set_has_close_session();
  void set_has_list_devices();
  void set_has_reset_request();
  void set_has_make_callable();
  void set_has_run_callable();
  void set_has_release_callable();
  void set_has_new_replay_session();
  void set_has_create_session_response();
  void set_has_extend_session_response();
  void set_has_partial_run_setup_response();
  void set_has_run_step_response();
  void set_has_close_session_response();
  void set_has_list_devices_response();
  void set_has_reset_request_response();
  void set_has_make_callable_response();
  void set_has_run_callable_response();
  void set_has_release_callable_response();

  inline bool has_op() const;
  inline void clear_has_op();

  inline bool has_response() const;
  inline void clear_has_response();

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double start_time_us_;
  double end_time_us_;
  union OpUnion {
    OpUnion() {}
    ::tensorflow::CreateSessionRequest* create_session_;
    ::tensorflow::ExtendSessionRequest* extend_session_;
    ::tensorflow::PartialRunSetupRequest* partial_run_setup_;
    ::tensorflow::RunStepRequest* run_step_;
    ::tensorflow::CloseSessionRequest* close_session_;
    ::tensorflow::ListDevicesRequest* list_devices_;
    ::tensorflow::ResetRequest* reset_request_;
    ::tensorflow::MakeCallableRequest* make_callable_;
    ::tensorflow::RunCallableRequest* run_callable_;
    ::tensorflow::ReleaseCallableRequest* release_callable_;
    ::tensorflow::NewReplaySession* new_replay_session_;
  } op_;
  union ResponseUnion {
    ResponseUnion() {}
    ::tensorflow::CreateSessionResponse* create_session_response_;
    ::tensorflow::ExtendSessionResponse* extend_session_response_;
    ::tensorflow::PartialRunSetupResponse* partial_run_setup_response_;
    ::tensorflow::RunStepResponse* run_step_response_;
    ::tensorflow::CloseSessionResponse* close_session_response_;
    ::tensorflow::ListDevicesResponse* list_devices_response_;
    ::tensorflow::ResetResponse* reset_request_response_;
    ::tensorflow::MakeCallableResponse* make_callable_response_;
    ::tensorflow::RunCallableResponse* run_callable_response_;
    ::tensorflow::ReleaseCallableResponse* release_callable_response_;
  } response_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[2];

  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// NewReplaySession

// .tensorflow.ListDevicesResponse devices = 1;
inline bool NewReplaySession::has_devices() const {
  return this != internal_default_instance() && devices_ != nullptr;
}
inline const ::tensorflow::ListDevicesResponse& NewReplaySession::devices() const {
  const ::tensorflow::ListDevicesResponse* p = devices_;
  // @@protoc_insertion_point(field_get:tensorflow.NewReplaySession.devices)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ListDevicesResponse*>(
      &::tensorflow::_ListDevicesResponse_default_instance_);
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::release_devices() {
  // @@protoc_insertion_point(field_release:tensorflow.NewReplaySession.devices)
  
  ::tensorflow::ListDevicesResponse* temp = devices_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  devices_ = nullptr;
  return temp;
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::unsafe_arena_release_devices() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NewReplaySession.devices)
  
  ::tensorflow::ListDevicesResponse* temp = devices_;
  devices_ = nullptr;
  return temp;
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::mutable_devices() {
  
  if (devices_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ListDevicesResponse>(GetArenaNoVirtual());
    devices_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.NewReplaySession.devices)
  return devices_;
}
inline void NewReplaySession::set_allocated_devices(::tensorflow::ListDevicesResponse* devices) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(devices_);
  }
  if (devices) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(devices)->GetArena();
    if (message_arena != submessage_arena) {
      devices = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, devices, submessage_arena);
    }
    
  } else {
    
  }
  devices_ = devices;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewReplaySession.devices)
}

// string session_handle = 2;
inline void NewReplaySession::clear_session_handle() {
  session_handle_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& NewReplaySession::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewReplaySession.session_handle)
  return session_handle_.Get();
}
inline void NewReplaySession::set_session_handle(const std::string& value) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.NewReplaySession.session_handle)
}
inline void NewReplaySession::set_session_handle(std::string&& value) {
  
  session_handle_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.NewReplaySession.session_handle)
}
inline void NewReplaySession::set_session_handle(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.NewReplaySession.session_handle)
}
inline void NewReplaySession::set_session_handle(const char* value,
    size_t size) {
  
  session_handle_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NewReplaySession.session_handle)
}
inline std::string* NewReplaySession::mutable_session_handle() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.NewReplaySession.session_handle)
  return session_handle_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* NewReplaySession::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.NewReplaySession.session_handle)
  
  return session_handle_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void NewReplaySession::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_handle,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewReplaySession.session_handle)
}
inline std::string* NewReplaySession::unsafe_arena_release_session_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.NewReplaySession.session_handle)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return session_handle_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void NewReplaySession::unsafe_arena_set_allocated_session_handle(
    std::string* session_handle) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (session_handle != nullptr) {
    
  } else {
    
  }
  session_handle_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      session_handle, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NewReplaySession.session_handle)
}

// -------------------------------------------------------------------

// ReplayOp

// double start_time_us = 31;
inline void ReplayOp::clear_start_time_us() {
  start_time_us_ = 0;
}
inline double ReplayOp::start_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.start_time_us)
  return start_time_us_;
}
inline void ReplayOp::set_start_time_us(double value) {
  
  start_time_us_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReplayOp.start_time_us)
}

// double end_time_us = 32;
inline void ReplayOp::clear_end_time_us() {
  end_time_us_ = 0;
}
inline double ReplayOp::end_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.end_time_us)
  return end_time_us_;
}
inline void ReplayOp::set_end_time_us(double value) {
  
  end_time_us_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ReplayOp.end_time_us)
}

// .tensorflow.CreateSessionRequest create_session = 1;
inline bool ReplayOp::has_create_session() const {
  return op_case() == kCreateSession;
}
inline void ReplayOp::set_has_create_session() {
  _oneof_case_[0] = kCreateSession;
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::release_create_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.create_session)
  if (has_create_session()) {
    clear_has_op();
      ::tensorflow::CreateSessionRequest* temp = op_.create_session_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.create_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CreateSessionRequest& ReplayOp::create_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.create_session)
  return has_create_session()
      ? *op_.create_session_
      : *reinterpret_cast< ::tensorflow::CreateSessionRequest*>(&::tensorflow::_CreateSessionRequest_default_instance_);
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::unsafe_arena_release_create_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.create_session)
  if (has_create_session()) {
    clear_has_op();
    ::tensorflow::CreateSessionRequest* temp = op_.create_session_;
    op_.create_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_create_session(::tensorflow::CreateSessionRequest* create_session) {
  clear_op();
  if (create_session) {
    set_has_create_session();
    op_.create_session_ = create_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.create_session)
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::mutable_create_session() {
  if (!has_create_session()) {
    clear_op();
    set_has_create_session();
    op_.create_session_ = CreateMaybeMessage< ::tensorflow::CreateSessionRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.create_session)
  return op_.create_session_;
}

// .tensorflow.ExtendSessionRequest extend_session = 2;
inline bool ReplayOp::has_extend_session() const {
  return op_case() == kExtendSession;
}
inline void ReplayOp::set_has_extend_session() {
  _oneof_case_[0] = kExtendSession;
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::release_extend_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.extend_session)
  if (has_extend_session()) {
    clear_has_op();
      ::tensorflow::ExtendSessionRequest* temp = op_.extend_session_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.extend_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ExtendSessionRequest& ReplayOp::extend_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.extend_session)
  return has_extend_session()
      ? *op_.extend_session_
      : *reinterpret_cast< ::tensorflow::ExtendSessionRequest*>(&::tensorflow::_ExtendSessionRequest_default_instance_);
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::unsafe_arena_release_extend_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.extend_session)
  if (has_extend_session()) {
    clear_has_op();
    ::tensorflow::ExtendSessionRequest* temp = op_.extend_session_;
    op_.extend_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_extend_session(::tensorflow::ExtendSessionRequest* extend_session) {
  clear_op();
  if (extend_session) {
    set_has_extend_session();
    op_.extend_session_ = extend_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.extend_session)
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::mutable_extend_session() {
  if (!has_extend_session()) {
    clear_op();
    set_has_extend_session();
    op_.extend_session_ = CreateMaybeMessage< ::tensorflow::ExtendSessionRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.extend_session)
  return op_.extend_session_;
}

// .tensorflow.PartialRunSetupRequest partial_run_setup = 3;
inline bool ReplayOp::has_partial_run_setup() const {
  return op_case() == kPartialRunSetup;
}
inline void ReplayOp::set_has_partial_run_setup() {
  _oneof_case_[0] = kPartialRunSetup;
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::release_partial_run_setup() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.partial_run_setup)
  if (has_partial_run_setup()) {
    clear_has_op();
      ::tensorflow::PartialRunSetupRequest* temp = op_.partial_run_setup_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.partial_run_setup_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::PartialRunSetupRequest& ReplayOp::partial_run_setup() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.partial_run_setup)
  return has_partial_run_setup()
      ? *op_.partial_run_setup_
      : *reinterpret_cast< ::tensorflow::PartialRunSetupRequest*>(&::tensorflow::_PartialRunSetupRequest_default_instance_);
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::unsafe_arena_release_partial_run_setup() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.partial_run_setup)
  if (has_partial_run_setup()) {
    clear_has_op();
    ::tensorflow::PartialRunSetupRequest* temp = op_.partial_run_setup_;
    op_.partial_run_setup_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_partial_run_setup(::tensorflow::PartialRunSetupRequest* partial_run_setup) {
  clear_op();
  if (partial_run_setup) {
    set_has_partial_run_setup();
    op_.partial_run_setup_ = partial_run_setup;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.partial_run_setup)
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::mutable_partial_run_setup() {
  if (!has_partial_run_setup()) {
    clear_op();
    set_has_partial_run_setup();
    op_.partial_run_setup_ = CreateMaybeMessage< ::tensorflow::PartialRunSetupRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.partial_run_setup)
  return op_.partial_run_setup_;
}

// .tensorflow.RunStepRequest run_step = 4;
inline bool ReplayOp::has_run_step() const {
  return op_case() == kRunStep;
}
inline void ReplayOp::set_has_run_step() {
  _oneof_case_[0] = kRunStep;
}
inline ::tensorflow::RunStepRequest* ReplayOp::release_run_step() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_step)
  if (has_run_step()) {
    clear_has_op();
      ::tensorflow::RunStepRequest* temp = op_.run_step_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.run_step_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunStepRequest& ReplayOp::run_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_step)
  return has_run_step()
      ? *op_.run_step_
      : *reinterpret_cast< ::tensorflow::RunStepRequest*>(&::tensorflow::_RunStepRequest_default_instance_);
}
inline ::tensorflow::RunStepRequest* ReplayOp::unsafe_arena_release_run_step() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_step)
  if (has_run_step()) {
    clear_has_op();
    ::tensorflow::RunStepRequest* temp = op_.run_step_;
    op_.run_step_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_step(::tensorflow::RunStepRequest* run_step) {
  clear_op();
  if (run_step) {
    set_has_run_step();
    op_.run_step_ = run_step;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_step)
}
inline ::tensorflow::RunStepRequest* ReplayOp::mutable_run_step() {
  if (!has_run_step()) {
    clear_op();
    set_has_run_step();
    op_.run_step_ = CreateMaybeMessage< ::tensorflow::RunStepRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_step)
  return op_.run_step_;
}

// .tensorflow.CloseSessionRequest close_session = 5;
inline bool ReplayOp::has_close_session() const {
  return op_case() == kCloseSession;
}
inline void ReplayOp::set_has_close_session() {
  _oneof_case_[0] = kCloseSession;
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::release_close_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.close_session)
  if (has_close_session()) {
    clear_has_op();
      ::tensorflow::CloseSessionRequest* temp = op_.close_session_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.close_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CloseSessionRequest& ReplayOp::close_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.close_session)
  return has_close_session()
      ? *op_.close_session_
      : *reinterpret_cast< ::tensorflow::CloseSessionRequest*>(&::tensorflow::_CloseSessionRequest_default_instance_);
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::unsafe_arena_release_close_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.close_session)
  if (has_close_session()) {
    clear_has_op();
    ::tensorflow::CloseSessionRequest* temp = op_.close_session_;
    op_.close_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_close_session(::tensorflow::CloseSessionRequest* close_session) {
  clear_op();
  if (close_session) {
    set_has_close_session();
    op_.close_session_ = close_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.close_session)
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::mutable_close_session() {
  if (!has_close_session()) {
    clear_op();
    set_has_close_session();
    op_.close_session_ = CreateMaybeMessage< ::tensorflow::CloseSessionRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.close_session)
  return op_.close_session_;
}

// .tensorflow.ListDevicesRequest list_devices = 6;
inline bool ReplayOp::has_list_devices() const {
  return op_case() == kListDevices;
}
inline void ReplayOp::set_has_list_devices() {
  _oneof_case_[0] = kListDevices;
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::release_list_devices() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.list_devices)
  if (has_list_devices()) {
    clear_has_op();
      ::tensorflow::ListDevicesRequest* temp = op_.list_devices_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.list_devices_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ListDevicesRequest& ReplayOp::list_devices() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.list_devices)
  return has_list_devices()
      ? *op_.list_devices_
      : *reinterpret_cast< ::tensorflow::ListDevicesRequest*>(&::tensorflow::_ListDevicesRequest_default_instance_);
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::unsafe_arena_release_list_devices() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.list_devices)
  if (has_list_devices()) {
    clear_has_op();
    ::tensorflow::ListDevicesRequest* temp = op_.list_devices_;
    op_.list_devices_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_list_devices(::tensorflow::ListDevicesRequest* list_devices) {
  clear_op();
  if (list_devices) {
    set_has_list_devices();
    op_.list_devices_ = list_devices;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.list_devices)
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::mutable_list_devices() {
  if (!has_list_devices()) {
    clear_op();
    set_has_list_devices();
    op_.list_devices_ = CreateMaybeMessage< ::tensorflow::ListDevicesRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.list_devices)
  return op_.list_devices_;
}

// .tensorflow.ResetRequest reset_request = 7;
inline bool ReplayOp::has_reset_request() const {
  return op_case() == kResetRequest;
}
inline void ReplayOp::set_has_reset_request() {
  _oneof_case_[0] = kResetRequest;
}
inline ::tensorflow::ResetRequest* ReplayOp::release_reset_request() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.reset_request)
  if (has_reset_request()) {
    clear_has_op();
      ::tensorflow::ResetRequest* temp = op_.reset_request_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.reset_request_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ResetRequest& ReplayOp::reset_request() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.reset_request)
  return has_reset_request()
      ? *op_.reset_request_
      : *reinterpret_cast< ::tensorflow::ResetRequest*>(&::tensorflow::_ResetRequest_default_instance_);
}
inline ::tensorflow::ResetRequest* ReplayOp::unsafe_arena_release_reset_request() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.reset_request)
  if (has_reset_request()) {
    clear_has_op();
    ::tensorflow::ResetRequest* temp = op_.reset_request_;
    op_.reset_request_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_reset_request(::tensorflow::ResetRequest* reset_request) {
  clear_op();
  if (reset_request) {
    set_has_reset_request();
    op_.reset_request_ = reset_request;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.reset_request)
}
inline ::tensorflow::ResetRequest* ReplayOp::mutable_reset_request() {
  if (!has_reset_request()) {
    clear_op();
    set_has_reset_request();
    op_.reset_request_ = CreateMaybeMessage< ::tensorflow::ResetRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.reset_request)
  return op_.reset_request_;
}

// .tensorflow.MakeCallableRequest make_callable = 8;
inline bool ReplayOp::has_make_callable() const {
  return op_case() == kMakeCallable;
}
inline void ReplayOp::set_has_make_callable() {
  _oneof_case_[0] = kMakeCallable;
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::release_make_callable() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.make_callable)
  if (has_make_callable()) {
    clear_has_op();
      ::tensorflow::MakeCallableRequest* temp = op_.make_callable_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.make_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::MakeCallableRequest& ReplayOp::make_callable() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.make_callable)
  return has_make_callable()
      ? *op_.make_callable_
      : *reinterpret_cast< ::tensorflow::MakeCallableRequest*>(&::tensorflow::_MakeCallableRequest_default_instance_);
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::unsafe_arena_release_make_callable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.make_callable)
  if (has_make_callable()) {
    clear_has_op();
    ::tensorflow::MakeCallableRequest* temp = op_.make_callable_;
    op_.make_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_make_callable(::tensorflow::MakeCallableRequest* make_callable) {
  clear_op();
  if (make_callable) {
    set_has_make_callable();
    op_.make_callable_ = make_callable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.make_callable)
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::mutable_make_callable() {
  if (!has_make_callable()) {
    clear_op();
    set_has_make_callable();
    op_.make_callable_ = CreateMaybeMessage< ::tensorflow::MakeCallableRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.make_callable)
  return op_.make_callable_;
}

// .tensorflow.RunCallableRequest run_callable = 9;
inline bool ReplayOp::has_run_callable() const {
  return op_case() == kRunCallable;
}
inline void ReplayOp::set_has_run_callable() {
  _oneof_case_[0] = kRunCallable;
}
inline ::tensorflow::RunCallableRequest* ReplayOp::release_run_callable() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_callable)
  if (has_run_callable()) {
    clear_has_op();
      ::tensorflow::RunCallableRequest* temp = op_.run_callable_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.run_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunCallableRequest& ReplayOp::run_callable() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_callable)
  return has_run_callable()
      ? *op_.run_callable_
      : *reinterpret_cast< ::tensorflow::RunCallableRequest*>(&::tensorflow::_RunCallableRequest_default_instance_);
}
inline ::tensorflow::RunCallableRequest* ReplayOp::unsafe_arena_release_run_callable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_callable)
  if (has_run_callable()) {
    clear_has_op();
    ::tensorflow::RunCallableRequest* temp = op_.run_callable_;
    op_.run_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_callable(::tensorflow::RunCallableRequest* run_callable) {
  clear_op();
  if (run_callable) {
    set_has_run_callable();
    op_.run_callable_ = run_callable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_callable)
}
inline ::tensorflow::RunCallableRequest* ReplayOp::mutable_run_callable() {
  if (!has_run_callable()) {
    clear_op();
    set_has_run_callable();
    op_.run_callable_ = CreateMaybeMessage< ::tensorflow::RunCallableRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_callable)
  return op_.run_callable_;
}

// .tensorflow.ReleaseCallableRequest release_callable = 10;
inline bool ReplayOp::has_release_callable() const {
  return op_case() == kReleaseCallable;
}
inline void ReplayOp::set_has_release_callable() {
  _oneof_case_[0] = kReleaseCallable;
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::release_release_callable() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.release_callable)
  if (has_release_callable()) {
    clear_has_op();
      ::tensorflow::ReleaseCallableRequest* temp = op_.release_callable_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.release_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ReleaseCallableRequest& ReplayOp::release_callable() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.release_callable)
  return has_release_callable()
      ? *op_.release_callable_
      : *reinterpret_cast< ::tensorflow::ReleaseCallableRequest*>(&::tensorflow::_ReleaseCallableRequest_default_instance_);
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::unsafe_arena_release_release_callable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.release_callable)
  if (has_release_callable()) {
    clear_has_op();
    ::tensorflow::ReleaseCallableRequest* temp = op_.release_callable_;
    op_.release_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_release_callable(::tensorflow::ReleaseCallableRequest* release_callable) {
  clear_op();
  if (release_callable) {
    set_has_release_callable();
    op_.release_callable_ = release_callable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.release_callable)
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::mutable_release_callable() {
  if (!has_release_callable()) {
    clear_op();
    set_has_release_callable();
    op_.release_callable_ = CreateMaybeMessage< ::tensorflow::ReleaseCallableRequest >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.release_callable)
  return op_.release_callable_;
}

// .tensorflow.NewReplaySession new_replay_session = 11;
inline bool ReplayOp::has_new_replay_session() const {
  return op_case() == kNewReplaySession;
}
inline void ReplayOp::set_has_new_replay_session() {
  _oneof_case_[0] = kNewReplaySession;
}
inline void ReplayOp::clear_new_replay_session() {
  if (has_new_replay_session()) {
    if (GetArenaNoVirtual() == nullptr) {
      delete op_.new_replay_session_;
    }
    clear_has_op();
  }
}
inline ::tensorflow::NewReplaySession* ReplayOp::release_new_replay_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.new_replay_session)
  if (has_new_replay_session()) {
    clear_has_op();
      ::tensorflow::NewReplaySession* temp = op_.new_replay_session_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    op_.new_replay_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NewReplaySession& ReplayOp::new_replay_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.new_replay_session)
  return has_new_replay_session()
      ? *op_.new_replay_session_
      : *reinterpret_cast< ::tensorflow::NewReplaySession*>(&::tensorflow::_NewReplaySession_default_instance_);
}
inline ::tensorflow::NewReplaySession* ReplayOp::unsafe_arena_release_new_replay_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.new_replay_session)
  if (has_new_replay_session()) {
    clear_has_op();
    ::tensorflow::NewReplaySession* temp = op_.new_replay_session_;
    op_.new_replay_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_new_replay_session(::tensorflow::NewReplaySession* new_replay_session) {
  clear_op();
  if (new_replay_session) {
    set_has_new_replay_session();
    op_.new_replay_session_ = new_replay_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.new_replay_session)
}
inline ::tensorflow::NewReplaySession* ReplayOp::mutable_new_replay_session() {
  if (!has_new_replay_session()) {
    clear_op();
    set_has_new_replay_session();
    op_.new_replay_session_ = CreateMaybeMessage< ::tensorflow::NewReplaySession >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.new_replay_session)
  return op_.new_replay_session_;
}

// .tensorflow.CreateSessionResponse create_session_response = 21;
inline bool ReplayOp::has_create_session_response() const {
  return response_case() == kCreateSessionResponse;
}
inline void ReplayOp::set_has_create_session_response() {
  _oneof_case_[1] = kCreateSessionResponse;
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::release_create_session_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.create_session_response)
  if (has_create_session_response()) {
    clear_has_response();
      ::tensorflow::CreateSessionResponse* temp = response_.create_session_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.create_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CreateSessionResponse& ReplayOp::create_session_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.create_session_response)
  return has_create_session_response()
      ? *response_.create_session_response_
      : *reinterpret_cast< ::tensorflow::CreateSessionResponse*>(&::tensorflow::_CreateSessionResponse_default_instance_);
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::unsafe_arena_release_create_session_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.create_session_response)
  if (has_create_session_response()) {
    clear_has_response();
    ::tensorflow::CreateSessionResponse* temp = response_.create_session_response_;
    response_.create_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_create_session_response(::tensorflow::CreateSessionResponse* create_session_response) {
  clear_response();
  if (create_session_response) {
    set_has_create_session_response();
    response_.create_session_response_ = create_session_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.create_session_response)
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::mutable_create_session_response() {
  if (!has_create_session_response()) {
    clear_response();
    set_has_create_session_response();
    response_.create_session_response_ = CreateMaybeMessage< ::tensorflow::CreateSessionResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.create_session_response)
  return response_.create_session_response_;
}

// .tensorflow.ExtendSessionResponse extend_session_response = 22;
inline bool ReplayOp::has_extend_session_response() const {
  return response_case() == kExtendSessionResponse;
}
inline void ReplayOp::set_has_extend_session_response() {
  _oneof_case_[1] = kExtendSessionResponse;
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::release_extend_session_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.extend_session_response)
  if (has_extend_session_response()) {
    clear_has_response();
      ::tensorflow::ExtendSessionResponse* temp = response_.extend_session_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.extend_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ExtendSessionResponse& ReplayOp::extend_session_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.extend_session_response)
  return has_extend_session_response()
      ? *response_.extend_session_response_
      : *reinterpret_cast< ::tensorflow::ExtendSessionResponse*>(&::tensorflow::_ExtendSessionResponse_default_instance_);
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::unsafe_arena_release_extend_session_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.extend_session_response)
  if (has_extend_session_response()) {
    clear_has_response();
    ::tensorflow::ExtendSessionResponse* temp = response_.extend_session_response_;
    response_.extend_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_extend_session_response(::tensorflow::ExtendSessionResponse* extend_session_response) {
  clear_response();
  if (extend_session_response) {
    set_has_extend_session_response();
    response_.extend_session_response_ = extend_session_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.extend_session_response)
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::mutable_extend_session_response() {
  if (!has_extend_session_response()) {
    clear_response();
    set_has_extend_session_response();
    response_.extend_session_response_ = CreateMaybeMessage< ::tensorflow::ExtendSessionResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.extend_session_response)
  return response_.extend_session_response_;
}

// .tensorflow.PartialRunSetupResponse partial_run_setup_response = 23;
inline bool ReplayOp::has_partial_run_setup_response() const {
  return response_case() == kPartialRunSetupResponse;
}
inline void ReplayOp::set_has_partial_run_setup_response() {
  _oneof_case_[1] = kPartialRunSetupResponse;
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::release_partial_run_setup_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.partial_run_setup_response)
  if (has_partial_run_setup_response()) {
    clear_has_response();
      ::tensorflow::PartialRunSetupResponse* temp = response_.partial_run_setup_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.partial_run_setup_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::PartialRunSetupResponse& ReplayOp::partial_run_setup_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.partial_run_setup_response)
  return has_partial_run_setup_response()
      ? *response_.partial_run_setup_response_
      : *reinterpret_cast< ::tensorflow::PartialRunSetupResponse*>(&::tensorflow::_PartialRunSetupResponse_default_instance_);
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::unsafe_arena_release_partial_run_setup_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.partial_run_setup_response)
  if (has_partial_run_setup_response()) {
    clear_has_response();
    ::tensorflow::PartialRunSetupResponse* temp = response_.partial_run_setup_response_;
    response_.partial_run_setup_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_partial_run_setup_response(::tensorflow::PartialRunSetupResponse* partial_run_setup_response) {
  clear_response();
  if (partial_run_setup_response) {
    set_has_partial_run_setup_response();
    response_.partial_run_setup_response_ = partial_run_setup_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.partial_run_setup_response)
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::mutable_partial_run_setup_response() {
  if (!has_partial_run_setup_response()) {
    clear_response();
    set_has_partial_run_setup_response();
    response_.partial_run_setup_response_ = CreateMaybeMessage< ::tensorflow::PartialRunSetupResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.partial_run_setup_response)
  return response_.partial_run_setup_response_;
}

// .tensorflow.RunStepResponse run_step_response = 24;
inline bool ReplayOp::has_run_step_response() const {
  return response_case() == kRunStepResponse;
}
inline void ReplayOp::set_has_run_step_response() {
  _oneof_case_[1] = kRunStepResponse;
}
inline ::tensorflow::RunStepResponse* ReplayOp::release_run_step_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_step_response)
  if (has_run_step_response()) {
    clear_has_response();
      ::tensorflow::RunStepResponse* temp = response_.run_step_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.run_step_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunStepResponse& ReplayOp::run_step_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_step_response)
  return has_run_step_response()
      ? *response_.run_step_response_
      : *reinterpret_cast< ::tensorflow::RunStepResponse*>(&::tensorflow::_RunStepResponse_default_instance_);
}
inline ::tensorflow::RunStepResponse* ReplayOp::unsafe_arena_release_run_step_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_step_response)
  if (has_run_step_response()) {
    clear_has_response();
    ::tensorflow::RunStepResponse* temp = response_.run_step_response_;
    response_.run_step_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_step_response(::tensorflow::RunStepResponse* run_step_response) {
  clear_response();
  if (run_step_response) {
    set_has_run_step_response();
    response_.run_step_response_ = run_step_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_step_response)
}
inline ::tensorflow::RunStepResponse* ReplayOp::mutable_run_step_response() {
  if (!has_run_step_response()) {
    clear_response();
    set_has_run_step_response();
    response_.run_step_response_ = CreateMaybeMessage< ::tensorflow::RunStepResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_step_response)
  return response_.run_step_response_;
}

// .tensorflow.CloseSessionResponse close_session_response = 25;
inline bool ReplayOp::has_close_session_response() const {
  return response_case() == kCloseSessionResponse;
}
inline void ReplayOp::set_has_close_session_response() {
  _oneof_case_[1] = kCloseSessionResponse;
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::release_close_session_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.close_session_response)
  if (has_close_session_response()) {
    clear_has_response();
      ::tensorflow::CloseSessionResponse* temp = response_.close_session_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.close_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CloseSessionResponse& ReplayOp::close_session_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.close_session_response)
  return has_close_session_response()
      ? *response_.close_session_response_
      : *reinterpret_cast< ::tensorflow::CloseSessionResponse*>(&::tensorflow::_CloseSessionResponse_default_instance_);
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::unsafe_arena_release_close_session_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.close_session_response)
  if (has_close_session_response()) {
    clear_has_response();
    ::tensorflow::CloseSessionResponse* temp = response_.close_session_response_;
    response_.close_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_close_session_response(::tensorflow::CloseSessionResponse* close_session_response) {
  clear_response();
  if (close_session_response) {
    set_has_close_session_response();
    response_.close_session_response_ = close_session_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.close_session_response)
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::mutable_close_session_response() {
  if (!has_close_session_response()) {
    clear_response();
    set_has_close_session_response();
    response_.close_session_response_ = CreateMaybeMessage< ::tensorflow::CloseSessionResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.close_session_response)
  return response_.close_session_response_;
}

// .tensorflow.ListDevicesResponse list_devices_response = 26;
inline bool ReplayOp::has_list_devices_response() const {
  return response_case() == kListDevicesResponse;
}
inline void ReplayOp::set_has_list_devices_response() {
  _oneof_case_[1] = kListDevicesResponse;
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::release_list_devices_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.list_devices_response)
  if (has_list_devices_response()) {
    clear_has_response();
      ::tensorflow::ListDevicesResponse* temp = response_.list_devices_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.list_devices_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ListDevicesResponse& ReplayOp::list_devices_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.list_devices_response)
  return has_list_devices_response()
      ? *response_.list_devices_response_
      : *reinterpret_cast< ::tensorflow::ListDevicesResponse*>(&::tensorflow::_ListDevicesResponse_default_instance_);
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::unsafe_arena_release_list_devices_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.list_devices_response)
  if (has_list_devices_response()) {
    clear_has_response();
    ::tensorflow::ListDevicesResponse* temp = response_.list_devices_response_;
    response_.list_devices_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_list_devices_response(::tensorflow::ListDevicesResponse* list_devices_response) {
  clear_response();
  if (list_devices_response) {
    set_has_list_devices_response();
    response_.list_devices_response_ = list_devices_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.list_devices_response)
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::mutable_list_devices_response() {
  if (!has_list_devices_response()) {
    clear_response();
    set_has_list_devices_response();
    response_.list_devices_response_ = CreateMaybeMessage< ::tensorflow::ListDevicesResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.list_devices_response)
  return response_.list_devices_response_;
}

// .tensorflow.ResetResponse reset_request_response = 27;
inline bool ReplayOp::has_reset_request_response() const {
  return response_case() == kResetRequestResponse;
}
inline void ReplayOp::set_has_reset_request_response() {
  _oneof_case_[1] = kResetRequestResponse;
}
inline ::tensorflow::ResetResponse* ReplayOp::release_reset_request_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.reset_request_response)
  if (has_reset_request_response()) {
    clear_has_response();
      ::tensorflow::ResetResponse* temp = response_.reset_request_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.reset_request_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ResetResponse& ReplayOp::reset_request_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.reset_request_response)
  return has_reset_request_response()
      ? *response_.reset_request_response_
      : *reinterpret_cast< ::tensorflow::ResetResponse*>(&::tensorflow::_ResetResponse_default_instance_);
}
inline ::tensorflow::ResetResponse* ReplayOp::unsafe_arena_release_reset_request_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.reset_request_response)
  if (has_reset_request_response()) {
    clear_has_response();
    ::tensorflow::ResetResponse* temp = response_.reset_request_response_;
    response_.reset_request_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_reset_request_response(::tensorflow::ResetResponse* reset_request_response) {
  clear_response();
  if (reset_request_response) {
    set_has_reset_request_response();
    response_.reset_request_response_ = reset_request_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.reset_request_response)
}
inline ::tensorflow::ResetResponse* ReplayOp::mutable_reset_request_response() {
  if (!has_reset_request_response()) {
    clear_response();
    set_has_reset_request_response();
    response_.reset_request_response_ = CreateMaybeMessage< ::tensorflow::ResetResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.reset_request_response)
  return response_.reset_request_response_;
}

// .tensorflow.MakeCallableResponse make_callable_response = 28;
inline bool ReplayOp::has_make_callable_response() const {
  return response_case() == kMakeCallableResponse;
}
inline void ReplayOp::set_has_make_callable_response() {
  _oneof_case_[1] = kMakeCallableResponse;
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::release_make_callable_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.make_callable_response)
  if (has_make_callable_response()) {
    clear_has_response();
      ::tensorflow::MakeCallableResponse* temp = response_.make_callable_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.make_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::MakeCallableResponse& ReplayOp::make_callable_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.make_callable_response)
  return has_make_callable_response()
      ? *response_.make_callable_response_
      : *reinterpret_cast< ::tensorflow::MakeCallableResponse*>(&::tensorflow::_MakeCallableResponse_default_instance_);
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::unsafe_arena_release_make_callable_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.make_callable_response)
  if (has_make_callable_response()) {
    clear_has_response();
    ::tensorflow::MakeCallableResponse* temp = response_.make_callable_response_;
    response_.make_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_make_callable_response(::tensorflow::MakeCallableResponse* make_callable_response) {
  clear_response();
  if (make_callable_response) {
    set_has_make_callable_response();
    response_.make_callable_response_ = make_callable_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.make_callable_response)
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::mutable_make_callable_response() {
  if (!has_make_callable_response()) {
    clear_response();
    set_has_make_callable_response();
    response_.make_callable_response_ = CreateMaybeMessage< ::tensorflow::MakeCallableResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.make_callable_response)
  return response_.make_callable_response_;
}

// .tensorflow.RunCallableResponse run_callable_response = 29;
inline bool ReplayOp::has_run_callable_response() const {
  return response_case() == kRunCallableResponse;
}
inline void ReplayOp::set_has_run_callable_response() {
  _oneof_case_[1] = kRunCallableResponse;
}
inline ::tensorflow::RunCallableResponse* ReplayOp::release_run_callable_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_callable_response)
  if (has_run_callable_response()) {
    clear_has_response();
      ::tensorflow::RunCallableResponse* temp = response_.run_callable_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.run_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunCallableResponse& ReplayOp::run_callable_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_callable_response)
  return has_run_callable_response()
      ? *response_.run_callable_response_
      : *reinterpret_cast< ::tensorflow::RunCallableResponse*>(&::tensorflow::_RunCallableResponse_default_instance_);
}
inline ::tensorflow::RunCallableResponse* ReplayOp::unsafe_arena_release_run_callable_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_callable_response)
  if (has_run_callable_response()) {
    clear_has_response();
    ::tensorflow::RunCallableResponse* temp = response_.run_callable_response_;
    response_.run_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_callable_response(::tensorflow::RunCallableResponse* run_callable_response) {
  clear_response();
  if (run_callable_response) {
    set_has_run_callable_response();
    response_.run_callable_response_ = run_callable_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_callable_response)
}
inline ::tensorflow::RunCallableResponse* ReplayOp::mutable_run_callable_response() {
  if (!has_run_callable_response()) {
    clear_response();
    set_has_run_callable_response();
    response_.run_callable_response_ = CreateMaybeMessage< ::tensorflow::RunCallableResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_callable_response)
  return response_.run_callable_response_;
}

// .tensorflow.ReleaseCallableResponse release_callable_response = 30;
inline bool ReplayOp::has_release_callable_response() const {
  return response_case() == kReleaseCallableResponse;
}
inline void ReplayOp::set_has_release_callable_response() {
  _oneof_case_[1] = kReleaseCallableResponse;
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::release_release_callable_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.release_callable_response)
  if (has_release_callable_response()) {
    clear_has_response();
      ::tensorflow::ReleaseCallableResponse* temp = response_.release_callable_response_;
    if (GetArenaNoVirtual() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    response_.release_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ReleaseCallableResponse& ReplayOp::release_callable_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.release_callable_response)
  return has_release_callable_response()
      ? *response_.release_callable_response_
      : *reinterpret_cast< ::tensorflow::ReleaseCallableResponse*>(&::tensorflow::_ReleaseCallableResponse_default_instance_);
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::unsafe_arena_release_release_callable_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.release_callable_response)
  if (has_release_callable_response()) {
    clear_has_response();
    ::tensorflow::ReleaseCallableResponse* temp = response_.release_callable_response_;
    response_.release_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_release_callable_response(::tensorflow::ReleaseCallableResponse* release_callable_response) {
  clear_response();
  if (release_callable_response) {
    set_has_release_callable_response();
    response_.release_callable_response_ = release_callable_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.release_callable_response)
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::mutable_release_callable_response() {
  if (!has_release_callable_response()) {
    clear_response();
    set_has_release_callable_response();
    response_.release_callable_response_ = CreateMaybeMessage< ::tensorflow::ReleaseCallableResponse >(
        GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.release_callable_response)
  return response_.release_callable_response_;
}

inline bool ReplayOp::has_op() const {
  return op_case() != OP_NOT_SET;
}
inline void ReplayOp::clear_has_op() {
  _oneof_case_[0] = OP_NOT_SET;
}
inline bool ReplayOp::has_response() const {
  return response_case() != RESPONSE_NOT_SET;
}
inline void ReplayOp::clear_has_response() {
  _oneof_case_[1] = RESPONSE_NOT_SET;
}
inline ReplayOp::OpCase ReplayOp::op_case() const {
  return ReplayOp::OpCase(_oneof_case_[0]);
}
inline ReplayOp::ResponseCase ReplayOp::response_case() const {
  return ReplayOp::ResponseCase(_oneof_case_[1]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto
