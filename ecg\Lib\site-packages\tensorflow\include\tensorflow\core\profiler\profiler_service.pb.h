// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/profiler_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/profiler/profiler_options.pb.h"
#include "tensorflow/core/profiler/profiler_service_monitor_result.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
namespace tensorflow {
class MonitorRequest;
class MonitorRequestDefaultTypeInternal;
extern MonitorRequestDefaultTypeInternal _MonitorRequest_default_instance_;
class MonitorResponse;
class MonitorResponseDefaultTypeInternal;
extern MonitorResponseDefaultTypeInternal _MonitorResponse_default_instance_;
class ProfileRequest;
class ProfileRequestDefaultTypeInternal;
extern ProfileRequestDefaultTypeInternal _ProfileRequest_default_instance_;
class ProfileRequest_ToolOptionsEntry_DoNotUse;
class ProfileRequest_ToolOptionsEntry_DoNotUseDefaultTypeInternal;
extern ProfileRequest_ToolOptionsEntry_DoNotUseDefaultTypeInternal _ProfileRequest_ToolOptionsEntry_DoNotUse_default_instance_;
class ProfileResponse;
class ProfileResponseDefaultTypeInternal;
extern ProfileResponseDefaultTypeInternal _ProfileResponse_default_instance_;
class ProfileToolData;
class ProfileToolDataDefaultTypeInternal;
extern ProfileToolDataDefaultTypeInternal _ProfileToolData_default_instance_;
class TerminateRequest;
class TerminateRequestDefaultTypeInternal;
extern TerminateRequestDefaultTypeInternal _TerminateRequest_default_instance_;
class TerminateResponse;
class TerminateResponseDefaultTypeInternal;
extern TerminateResponseDefaultTypeInternal _TerminateResponse_default_instance_;
class ToolRequestOptions;
class ToolRequestOptionsDefaultTypeInternal;
extern ToolRequestOptionsDefaultTypeInternal _ToolRequestOptions_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::MonitorRequest* Arena::CreateMaybeMessage<::tensorflow::MonitorRequest>(Arena*);
template<> ::tensorflow::MonitorResponse* Arena::CreateMaybeMessage<::tensorflow::MonitorResponse>(Arena*);
template<> ::tensorflow::ProfileRequest* Arena::CreateMaybeMessage<::tensorflow::ProfileRequest>(Arena*);
template<> ::tensorflow::ProfileRequest_ToolOptionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ProfileRequest_ToolOptionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ProfileResponse* Arena::CreateMaybeMessage<::tensorflow::ProfileResponse>(Arena*);
template<> ::tensorflow::ProfileToolData* Arena::CreateMaybeMessage<::tensorflow::ProfileToolData>(Arena*);
template<> ::tensorflow::TerminateRequest* Arena::CreateMaybeMessage<::tensorflow::TerminateRequest>(Arena*);
template<> ::tensorflow::TerminateResponse* Arena::CreateMaybeMessage<::tensorflow::TerminateResponse>(Arena*);
template<> ::tensorflow::ToolRequestOptions* Arena::CreateMaybeMessage<::tensorflow::ToolRequestOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ToolRequestOptions :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ToolRequestOptions) */ {
 public:
  ToolRequestOptions();
  virtual ~ToolRequestOptions();

  ToolRequestOptions(const ToolRequestOptions& from);
  ToolRequestOptions(ToolRequestOptions&& from) noexcept
    : ToolRequestOptions() {
    *this = ::std::move(from);
  }

  inline ToolRequestOptions& operator=(const ToolRequestOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ToolRequestOptions& operator=(ToolRequestOptions&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ToolRequestOptions& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ToolRequestOptions* internal_default_instance() {
    return reinterpret_cast<const ToolRequestOptions*>(
               &_ToolRequestOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ToolRequestOptions& a, ToolRequestOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ToolRequestOptions* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ToolRequestOptions* New() const final {
    return CreateMaybeMessage<ToolRequestOptions>(nullptr);
  }

  ToolRequestOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ToolRequestOptions>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ToolRequestOptions& from);
  void MergeFrom(const ToolRequestOptions& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ToolRequestOptions* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ToolRequestOptions";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputFormatsFieldNumber = 2,
    kSaveToRepoFieldNumber = 3,
  };
  // string output_formats = 2;
  void clear_output_formats();
  const std::string& output_formats() const;
  void set_output_formats(const std::string& value);
  void set_output_formats(std::string&& value);
  void set_output_formats(const char* value);
  void set_output_formats(const char* value, size_t size);
  std::string* mutable_output_formats();
  std::string* release_output_formats();
  void set_allocated_output_formats(std::string* output_formats);

  // bool save_to_repo = 3;
  void clear_save_to_repo();
  bool save_to_repo() const;
  void set_save_to_repo(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.ToolRequestOptions)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_formats_;
  bool save_to_repo_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ProfileRequest_ToolOptionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileRequest_ToolOptionsEntry_DoNotUse, 
    std::string, ::tensorflow::ToolRequestOptions,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileRequest_ToolOptionsEntry_DoNotUse, 
    std::string, ::tensorflow::ToolRequestOptions,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
    0 > SuperType;
  ProfileRequest_ToolOptionsEntry_DoNotUse();
  ProfileRequest_ToolOptionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileRequest_ToolOptionsEntry_DoNotUse& other);
  static const ProfileRequest_ToolOptionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileRequest_ToolOptionsEntry_DoNotUse*>(&_ProfileRequest_ToolOptionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), s->size(), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ProfileRequest.ToolOptionsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& other) final;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[1];
  }

  public:
};

// -------------------------------------------------------------------

class ProfileRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileRequest) */ {
 public:
  ProfileRequest();
  virtual ~ProfileRequest();

  ProfileRequest(const ProfileRequest& from);
  ProfileRequest(ProfileRequest&& from) noexcept
    : ProfileRequest() {
    *this = ::std::move(from);
  }

  inline ProfileRequest& operator=(const ProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileRequest& operator=(ProfileRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileRequest* internal_default_instance() {
    return reinterpret_cast<const ProfileRequest*>(
               &_ProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ProfileRequest& a, ProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileRequest* New() const final {
    return CreateMaybeMessage<ProfileRequest>(nullptr);
  }

  ProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileRequest& from);
  void MergeFrom(const ProfileRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kToolsFieldNumber = 3,
    kToolOptionsFieldNumber = 8,
    kRepositoryRootFieldNumber = 5,
    kSessionIdFieldNumber = 6,
    kHostNameFieldNumber = 7,
    kOptsFieldNumber = 4,
    kDurationMsFieldNumber = 1,
    kMaxEventsFieldNumber = 2,
  };
  // repeated string tools = 3;
  int tools_size() const;
  void clear_tools();
  const std::string& tools(int index) const;
  std::string* mutable_tools(int index);
  void set_tools(int index, const std::string& value);
  void set_tools(int index, std::string&& value);
  void set_tools(int index, const char* value);
  void set_tools(int index, const char* value, size_t size);
  std::string* add_tools();
  void add_tools(const std::string& value);
  void add_tools(std::string&& value);
  void add_tools(const char* value);
  void add_tools(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& tools() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_tools();

  // map<string, .tensorflow.ToolRequestOptions> tool_options = 8;
  int tool_options_size() const;
  void clear_tool_options();
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >&
      tool_options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >*
      mutable_tool_options();

  // string repository_root = 5;
  void clear_repository_root();
  const std::string& repository_root() const;
  void set_repository_root(const std::string& value);
  void set_repository_root(std::string&& value);
  void set_repository_root(const char* value);
  void set_repository_root(const char* value, size_t size);
  std::string* mutable_repository_root();
  std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);

  // string session_id = 6;
  void clear_session_id();
  const std::string& session_id() const;
  void set_session_id(const std::string& value);
  void set_session_id(std::string&& value);
  void set_session_id(const char* value);
  void set_session_id(const char* value, size_t size);
  std::string* mutable_session_id();
  std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);

  // string host_name = 7;
  void clear_host_name();
  const std::string& host_name() const;
  void set_host_name(const std::string& value);
  void set_host_name(std::string&& value);
  void set_host_name(const char* value);
  void set_host_name(const char* value, size_t size);
  std::string* mutable_host_name();
  std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);

  // .tensorflow.ProfileOptions opts = 4;
  bool has_opts() const;
  void clear_opts();
  const ::tensorflow::ProfileOptions& opts() const;
  ::tensorflow::ProfileOptions* release_opts();
  ::tensorflow::ProfileOptions* mutable_opts();
  void set_allocated_opts(::tensorflow::ProfileOptions* opts);

  // uint64 duration_ms = 1;
  void clear_duration_ms();
  ::PROTOBUF_NAMESPACE_ID::uint64 duration_ms() const;
  void set_duration_ms(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // uint64 max_events = 2;
  void clear_max_events();
  ::PROTOBUF_NAMESPACE_ID::uint64 max_events() const;
  void set_max_events(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> tools_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      ProfileRequest_ToolOptionsEntry_DoNotUse,
      std::string, ::tensorflow::ToolRequestOptions,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE,
      0 > tool_options_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
  ::tensorflow::ProfileOptions* opts_;
  ::PROTOBUF_NAMESPACE_ID::uint64 duration_ms_;
  ::PROTOBUF_NAMESPACE_ID::uint64 max_events_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ProfileToolData :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileToolData) */ {
 public:
  ProfileToolData();
  virtual ~ProfileToolData();

  ProfileToolData(const ProfileToolData& from);
  ProfileToolData(ProfileToolData&& from) noexcept
    : ProfileToolData() {
    *this = ::std::move(from);
  }

  inline ProfileToolData& operator=(const ProfileToolData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileToolData& operator=(ProfileToolData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileToolData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileToolData* internal_default_instance() {
    return reinterpret_cast<const ProfileToolData*>(
               &_ProfileToolData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ProfileToolData& a, ProfileToolData& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileToolData* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileToolData* New() const final {
    return CreateMaybeMessage<ProfileToolData>(nullptr);
  }

  ProfileToolData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileToolData>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileToolData& from);
  void MergeFrom(const ProfileToolData& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileToolData* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileToolData";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDataFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);

  // bytes data = 2;
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const void* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileToolData)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ProfileResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileResponse) */ {
 public:
  ProfileResponse();
  virtual ~ProfileResponse();

  ProfileResponse(const ProfileResponse& from);
  ProfileResponse(ProfileResponse&& from) noexcept
    : ProfileResponse() {
    *this = ::std::move(from);
  }

  inline ProfileResponse& operator=(const ProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileResponse& operator=(ProfileResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ProfileResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ProfileResponse* internal_default_instance() {
    return reinterpret_cast<const ProfileResponse*>(
               &_ProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ProfileResponse& a, ProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ProfileResponse* New() const final {
    return CreateMaybeMessage<ProfileResponse>(nullptr);
  }

  ProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ProfileResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ProfileResponse& from);
  void MergeFrom(const ProfileResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kToolDataFieldNumber = 6,
    kEmptyTraceFieldNumber = 7,
  };
  // repeated .tensorflow.ProfileToolData tool_data = 6;
  int tool_data_size() const;
  void clear_tool_data();
  ::tensorflow::ProfileToolData* mutable_tool_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >*
      mutable_tool_data();
  const ::tensorflow::ProfileToolData& tool_data(int index) const;
  ::tensorflow::ProfileToolData* add_tool_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >&
      tool_data() const;

  // bool empty_trace = 7;
  void clear_empty_trace();
  bool empty_trace() const;
  void set_empty_trace(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData > tool_data_;
  bool empty_trace_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TerminateRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TerminateRequest) */ {
 public:
  TerminateRequest();
  virtual ~TerminateRequest();

  TerminateRequest(const TerminateRequest& from);
  TerminateRequest(TerminateRequest&& from) noexcept
    : TerminateRequest() {
    *this = ::std::move(from);
  }

  inline TerminateRequest& operator=(const TerminateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TerminateRequest& operator=(TerminateRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TerminateRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TerminateRequest* internal_default_instance() {
    return reinterpret_cast<const TerminateRequest*>(
               &_TerminateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(TerminateRequest& a, TerminateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TerminateRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TerminateRequest* New() const final {
    return CreateMaybeMessage<TerminateRequest>(nullptr);
  }

  TerminateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TerminateRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TerminateRequest& from);
  void MergeFrom(const TerminateRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TerminateRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TerminateRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionIdFieldNumber = 1,
  };
  // string session_id = 1;
  void clear_session_id();
  const std::string& session_id() const;
  void set_session_id(const std::string& value);
  void set_session_id(std::string&& value);
  void set_session_id(const char* value);
  void set_session_id(const char* value, size_t size);
  std::string* mutable_session_id();
  std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);

  // @@protoc_insertion_point(class_scope:tensorflow.TerminateRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TerminateResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TerminateResponse) */ {
 public:
  TerminateResponse();
  virtual ~TerminateResponse();

  TerminateResponse(const TerminateResponse& from);
  TerminateResponse(TerminateResponse&& from) noexcept
    : TerminateResponse() {
    *this = ::std::move(from);
  }

  inline TerminateResponse& operator=(const TerminateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TerminateResponse& operator=(TerminateResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TerminateResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const TerminateResponse* internal_default_instance() {
    return reinterpret_cast<const TerminateResponse*>(
               &_TerminateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TerminateResponse& a, TerminateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TerminateResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TerminateResponse* New() const final {
    return CreateMaybeMessage<TerminateResponse>(nullptr);
  }

  TerminateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TerminateResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TerminateResponse& from);
  void MergeFrom(const TerminateResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TerminateResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TerminateResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.TerminateResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class MonitorRequest :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MonitorRequest) */ {
 public:
  MonitorRequest();
  virtual ~MonitorRequest();

  MonitorRequest(const MonitorRequest& from);
  MonitorRequest(MonitorRequest&& from) noexcept
    : MonitorRequest() {
    *this = ::std::move(from);
  }

  inline MonitorRequest& operator=(const MonitorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MonitorRequest& operator=(MonitorRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MonitorRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MonitorRequest* internal_default_instance() {
    return reinterpret_cast<const MonitorRequest*>(
               &_MonitorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(MonitorRequest& a, MonitorRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MonitorRequest* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MonitorRequest* New() const final {
    return CreateMaybeMessage<MonitorRequest>(nullptr);
  }

  MonitorRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MonitorRequest>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MonitorRequest& from);
  void MergeFrom(const MonitorRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MonitorRequest* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MonitorRequest";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDurationMsFieldNumber = 1,
    kMonitoringLevelFieldNumber = 2,
    kTimestampFieldNumber = 3,
  };
  // uint64 duration_ms = 1;
  void clear_duration_ms();
  ::PROTOBUF_NAMESPACE_ID::uint64 duration_ms() const;
  void set_duration_ms(::PROTOBUF_NAMESPACE_ID::uint64 value);

  // int32 monitoring_level = 2;
  void clear_monitoring_level();
  ::PROTOBUF_NAMESPACE_ID::int32 monitoring_level() const;
  void set_monitoring_level(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool timestamp = 3;
  void clear_timestamp();
  bool timestamp() const;
  void set_timestamp(bool value);

  // @@protoc_insertion_point(class_scope:tensorflow.MonitorRequest)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::uint64 duration_ms_;
  ::PROTOBUF_NAMESPACE_ID::int32 monitoring_level_;
  bool timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class MonitorResponse :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MonitorResponse) */ {
 public:
  MonitorResponse();
  virtual ~MonitorResponse();

  MonitorResponse(const MonitorResponse& from);
  MonitorResponse(MonitorResponse&& from) noexcept
    : MonitorResponse() {
    *this = ::std::move(from);
  }

  inline MonitorResponse& operator=(const MonitorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MonitorResponse& operator=(MonitorResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MonitorResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const MonitorResponse* internal_default_instance() {
    return reinterpret_cast<const MonitorResponse*>(
               &_MonitorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(MonitorResponse& a, MonitorResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MonitorResponse* other) {
    if (other == this) return;
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MonitorResponse* New() const final {
    return CreateMaybeMessage<MonitorResponse>(nullptr);
  }

  MonitorResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MonitorResponse>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MonitorResponse& from);
  void MergeFrom(const MonitorResponse& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MonitorResponse* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MonitorResponse";
  }
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return nullptr;
  }
  inline void* MaybeArenaPtr() const {
    return nullptr;
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kMonitorResultFieldNumber = 10,
  };
  // string data = 1;
  void clear_data();
  const std::string& data() const;
  void set_data(const std::string& value);
  void set_data(std::string&& value);
  void set_data(const char* value);
  void set_data(const char* value, size_t size);
  std::string* mutable_data();
  std::string* release_data();
  void set_allocated_data(std::string* data);

  // .tensorflow.ProfilerServiceMonitorResult monitor_result = 10;
  bool has_monitor_result() const;
  void clear_monitor_result();
  const ::tensorflow::ProfilerServiceMonitorResult& monitor_result() const;
  ::tensorflow::ProfilerServiceMonitorResult* release_monitor_result();
  ::tensorflow::ProfilerServiceMonitorResult* mutable_monitor_result();
  void set_allocated_monitor_result(::tensorflow::ProfilerServiceMonitorResult* monitor_result);

  // @@protoc_insertion_point(class_scope:tensorflow.MonitorResponse)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  ::tensorflow::ProfilerServiceMonitorResult* monitor_result_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ToolRequestOptions

// string output_formats = 2;
inline void ToolRequestOptions::clear_output_formats() {
  output_formats_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ToolRequestOptions::output_formats() const {
  // @@protoc_insertion_point(field_get:tensorflow.ToolRequestOptions.output_formats)
  return output_formats_.GetNoArena();
}
inline void ToolRequestOptions::set_output_formats(const std::string& value) {
  
  output_formats_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ToolRequestOptions.output_formats)
}
inline void ToolRequestOptions::set_output_formats(std::string&& value) {
  
  output_formats_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ToolRequestOptions.output_formats)
}
inline void ToolRequestOptions::set_output_formats(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  output_formats_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ToolRequestOptions.output_formats)
}
inline void ToolRequestOptions::set_output_formats(const char* value, size_t size) {
  
  output_formats_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ToolRequestOptions.output_formats)
}
inline std::string* ToolRequestOptions::mutable_output_formats() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ToolRequestOptions.output_formats)
  return output_formats_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ToolRequestOptions::release_output_formats() {
  // @@protoc_insertion_point(field_release:tensorflow.ToolRequestOptions.output_formats)
  
  return output_formats_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ToolRequestOptions::set_allocated_output_formats(std::string* output_formats) {
  if (output_formats != nullptr) {
    
  } else {
    
  }
  output_formats_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), output_formats);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ToolRequestOptions.output_formats)
}

// bool save_to_repo = 3;
inline void ToolRequestOptions::clear_save_to_repo() {
  save_to_repo_ = false;
}
inline bool ToolRequestOptions::save_to_repo() const {
  // @@protoc_insertion_point(field_get:tensorflow.ToolRequestOptions.save_to_repo)
  return save_to_repo_;
}
inline void ToolRequestOptions::set_save_to_repo(bool value) {
  
  save_to_repo_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ToolRequestOptions.save_to_repo)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileRequest

// uint64 duration_ms = 1;
inline void ProfileRequest::clear_duration_ms() {
  duration_ms_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ProfileRequest::duration_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.duration_ms)
  return duration_ms_;
}
inline void ProfileRequest::set_duration_ms(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  duration_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.duration_ms)
}

// uint64 max_events = 2;
inline void ProfileRequest::clear_max_events() {
  max_events_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ProfileRequest::max_events() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.max_events)
  return max_events_;
}
inline void ProfileRequest::set_max_events(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  max_events_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.max_events)
}

// repeated string tools = 3;
inline int ProfileRequest::tools_size() const {
  return tools_.size();
}
inline void ProfileRequest::clear_tools() {
  tools_.Clear();
}
inline const std::string& ProfileRequest::tools(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.tools)
  return tools_.Get(index);
}
inline std::string* ProfileRequest::mutable_tools(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.tools)
  return tools_.Mutable(index);
}
inline void ProfileRequest::set_tools(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.tools)
  tools_.Mutable(index)->assign(value);
}
inline void ProfileRequest::set_tools(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.tools)
  tools_.Mutable(index)->assign(std::move(value));
}
inline void ProfileRequest::set_tools(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  tools_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::set_tools(int index, const char* value, size_t size) {
  tools_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileRequest.tools)
}
inline std::string* ProfileRequest::add_tools() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ProfileRequest.tools)
  return tools_.Add();
}
inline void ProfileRequest::add_tools(const std::string& value) {
  tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::add_tools(std::string&& value) {
  tools_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::add_tools(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::add_tools(const char* value, size_t size) {
  tools_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ProfileRequest.tools)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProfileRequest::tools() const {
  // @@protoc_insertion_point(field_list:tensorflow.ProfileRequest.tools)
  return tools_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProfileRequest::mutable_tools() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ProfileRequest.tools)
  return &tools_;
}

// map<string, .tensorflow.ToolRequestOptions> tool_options = 8;
inline int ProfileRequest::tool_options_size() const {
  return tool_options_.size();
}
inline void ProfileRequest::clear_tool_options() {
  tool_options_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >&
ProfileRequest::tool_options() const {
  // @@protoc_insertion_point(field_map:tensorflow.ProfileRequest.tool_options)
  return tool_options_.GetMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >*
ProfileRequest::mutable_tool_options() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ProfileRequest.tool_options)
  return tool_options_.MutableMap();
}

// .tensorflow.ProfileOptions opts = 4;
inline bool ProfileRequest::has_opts() const {
  return this != internal_default_instance() && opts_ != nullptr;
}
inline const ::tensorflow::ProfileOptions& ProfileRequest::opts() const {
  const ::tensorflow::ProfileOptions* p = opts_;
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.opts)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ProfileOptions*>(
      &::tensorflow::_ProfileOptions_default_instance_);
}
inline ::tensorflow::ProfileOptions* ProfileRequest::release_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.opts)
  
  ::tensorflow::ProfileOptions* temp = opts_;
  opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfileOptions* ProfileRequest::mutable_opts() {
  
  if (opts_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfileOptions>(GetArenaNoVirtual());
    opts_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.opts)
  return opts_;
}
inline void ProfileRequest::set_allocated_opts(::tensorflow::ProfileOptions* opts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(opts_);
  }
  if (opts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      opts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, opts, submessage_arena);
    }
    
  } else {
    
  }
  opts_ = opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.opts)
}

// string repository_root = 5;
inline void ProfileRequest::clear_repository_root() {
  repository_root_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.repository_root)
  return repository_root_.GetNoArena();
}
inline void ProfileRequest::set_repository_root(const std::string& value) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.repository_root)
}
inline void ProfileRequest::set_repository_root(std::string&& value) {
  
  repository_root_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileRequest.repository_root)
}
inline void ProfileRequest::set_repository_root(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileRequest.repository_root)
}
inline void ProfileRequest::set_repository_root(const char* value, size_t size) {
  
  repository_root_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileRequest.repository_root)
}
inline std::string* ProfileRequest::mutable_repository_root() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.repository_root)
  return repository_root_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.repository_root)
  
  return repository_root_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  repository_root_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), repository_root);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.repository_root)
}

// string session_id = 6;
inline void ProfileRequest::clear_session_id() {
  session_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.session_id)
  return session_id_.GetNoArena();
}
inline void ProfileRequest::set_session_id(const std::string& value) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.session_id)
}
inline void ProfileRequest::set_session_id(std::string&& value) {
  
  session_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileRequest.session_id)
}
inline void ProfileRequest::set_session_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileRequest.session_id)
}
inline void ProfileRequest::set_session_id(const char* value, size_t size) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileRequest.session_id)
}
inline std::string* ProfileRequest::mutable_session_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.session_id)
  return session_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.session_id)
  
  return session_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  session_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.session_id)
}

// string host_name = 7;
inline void ProfileRequest::clear_host_name() {
  host_name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileRequest::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.host_name)
  return host_name_.GetNoArena();
}
inline void ProfileRequest::set_host_name(const std::string& value) {
  
  host_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.host_name)
}
inline void ProfileRequest::set_host_name(std::string&& value) {
  
  host_name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileRequest.host_name)
}
inline void ProfileRequest::set_host_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  host_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileRequest.host_name)
}
inline void ProfileRequest::set_host_name(const char* value, size_t size) {
  
  host_name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileRequest.host_name)
}
inline std::string* ProfileRequest::mutable_host_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.host_name)
  return host_name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileRequest::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.host_name)
  
  return host_name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileRequest::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.host_name)
}

// -------------------------------------------------------------------

// ProfileToolData

// string name = 1;
inline void ProfileToolData::clear_name() {
  name_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileToolData::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileToolData.name)
  return name_.GetNoArena();
}
inline void ProfileToolData::set_name(const std::string& value) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileToolData.name)
}
inline void ProfileToolData::set_name(std::string&& value) {
  
  name_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileToolData.name)
}
inline void ProfileToolData::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileToolData.name)
}
inline void ProfileToolData::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileToolData.name)
}
inline std::string* ProfileToolData::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileToolData.name)
  return name_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileToolData::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileToolData.name)
  
  return name_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileToolData::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileToolData.name)
}

// bytes data = 2;
inline void ProfileToolData::clear_data() {
  data_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& ProfileToolData::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileToolData.data)
  return data_.GetNoArena();
}
inline void ProfileToolData::set_data(const std::string& value) {
  
  data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileToolData.data)
}
inline void ProfileToolData::set_data(std::string&& value) {
  
  data_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.ProfileToolData.data)
}
inline void ProfileToolData::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileToolData.data)
}
inline void ProfileToolData::set_data(const void* value, size_t size) {
  
  data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileToolData.data)
}
inline std::string* ProfileToolData::mutable_data() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileToolData.data)
  return data_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* ProfileToolData::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileToolData.data)
  
  return data_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void ProfileToolData::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileToolData.data)
}

// -------------------------------------------------------------------

// ProfileResponse

// repeated .tensorflow.ProfileToolData tool_data = 6;
inline int ProfileResponse::tool_data_size() const {
  return tool_data_.size();
}
inline void ProfileResponse::clear_tool_data() {
  tool_data_.Clear();
}
inline ::tensorflow::ProfileToolData* ProfileResponse::mutable_tool_data(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileResponse.tool_data)
  return tool_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >*
ProfileResponse::mutable_tool_data() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ProfileResponse.tool_data)
  return &tool_data_;
}
inline const ::tensorflow::ProfileToolData& ProfileResponse::tool_data(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileResponse.tool_data)
  return tool_data_.Get(index);
}
inline ::tensorflow::ProfileToolData* ProfileResponse::add_tool_data() {
  // @@protoc_insertion_point(field_add:tensorflow.ProfileResponse.tool_data)
  return tool_data_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >&
ProfileResponse::tool_data() const {
  // @@protoc_insertion_point(field_list:tensorflow.ProfileResponse.tool_data)
  return tool_data_;
}

// bool empty_trace = 7;
inline void ProfileResponse::clear_empty_trace() {
  empty_trace_ = false;
}
inline bool ProfileResponse::empty_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileResponse.empty_trace)
  return empty_trace_;
}
inline void ProfileResponse::set_empty_trace(bool value) {
  
  empty_trace_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.ProfileResponse.empty_trace)
}

// -------------------------------------------------------------------

// TerminateRequest

// string session_id = 1;
inline void TerminateRequest::clear_session_id() {
  session_id_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& TerminateRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TerminateRequest.session_id)
  return session_id_.GetNoArena();
}
inline void TerminateRequest::set_session_id(const std::string& value) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.TerminateRequest.session_id)
}
inline void TerminateRequest::set_session_id(std::string&& value) {
  
  session_id_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.TerminateRequest.session_id)
}
inline void TerminateRequest::set_session_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.TerminateRequest.session_id)
}
inline void TerminateRequest::set_session_id(const char* value, size_t size) {
  
  session_id_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TerminateRequest.session_id)
}
inline std::string* TerminateRequest::mutable_session_id() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.TerminateRequest.session_id)
  return session_id_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* TerminateRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.TerminateRequest.session_id)
  
  return session_id_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void TerminateRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  session_id_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_id);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TerminateRequest.session_id)
}

// -------------------------------------------------------------------

// TerminateResponse

// -------------------------------------------------------------------

// MonitorRequest

// uint64 duration_ms = 1;
inline void MonitorRequest::clear_duration_ms() {
  duration_ms_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 MonitorRequest::duration_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorRequest.duration_ms)
  return duration_ms_;
}
inline void MonitorRequest::set_duration_ms(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  duration_ms_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MonitorRequest.duration_ms)
}

// int32 monitoring_level = 2;
inline void MonitorRequest::clear_monitoring_level() {
  monitoring_level_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MonitorRequest::monitoring_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorRequest.monitoring_level)
  return monitoring_level_;
}
inline void MonitorRequest::set_monitoring_level(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  monitoring_level_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MonitorRequest.monitoring_level)
}

// bool timestamp = 3;
inline void MonitorRequest::clear_timestamp() {
  timestamp_ = false;
}
inline bool MonitorRequest::timestamp() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorRequest.timestamp)
  return timestamp_;
}
inline void MonitorRequest::set_timestamp(bool value) {
  
  timestamp_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.MonitorRequest.timestamp)
}

// -------------------------------------------------------------------

// MonitorResponse

// string data = 1;
inline void MonitorResponse::clear_data() {
  data_.ClearToEmptyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline const std::string& MonitorResponse::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorResponse.data)
  return data_.GetNoArena();
}
inline void MonitorResponse::set_data(const std::string& value) {
  
  data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:tensorflow.MonitorResponse.data)
}
inline void MonitorResponse::set_data(std::string&& value) {
  
  data_.SetNoArena(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.MonitorResponse.data)
}
inline void MonitorResponse::set_data(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:tensorflow.MonitorResponse.data)
}
inline void MonitorResponse::set_data(const char* value, size_t size) {
  
  data_.SetNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MonitorResponse.data)
}
inline std::string* MonitorResponse::mutable_data() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.MonitorResponse.data)
  return data_.MutableNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline std::string* MonitorResponse::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.MonitorResponse.data)
  
  return data_.ReleaseNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}
inline void MonitorResponse::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  data_.SetAllocatedNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), data);
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MonitorResponse.data)
}

// .tensorflow.ProfilerServiceMonitorResult monitor_result = 10;
inline bool MonitorResponse::has_monitor_result() const {
  return this != internal_default_instance() && monitor_result_ != nullptr;
}
inline const ::tensorflow::ProfilerServiceMonitorResult& MonitorResponse::monitor_result() const {
  const ::tensorflow::ProfilerServiceMonitorResult* p = monitor_result_;
  // @@protoc_insertion_point(field_get:tensorflow.MonitorResponse.monitor_result)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::ProfilerServiceMonitorResult*>(
      &::tensorflow::_ProfilerServiceMonitorResult_default_instance_);
}
inline ::tensorflow::ProfilerServiceMonitorResult* MonitorResponse::release_monitor_result() {
  // @@protoc_insertion_point(field_release:tensorflow.MonitorResponse.monitor_result)
  
  ::tensorflow::ProfilerServiceMonitorResult* temp = monitor_result_;
  monitor_result_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfilerServiceMonitorResult* MonitorResponse::mutable_monitor_result() {
  
  if (monitor_result_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfilerServiceMonitorResult>(GetArenaNoVirtual());
    monitor_result_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.MonitorResponse.monitor_result)
  return monitor_result_;
}
inline void MonitorResponse::set_allocated_monitor_result(::tensorflow::ProfilerServiceMonitorResult* monitor_result) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(monitor_result_);
  }
  if (monitor_result) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena = nullptr;
    if (message_arena != submessage_arena) {
      monitor_result = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, monitor_result, submessage_arena);
    }
    
  } else {
    
  }
  monitor_result_ = monitor_result;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MonitorResponse.monitor_result)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2fprofiler_5fservice_2eproto
