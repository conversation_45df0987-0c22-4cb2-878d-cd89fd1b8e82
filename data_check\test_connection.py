from sql_helper import MysqlHelper
import time

def test_connection(max_retries=3, retry_delay=5):
    """测试数据库连接，带重试机制"""
    for attempt in range(max_retries):
        try:
            print(f"尝试连接数据库 (尝试 {attempt + 1}/{max_retries})...")
            db = MysqlHelper("default")
            conn = db.get_con()
            print("数据库连接成功！")
            
            # 测试简单查询
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print("查询测试成功！")
            
            cursor.close()
            db.close(conn)
            return True
            
        except Exception as e:
            print(f"连接失败: {str(e)}")
            if attempt < max_retries - 1:
                print(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                print("所有重试都失败了。")
                print("请检查：")
                print("1. 服务器 (39.102.213.76) 是否在线")
                print("2. 端口 (8306) 是否开放")
                print("3. 网络连接是否正常")
                print("4. 防火墙设置是否允许连接")
    return False

if __name__ == "__main__":
    test_connection() 