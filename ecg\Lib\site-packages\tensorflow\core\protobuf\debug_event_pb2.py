# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorflow/core/protobuf/debug_event.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorflow.core.framework import tensor_pb2 as tensorflow_dot_core_dot_framework_dot_tensor__pb2
from tensorflow.core.protobuf import graph_debug_info_pb2 as tensorflow_dot_core_dot_protobuf_dot_graph__debug__info__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='tensorflow/core/protobuf/debug_event.proto',
  package='tensorflow',
  syntax='proto3',
  serialized_options=_b('\n\023org.tensorflow.utilB\020DebugEventProtosP\001ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\370\001\001'),
  serialized_pb=_b('\n*tensorflow/core/protobuf/debug_event.proto\x12\ntensorflow\x1a&tensorflow/core/framework/tensor.proto\x1a/tensorflow/core/protobuf/graph_debug_info.proto\"\xfe\x03\n\nDebugEvent\x12\x11\n\twall_time\x18\x01 \x01(\x01\x12\x0c\n\x04step\x18\x02 \x01(\x03\x12\x33\n\x0e\x64\x65\x62ug_metadata\x18\x03 \x01(\x0b\x32\x19.tensorflow.DebugMetadataH\x00\x12-\n\x0bsource_file\x18\x04 \x01(\x0b\x32\x16.tensorflow.SourceFileH\x00\x12;\n\x13stack_frame_with_id\x18\x06 \x01(\x0b\x32\x1c.tensorflow.StackFrameWithIdH\x00\x12\x38\n\x11graph_op_creation\x18\x07 \x01(\x0b\x32\x1b.tensorflow.GraphOpCreationH\x00\x12\x33\n\x0e\x64\x65\x62ugged_graph\x18\x08 \x01(\x0b\x32\x19.tensorflow.DebuggedGraphH\x00\x12*\n\texecution\x18\t \x01(\x0b\x32\x15.tensorflow.ExecutionH\x00\x12@\n\x15graph_execution_trace\x18\n \x01(\x0b\x32\x1f.tensorflow.GraphExecutionTraceH\x00\x12\x12\n\x08graph_id\x18\x0b \x01(\tH\x00\x12\x35\n\x0f\x64\x65\x62ugged_device\x18\x0c \x01(\x0b\x32\x1a.tensorflow.DebuggedDeviceH\x00\x42\x06\n\x04what\"W\n\rDebugMetadata\x12\x1a\n\x12tensorflow_version\x18\x01 \x01(\t\x12\x14\n\x0c\x66ile_version\x18\x02 \x01(\t\x12\x14\n\x0ctfdbg_run_id\x18\x03 \x01(\t\"A\n\nSourceFile\x12\x11\n\tfile_path\x18\x01 \x01(\t\x12\x11\n\thost_name\x18\x02 \x01(\t\x12\r\n\x05lines\x18\x03 \x03(\t\"]\n\x10StackFrameWithId\x12\n\n\x02id\x18\x01 \x01(\t\x12=\n\rfile_line_col\x18\x02 \x01(\x0b\x32&.tensorflow.GraphDebugInfo.FileLineCol\":\n\x0c\x43odeLocation\x12\x11\n\thost_name\x18\x01 \x01(\t\x12\x17\n\x0fstack_frame_ids\x18\x02 \x03(\t\"\xe4\x01\n\x0fGraphOpCreation\x12\x0f\n\x07op_type\x18\x01 \x01(\t\x12\x0f\n\x07op_name\x18\x02 \x01(\t\x12\x12\n\ngraph_name\x18\x03 \x01(\t\x12\x10\n\x08graph_id\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65vice_name\x18\x05 \x01(\t\x12\x13\n\x0binput_names\x18\x06 \x03(\t\x12\x13\n\x0bnum_outputs\x18\x07 \x01(\x05\x12/\n\rcode_location\x18\x08 \x01(\x0b\x32\x18.tensorflow.CodeLocation\x12\x19\n\x11output_tensor_ids\x18\t \x03(\x05\"\xa5\x01\n\rDebuggedGraph\x12\x10\n\x08graph_id\x18\x01 \x01(\t\x12\x12\n\ngraph_name\x18\x02 \x01(\t\x12\x18\n\x10instrumented_ops\x18\x03 \x03(\t\x12\x1a\n\x12original_graph_def\x18\x04 \x01(\x0c\x12\x1e\n\x16instrumented_graph_def\x18\x05 \x01(\x0c\x12\x18\n\x10outer_context_id\x18\x06 \x01(\t\"8\n\x0e\x44\x65\x62uggedDevice\x12\x13\n\x0b\x64\x65vice_name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\x05\"\xb3\x02\n\tExecution\x12\x0f\n\x07op_type\x18\x01 \x01(\t\x12\x13\n\x0bnum_outputs\x18\x02 \x01(\x05\x12\x10\n\x08graph_id\x18\x03 \x01(\t\x12\x18\n\x10input_tensor_ids\x18\x04 \x03(\x03\x12\x19\n\x11output_tensor_ids\x18\x05 \x03(\x03\x12\x36\n\x11tensor_debug_mode\x18\x06 \x01(\x0e\x32\x1b.tensorflow.TensorDebugMode\x12.\n\rtensor_protos\x18\x07 \x03(\x0b\x32\x17.tensorflow.TensorProto\x12/\n\rcode_location\x18\x08 \x01(\x0b\x32\x18.tensorflow.CodeLocation\x12 \n\x18output_tensor_device_ids\x18\t \x03(\x05\"\xd1\x01\n\x13GraphExecutionTrace\x12\x18\n\x10tfdbg_context_id\x18\x01 \x01(\t\x12\x0f\n\x07op_name\x18\x02 \x01(\t\x12\x13\n\x0boutput_slot\x18\x03 \x01(\x05\x12\x36\n\x11tensor_debug_mode\x18\x04 \x01(\x0e\x32\x1b.tensorflow.TensorDebugMode\x12-\n\x0ctensor_proto\x18\x05 \x01(\x0b\x32\x17.tensorflow.TensorProto\x12\x13\n\x0b\x64\x65vice_name\x18\x06 \x01(\t*\xb6\x01\n\x0fTensorDebugMode\x12\x0f\n\x0bUNSPECIFIED\x10\x00\x12\r\n\tNO_TENSOR\x10\x01\x12\x0f\n\x0b\x43URT_HEALTH\x10\x02\x12\x12\n\x0e\x43ONCISE_HEALTH\x10\x03\x12\x0f\n\x0b\x46ULL_HEALTH\x10\x04\x12\t\n\x05SHAPE\x10\x05\x12\x11\n\rFULL_NUMERICS\x10\x06\x12\x0f\n\x0b\x46ULL_TENSOR\x10\x07\x12\x1e\n\x1aREDUCE_INF_NAN_THREE_SLOTS\x10\x08\x42\x83\x01\n\x13org.tensorflow.utilB\x10\x44\x65\x62ugEventProtosP\x01ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\xf8\x01\x01\x62\x06proto3')
  ,
  dependencies=[tensorflow_dot_core_dot_framework_dot_tensor__pb2.DESCRIPTOR,tensorflow_dot_core_dot_protobuf_dot_graph__debug__info__pb2.DESCRIPTOR,])

_TENSORDEBUGMODE = _descriptor.EnumDescriptor(
  name='TensorDebugMode',
  full_name='tensorflow.TensorDebugMode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NO_TENSOR', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CURT_HEALTH', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONCISE_HEALTH', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FULL_HEALTH', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SHAPE', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FULL_NUMERICS', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FULL_TENSOR', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REDUCE_INF_NAN_THREE_SLOTS', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1951,
  serialized_end=2133,
)
_sym_db.RegisterEnumDescriptor(_TENSORDEBUGMODE)

TensorDebugMode = enum_type_wrapper.EnumTypeWrapper(_TENSORDEBUGMODE)
UNSPECIFIED = 0
NO_TENSOR = 1
CURT_HEALTH = 2
CONCISE_HEALTH = 3
FULL_HEALTH = 4
SHAPE = 5
FULL_NUMERICS = 6
FULL_TENSOR = 7
REDUCE_INF_NAN_THREE_SLOTS = 8



_DEBUGEVENT = _descriptor.Descriptor(
  name='DebugEvent',
  full_name='tensorflow.DebugEvent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='wall_time', full_name='tensorflow.DebugEvent.wall_time', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='step', full_name='tensorflow.DebugEvent.step', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='debug_metadata', full_name='tensorflow.DebugEvent.debug_metadata', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_file', full_name='tensorflow.DebugEvent.source_file', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stack_frame_with_id', full_name='tensorflow.DebugEvent.stack_frame_with_id', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_op_creation', full_name='tensorflow.DebugEvent.graph_op_creation', index=5,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='debugged_graph', full_name='tensorflow.DebugEvent.debugged_graph', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='execution', full_name='tensorflow.DebugEvent.execution', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_execution_trace', full_name='tensorflow.DebugEvent.graph_execution_trace', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_id', full_name='tensorflow.DebugEvent.graph_id', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='debugged_device', full_name='tensorflow.DebugEvent.debugged_device', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='what', full_name='tensorflow.DebugEvent.what',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=148,
  serialized_end=658,
)


_DEBUGMETADATA = _descriptor.Descriptor(
  name='DebugMetadata',
  full_name='tensorflow.DebugMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tensorflow_version', full_name='tensorflow.DebugMetadata.tensorflow_version', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_version', full_name='tensorflow.DebugMetadata.file_version', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tfdbg_run_id', full_name='tensorflow.DebugMetadata.tfdbg_run_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=660,
  serialized_end=747,
)


_SOURCEFILE = _descriptor.Descriptor(
  name='SourceFile',
  full_name='tensorflow.SourceFile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='file_path', full_name='tensorflow.SourceFile.file_path', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='host_name', full_name='tensorflow.SourceFile.host_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lines', full_name='tensorflow.SourceFile.lines', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=749,
  serialized_end=814,
)


_STACKFRAMEWITHID = _descriptor.Descriptor(
  name='StackFrameWithId',
  full_name='tensorflow.StackFrameWithId',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='tensorflow.StackFrameWithId.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_line_col', full_name='tensorflow.StackFrameWithId.file_line_col', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=816,
  serialized_end=909,
)


_CODELOCATION = _descriptor.Descriptor(
  name='CodeLocation',
  full_name='tensorflow.CodeLocation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='host_name', full_name='tensorflow.CodeLocation.host_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stack_frame_ids', full_name='tensorflow.CodeLocation.stack_frame_ids', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=911,
  serialized_end=969,
)


_GRAPHOPCREATION = _descriptor.Descriptor(
  name='GraphOpCreation',
  full_name='tensorflow.GraphOpCreation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op_type', full_name='tensorflow.GraphOpCreation.op_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_name', full_name='tensorflow.GraphOpCreation.op_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_name', full_name='tensorflow.GraphOpCreation.graph_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_id', full_name='tensorflow.GraphOpCreation.graph_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_name', full_name='tensorflow.GraphOpCreation.device_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_names', full_name='tensorflow.GraphOpCreation.input_names', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_outputs', full_name='tensorflow.GraphOpCreation.num_outputs', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code_location', full_name='tensorflow.GraphOpCreation.code_location', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_tensor_ids', full_name='tensorflow.GraphOpCreation.output_tensor_ids', index=8,
      number=9, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=972,
  serialized_end=1200,
)


_DEBUGGEDGRAPH = _descriptor.Descriptor(
  name='DebuggedGraph',
  full_name='tensorflow.DebuggedGraph',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='graph_id', full_name='tensorflow.DebuggedGraph.graph_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_name', full_name='tensorflow.DebuggedGraph.graph_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='instrumented_ops', full_name='tensorflow.DebuggedGraph.instrumented_ops', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_graph_def', full_name='tensorflow.DebuggedGraph.original_graph_def', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='instrumented_graph_def', full_name='tensorflow.DebuggedGraph.instrumented_graph_def', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='outer_context_id', full_name='tensorflow.DebuggedGraph.outer_context_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1203,
  serialized_end=1368,
)


_DEBUGGEDDEVICE = _descriptor.Descriptor(
  name='DebuggedDevice',
  full_name='tensorflow.DebuggedDevice',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='device_name', full_name='tensorflow.DebuggedDevice.device_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_id', full_name='tensorflow.DebuggedDevice.device_id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1370,
  serialized_end=1426,
)


_EXECUTION = _descriptor.Descriptor(
  name='Execution',
  full_name='tensorflow.Execution',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='op_type', full_name='tensorflow.Execution.op_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_outputs', full_name='tensorflow.Execution.num_outputs', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='graph_id', full_name='tensorflow.Execution.graph_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='input_tensor_ids', full_name='tensorflow.Execution.input_tensor_ids', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_tensor_ids', full_name='tensorflow.Execution.output_tensor_ids', index=4,
      number=5, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensor_debug_mode', full_name='tensorflow.Execution.tensor_debug_mode', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensor_protos', full_name='tensorflow.Execution.tensor_protos', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code_location', full_name='tensorflow.Execution.code_location', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_tensor_device_ids', full_name='tensorflow.Execution.output_tensor_device_ids', index=8,
      number=9, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1429,
  serialized_end=1736,
)


_GRAPHEXECUTIONTRACE = _descriptor.Descriptor(
  name='GraphExecutionTrace',
  full_name='tensorflow.GraphExecutionTrace',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tfdbg_context_id', full_name='tensorflow.GraphExecutionTrace.tfdbg_context_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='op_name', full_name='tensorflow.GraphExecutionTrace.op_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='output_slot', full_name='tensorflow.GraphExecutionTrace.output_slot', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensor_debug_mode', full_name='tensorflow.GraphExecutionTrace.tensor_debug_mode', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tensor_proto', full_name='tensorflow.GraphExecutionTrace.tensor_proto', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='device_name', full_name='tensorflow.GraphExecutionTrace.device_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1739,
  serialized_end=1948,
)

_DEBUGEVENT.fields_by_name['debug_metadata'].message_type = _DEBUGMETADATA
_DEBUGEVENT.fields_by_name['source_file'].message_type = _SOURCEFILE
_DEBUGEVENT.fields_by_name['stack_frame_with_id'].message_type = _STACKFRAMEWITHID
_DEBUGEVENT.fields_by_name['graph_op_creation'].message_type = _GRAPHOPCREATION
_DEBUGEVENT.fields_by_name['debugged_graph'].message_type = _DEBUGGEDGRAPH
_DEBUGEVENT.fields_by_name['execution'].message_type = _EXECUTION
_DEBUGEVENT.fields_by_name['graph_execution_trace'].message_type = _GRAPHEXECUTIONTRACE
_DEBUGEVENT.fields_by_name['debugged_device'].message_type = _DEBUGGEDDEVICE
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['debug_metadata'])
_DEBUGEVENT.fields_by_name['debug_metadata'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['source_file'])
_DEBUGEVENT.fields_by_name['source_file'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['stack_frame_with_id'])
_DEBUGEVENT.fields_by_name['stack_frame_with_id'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['graph_op_creation'])
_DEBUGEVENT.fields_by_name['graph_op_creation'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['debugged_graph'])
_DEBUGEVENT.fields_by_name['debugged_graph'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['execution'])
_DEBUGEVENT.fields_by_name['execution'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['graph_execution_trace'])
_DEBUGEVENT.fields_by_name['graph_execution_trace'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['graph_id'])
_DEBUGEVENT.fields_by_name['graph_id'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_DEBUGEVENT.oneofs_by_name['what'].fields.append(
  _DEBUGEVENT.fields_by_name['debugged_device'])
_DEBUGEVENT.fields_by_name['debugged_device'].containing_oneof = _DEBUGEVENT.oneofs_by_name['what']
_STACKFRAMEWITHID.fields_by_name['file_line_col'].message_type = tensorflow_dot_core_dot_protobuf_dot_graph__debug__info__pb2._GRAPHDEBUGINFO_FILELINECOL
_GRAPHOPCREATION.fields_by_name['code_location'].message_type = _CODELOCATION
_EXECUTION.fields_by_name['tensor_debug_mode'].enum_type = _TENSORDEBUGMODE
_EXECUTION.fields_by_name['tensor_protos'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__pb2._TENSORPROTO
_EXECUTION.fields_by_name['code_location'].message_type = _CODELOCATION
_GRAPHEXECUTIONTRACE.fields_by_name['tensor_debug_mode'].enum_type = _TENSORDEBUGMODE
_GRAPHEXECUTIONTRACE.fields_by_name['tensor_proto'].message_type = tensorflow_dot_core_dot_framework_dot_tensor__pb2._TENSORPROTO
DESCRIPTOR.message_types_by_name['DebugEvent'] = _DEBUGEVENT
DESCRIPTOR.message_types_by_name['DebugMetadata'] = _DEBUGMETADATA
DESCRIPTOR.message_types_by_name['SourceFile'] = _SOURCEFILE
DESCRIPTOR.message_types_by_name['StackFrameWithId'] = _STACKFRAMEWITHID
DESCRIPTOR.message_types_by_name['CodeLocation'] = _CODELOCATION
DESCRIPTOR.message_types_by_name['GraphOpCreation'] = _GRAPHOPCREATION
DESCRIPTOR.message_types_by_name['DebuggedGraph'] = _DEBUGGEDGRAPH
DESCRIPTOR.message_types_by_name['DebuggedDevice'] = _DEBUGGEDDEVICE
DESCRIPTOR.message_types_by_name['Execution'] = _EXECUTION
DESCRIPTOR.message_types_by_name['GraphExecutionTrace'] = _GRAPHEXECUTIONTRACE
DESCRIPTOR.enum_types_by_name['TensorDebugMode'] = _TENSORDEBUGMODE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DebugEvent = _reflection.GeneratedProtocolMessageType('DebugEvent', (_message.Message,), {
  'DESCRIPTOR' : _DEBUGEVENT,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.DebugEvent)
  })
_sym_db.RegisterMessage(DebugEvent)

DebugMetadata = _reflection.GeneratedProtocolMessageType('DebugMetadata', (_message.Message,), {
  'DESCRIPTOR' : _DEBUGMETADATA,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.DebugMetadata)
  })
_sym_db.RegisterMessage(DebugMetadata)

SourceFile = _reflection.GeneratedProtocolMessageType('SourceFile', (_message.Message,), {
  'DESCRIPTOR' : _SOURCEFILE,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.SourceFile)
  })
_sym_db.RegisterMessage(SourceFile)

StackFrameWithId = _reflection.GeneratedProtocolMessageType('StackFrameWithId', (_message.Message,), {
  'DESCRIPTOR' : _STACKFRAMEWITHID,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.StackFrameWithId)
  })
_sym_db.RegisterMessage(StackFrameWithId)

CodeLocation = _reflection.GeneratedProtocolMessageType('CodeLocation', (_message.Message,), {
  'DESCRIPTOR' : _CODELOCATION,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.CodeLocation)
  })
_sym_db.RegisterMessage(CodeLocation)

GraphOpCreation = _reflection.GeneratedProtocolMessageType('GraphOpCreation', (_message.Message,), {
  'DESCRIPTOR' : _GRAPHOPCREATION,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.GraphOpCreation)
  })
_sym_db.RegisterMessage(GraphOpCreation)

DebuggedGraph = _reflection.GeneratedProtocolMessageType('DebuggedGraph', (_message.Message,), {
  'DESCRIPTOR' : _DEBUGGEDGRAPH,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.DebuggedGraph)
  })
_sym_db.RegisterMessage(DebuggedGraph)

DebuggedDevice = _reflection.GeneratedProtocolMessageType('DebuggedDevice', (_message.Message,), {
  'DESCRIPTOR' : _DEBUGGEDDEVICE,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.DebuggedDevice)
  })
_sym_db.RegisterMessage(DebuggedDevice)

Execution = _reflection.GeneratedProtocolMessageType('Execution', (_message.Message,), {
  'DESCRIPTOR' : _EXECUTION,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.Execution)
  })
_sym_db.RegisterMessage(Execution)

GraphExecutionTrace = _reflection.GeneratedProtocolMessageType('GraphExecutionTrace', (_message.Message,), {
  'DESCRIPTOR' : _GRAPHEXECUTIONTRACE,
  '__module__' : 'tensorflow.core.protobuf.debug_event_pb2'
  # @@protoc_insertion_point(class_scope:tensorflow.GraphExecutionTrace)
  })
_sym_db.RegisterMessage(GraphExecutionTrace)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)
