// Copyright (C) CVAT.ai Corporation
//
// SPDX-License-Identifier: MIT

@import 'base';

.cvat-requests-page {
    height: 100%;
    padding-top: $grid-unit-size * 2;
    padding-bottom: $grid-unit-size;

    > div:nth-child(1) {
        height: 90%;
    }

    > div:nth-child(2) {
        padding-top: $grid-unit-size * 2;
    }
}

.cvat-requests-list {
    height: 100%;
    overflow-y: auto;
    margin: $grid-unit-size;

    .ant-card .ant-card-body {
        padding: $grid-unit-size * 2;
    }
}

.cvat-requests-type, .cvat-requests-name, .cvat-requests-percent {
    display: flex;
    align-items: center;
}

.cvat-requests-percent {
    padding-left: $grid-unit-size * 2;
}

.cvat-requests-card {
    height: $grid-unit-size * 12;
    width: calc(100% - $grid-unit-size * 2);
    border: 1px solid $border-color-1;
    border-radius: $border-radius-base;
    background: $background-color-1;
    margin: $grid-unit-size;

    .cvat-request-item-progress-wrapper {
        > .ant-row {
            margin-bottom: -$grid-unit-size;

            span {
                font-size: 12px;
            }

            span[role="img"] {
                margin-left: $grid-unit-size;
            }
        }
    }

    .ant-card-body {
        width: 100%;
        height: 100%;
    }

    &:hover {
        transition: box-shadow $box-shadow-transition;
        box-shadow: $box-shadow-base;
    }
}

.cvat-request-item-progress-message {
    @extend .cvat-scrollbar;

    max-height: $grid-unit-size * 8;
    overflow: hidden auto;
    display: block;
}

.cvat-requests-page-actions-button {
    border: none;
    padding: 0;
    padding-top: $grid-unit-size * 4;
    display: flex;
    align-items: center;
    line-height: 14px;
    color: black;

    &:hover, &:focus {
        color: black;
    }
}

.cvat-request-actions-menu {
    box-shadow: $box-shadow-base;

    > li:hover {
        background-color: $hover-menu-color;
    }

    .ant-menu-submenu-title {
        margin: 0;
        width: 13em;
    }
}

.cvat-request-tooltip {
    border-bottom: 1px dotted;
}

.cvat-request-tooltip-inner {
    span {
        color: white;
    }
}

.cvat-empty-requests-list {
    .ant-empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
