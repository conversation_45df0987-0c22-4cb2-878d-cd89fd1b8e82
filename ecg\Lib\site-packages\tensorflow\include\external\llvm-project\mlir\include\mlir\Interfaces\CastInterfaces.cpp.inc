/*===- TableGen'erated file -------------------------------------*- C++ -*-===*\
|*                                                                            *|
|* Interface Definitions                                                      *|
|*                                                                            *|
|* Automatically generated file, do not edit!                                 *|
|*                                                                            *|
\*===----------------------------------------------------------------------===*/

bool mlir::CastOpInterface::areCastCompatible(mlir::TypeRange inputs, mlir::TypeRange outputs) {
      return getImpl()->areCastCompatible(inputs, outputs);
  }
