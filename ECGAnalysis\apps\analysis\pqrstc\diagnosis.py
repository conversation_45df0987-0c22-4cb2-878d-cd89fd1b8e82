import numpy as np
from apps.utils.logger_helper import Logger
from apps.analysis.common import detect_wave, heart_rate_calculation
from apps.analysis.common.ecg_diagnosis_processing import med_cal
from apps.models.arrhythmia_models import PQRSTCEntity
from apps.analysis.common import ecg_qrs_processing


def process(ecg_data, fs):
    """
    ECG信号指标
    :param ecg_data: ECG信号数据
    :param fs: 采样率
    :return: PQRSTCEntity对象或None
    """
    rr_intervals, cv_rr, SNA = med_cal(ecg_data, fs)

    # 检查 rr_intervals 是否为 None 或空
    if rr_intervals is None or len(rr_intervals) == 0:
        Logger().error("Error: RR intervals are invalid, cannot compute heart rate.")
        return None  # 或者返回一个默认的值或错误标志

    try:
        hr = int(60 / np.mean(rr_intervals))
    except ZeroDivisionError:
        Logger().error("Error: Mean of RR intervals is zero, cannot compute heart rate.")
        return None  # 或者返回一个默认的值或错误标志

    # RR间期暂时没有返回
    try:
        rr_average_num, qrs_average_num, qt_average_num, st_average_num, pr_average_num, pp_average_num, qtc_average_num = ecg_qrs_processing.process_ecg_dc(
            ecg_data, fs)
    except Exception as e:
        Logger().error(f"Error during ECG processing: {str(e)}")
        rr_average_num, qrs_average_num, qt_average_num, st_average_num, pr_average_num, pp_average_num, qtc_average_num = 0, 0, 0, 0, 0, 0, 0

    pqrstc = PQRSTCEntity()

    pqrstc.HR = hr
    pqrstc.QRS_duration = round(qrs_average_num * 1000, 5)
    pqrstc.QT = round(qt_average_num * 1000, 5)
    pqrstc.QTc = round(qtc_average_num * 1000, 5)

    p_duration = round(pp_average_num * 1000, 5)
    pqrstc.P_duration = p_duration if 70 <= p_duration <= 150 else 154.5

    pqrstc.PR_interval = round(pr_average_num * 1000, 5)
    pqrstc.ST_duration = round(st_average_num * 1000, 5)

    return pqrstc
