<html>
  <head>
    <link rel="stylesheet" href="{{ prefix }}/_static/css/page.css" type="text/css">
    <link rel="stylesheet" href="{{ prefix }}/_static/css/boilerplate.css" type="text/css" />
    <link rel="stylesheet" href="{{ prefix }}/_static/css/fbm.css" type="text/css" />
    <link rel="stylesheet" href="{{ prefix }}/_static/jquery-ui-1.12.1/jquery-ui.min.css" >
    <script src="{{ prefix }}/_static/jquery-ui-1.12.1/external/jquery/jquery.js"></script>
    <script src="{{ prefix }}/_static/jquery-ui-1.12.1/jquery-ui.min.js"></script>
    <script src="{{ prefix }}/_static/js/mpl_tornado.js"></script>
    <script src="{{ prefix }}/js/mpl.js"></script>

    <script>
      {% for (fig_id, fig_manager) in figures %}
        $(document).ready(
        function() {
          var main_div = $('div#figures');
          var figure_div = $('<div id="figure-div"/>')
          main_div.append(figure_div);
          var websocket_type = mpl.get_websocket_type();
          var websocket = new websocket_type(
              "{{ ws_uri }}" + "{{ fig_id }}" + "/ws");
          var fig = new mpl.figure(
              "{{ fig_id }}", websocket, mpl_ondownload, figure_div);

          fig.focus_on_mouseover = true;

          $(fig.canvas).attr('tabindex', {{ fig_id }});
          }
        );

	{% end %}
    </script>

  <title>MPL | WebAgg current figures</title>

  </head>
  <body>
    <div id="mpl-warnings" class="mpl-warnings"></div>

    <div id="figures" style="margin: 10px 10px;"></div>

  </body>
</html>
