# Generated by Django 3.2.14 on 2022-07-26 09:26

import django.db.models.deletion
from django.db import migrations, models

import cvat.apps.engine.models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0056_jobs_previews"),
    ]

    operations = [
        migrations.CreateModel(
            name="LabeledSkeleton",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("frame", models.PositiveIntegerField()),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("rectangle", "RECTANGLE"),
                            ("polygon", "POLYGON"),
                            ("polyline", "POLYLINE"),
                            ("points", "POINTS"),
                            ("ellipse", "ELLIPSE"),
                            ("cuboid", "CUBOID"),
                            ("skeleton", "SKELETON"),
                        ],
                        max_length=16,
                    ),
                ),
                ("occluded", models.BooleanField(default=False)),
                ("outside", models.BooleanField(default=False)),
                ("points", cvat.apps.engine.models.FloatArrayField(default="")),
            ],
        ),
        migrations.CreateModel(
            name="TrackedSkeleton",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("rectangle", "RECTANGLE"),
                            ("polygon", "POLYGON"),
                            ("polyline", "POLYLINE"),
                            ("points", "POINTS"),
                            ("ellipse", "ELLIPSE"),
                            ("cuboid", "CUBOID"),
                            ("skeleton", "SKELETON"),
                        ],
                        max_length=16,
                    ),
                ),
                ("occluded", models.BooleanField(default=False)),
                ("outside", models.BooleanField(default=False)),
                ("points", cvat.apps.engine.models.FloatArrayField(default="")),
            ],
        ),
        migrations.AddField(
            model_name="label",
            name="parent",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="sublabels",
                to="engine.label",
            ),
        ),
        migrations.AddField(
            model_name="label",
            name="type",
            field=models.CharField(
                choices=[
                    ("bbox", "BBOX"),
                    ("ellipse", "ELLIPSE"),
                    ("polygon", "POLYGON"),
                    ("polyline", "POLYLINE"),
                    ("points", "POINTS"),
                    ("cuboid", "CUBOID"),
                    ("cuboid_3d", "CUBOID_3D"),
                    ("skeleton", "SKELETON"),
                    ("tag", "TAG"),
                    ("any", "ANY"),
                ],
                default=cvat.apps.engine.models.LabelType["ANY"],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="labeledshape",
            name="points",
            field=cvat.apps.engine.models.FloatArrayField(default=[]),
        ),
        migrations.AlterField(
            model_name="labeledshape",
            name="type",
            field=models.CharField(
                choices=[
                    ("rectangle", "RECTANGLE"),
                    ("polygon", "POLYGON"),
                    ("polyline", "POLYLINE"),
                    ("points", "POINTS"),
                    ("ellipse", "ELLIPSE"),
                    ("cuboid", "CUBOID"),
                    ("skeleton", "SKELETON"),
                ],
                max_length=16,
            ),
        ),
        migrations.AlterField(
            model_name="trackedshape",
            name="points",
            field=cvat.apps.engine.models.FloatArrayField(default=[]),
        ),
        migrations.AlterField(
            model_name="trackedshape",
            name="type",
            field=models.CharField(
                choices=[
                    ("rectangle", "RECTANGLE"),
                    ("polygon", "POLYGON"),
                    ("polyline", "POLYLINE"),
                    ("points", "POINTS"),
                    ("ellipse", "ELLIPSE"),
                    ("cuboid", "CUBOID"),
                    ("skeleton", "SKELETON"),
                ],
                max_length=16,
            ),
        ),
        migrations.AlterUniqueTogether(
            name="label",
            unique_together={("task", "name", "parent")},
        ),
        migrations.CreateModel(
            name="TrackedSkeletonAttributeVal",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("value", cvat.apps.engine.models.SafeCharField(max_length=4096)),
                (
                    "skeleton",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="engine.trackedskeleton"
                    ),
                ),
                (
                    "spec",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="engine.attributespec"
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_permissions": (),
            },
        ),
        migrations.AddField(
            model_name="trackedskeleton",
            name="label",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="engine.label"),
        ),
        migrations.AddField(
            model_name="trackedskeleton",
            name="shape",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="engine.trackedshape"
            ),
        ),
        migrations.CreateModel(
            name="LabeledSkeletonAttributeVal",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("value", cvat.apps.engine.models.SafeCharField(max_length=4096)),
                (
                    "skeleton",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="engine.labeledskeleton"
                    ),
                ),
                (
                    "spec",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="engine.attributespec"
                    ),
                ),
            ],
            options={
                "abstract": False,
                "default_permissions": (),
            },
        ),
        migrations.AddField(
            model_name="labeledskeleton",
            name="label",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="engine.label"),
        ),
        migrations.AddField(
            model_name="labeledskeleton",
            name="shape",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="engine.labeledshape"
            ),
        ),
        migrations.CreateModel(
            name="Skeleton",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
                    ),
                ),
                ("svg", models.TextField(default=None, null=True)),
                (
                    "root",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE, to="engine.label"
                    ),
                ),
            ],
            options={
                "default_permissions": (),
                "unique_together": {("root",)},
            },
        ),
    ]
