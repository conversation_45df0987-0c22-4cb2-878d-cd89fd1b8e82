syntax = "proto3";

package tensorflow.profiler;

option cc_enable_arenas = true;

// A container of parallel XPlanes, generated by one or more profiling sources.
// Next ID: 5
message XSpace {
  repeated XPlane planes = 1;
  // Errors (if any) in the generation of planes.
  repeated string errors = 2;
  // Warnings (if any) in the generation of planes;
  repeated string warnings = 3;
  // List of hostnames that XPlanes are generated from.
  repeated string hostnames = 4;
}

// An XPlane is a container of parallel timelines (XLines), generated by a
// profiling source or by post-processing one or more XPlanes.
// Next ID: 7
message XPlane {
  int64 id = 1;

  // Name of this line.
  string name = 2;

  // Parallel timelines grouped in this plane. XLines with the same id
  // are effectively the same timeline.
  repeated XLine lines = 3;

  // XEventMetadata map, each entry uses the XEventMetadata.id as key. This map
  // should be used for events that share the same ID over the whole XPlane.
  map<int64, XEventMetadata> event_metadata = 4;

  // XStatMetadata map, each entry uses the XStatMetadata.id as key. This map
  // should be used for stats that share the same ID over the whole XPlane.
  map<int64, XStatMetadata> stat_metadata = 5;

  // XStats associated with this plane, e.g. device capabilities.
  // Each of these XStats should have a different metadata_id.
  repeated XStat stats = 6;
}

// An XLine is a timeline of trace events (XEvents).
// Next ID: 12
message XLine {
  // Id of this line, can be repeated within an XPlane. All XLines with the
  // same id are effectively the same timeline.
  int64 id = 1;

  // Display id of this line. Multiple lines with the same display_id are
  // grouped together in the same trace viewer row.
  int64 display_id = 10;

  // Name of this XLine.
  string name = 2;

  // Name of this XLine to display in trace viewer.
  string display_name = 11;

  // Start time of this line in nanoseconds since the UNIX epoch.
  // XEvent.offset_ps is relative to this timestamp.
  int64 timestamp_ns = 3;

  // Profiling duration for this line in picoseconds.
  int64 duration_ps = 9;

  // XEvents within the same XLine should not overlap in time, but they can be
  // nested.
  repeated XEvent events = 4;

  reserved 5, 6, 7, 8;
}

// An XEvent is a trace event, optionally annotated with XStats.
// Next ID: 6
message XEvent {
  // XEventMetadata.id of corresponding metadata.
  int64 metadata_id = 1;

  oneof data {
    // Start time of the event in picoseconds, as offset from
    // XLine.timestamp_ns().
    int64 offset_ps = 2;

    // Number of occurrences of the event, if aggregated.
    int64 num_occurrences = 5;
  }

  // Duration of the event in picoseconds. Can be zero for an instant event.
  int64 duration_ps = 3;

  // XStats associated with the event.
  // Each of these XStats should have a different metadata_id.
  repeated XStat stats = 4;
}

// An XStat is a named value associated with an XEvent, e.g., a performance
// counter value, a metric computed by a formula applied over nested XEvents
// and XStats.
// Next ID: 8
message XStat {
  // XStatMetadata.id of corresponding metadata.
  int64 metadata_id = 1;

  // Value of this stat.
  oneof value {
    double double_value = 2;
    uint64 uint64_value = 3;
    int64 int64_value = 4;
    string str_value = 5;
    bytes bytes_value = 6;
    // A string value that stored in XStatMetadata::name.
    uint64 ref_value = 7;
  }
}

// Metadata for an XEvent, corresponds to an event type and is shared by
// all XEvents with the same metadata_id.
// Next ID: 7
message XEventMetadata {
  // XPlane.event_metadata map key.
  int64 id = 1;

  // Name of the event.
  string name = 2;

  // Name of the event shown in trace viewer.
  string display_name = 4;

  // Additional metadata in serialized format.
  bytes metadata = 3;

  // XStats that are constant for all XEvents with the same metadata_id.
  // Each of these XStats should have a different metadata_id.
  repeated XStat stats = 5;

  // XPlane.event_metadata map key for children events.
  repeated int64 child_id = 6;
}

// Metadata for an XStat, corresponds to a stat type and is shared by all
// XStats with the same metadata_id.
// Next ID: 4
message XStatMetadata {
  // XPlane.stat_metadata map key.
  int64 id = 1;

  // Name of the stat (should be short).
  // Two XStatMetadata with different id should have different names.
  string name = 2;

  // Description of the stat (might be long).
  string description = 3;
}
