apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-frontend-service
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "cvat.labels" . | nindent 4 }}
    app: cvat-app
    tier: frontend
  annotations:
    {{- with .Values.cvat.frontend.service.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    app: cvat-app
    tier: frontend
    {{- include "cvat.labels" . | nindent 4 }}
  {{- with .Values.cvat.frontend.service }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
