import numpy as np
from scipy import signal, interpolate


def resample(ecg_data, sampling_rate):
    """
    重采样
    :param ecg_data: ECG信号数据
    :param sampling_rate: 采样率
    :return: 重采样后的心电数据
    """
    model_sampling_rate = 500  # 模型采样率

    # 如果采样率与模型采样率不匹配进行重采样
    if sampling_rate != model_sampling_rate:
        duration = len(ecg_data) / sampling_rate  # 计算持续时间

        t_original = np.linspace(0, duration, int(sampling_rate * duration))  # 生成指定范围内的等间隔数值

        # 低通滤波器设计：用于滤除超过奈奎斯特频率的成分
        b, a = signal.butter(6, 0.95, btype='low')
        ecg_data_filtered = signal.filtfilt(b, a, ecg_data)  # 应用滤波器

        # 线性插值重采样
        t_resampled = np.linspace(0, duration, int(duration * model_sampling_rate))
        linear_interp = interpolate.interp1d(t_original, ecg_data_filtered, kind='linear')
        ecg_data = linear_interp(t_resampled)

    return ecg_data
