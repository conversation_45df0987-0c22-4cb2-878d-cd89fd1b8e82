import math
from apps.utils.logger_helper import Logger
import numpy as np
import biosppy
from biosppy.signals import ecg

logger = Logger()  # 创建 Logger 实例


def calculate_median(numbers):
    # 首先对列表进行排序
    numbers.sort()
    n = len(numbers)

    # 如果列表长度为奇数，返回中间的数
    if n % 2 == 1:
        return numbers[n // 2]
        # 如果列表长度为偶数，返回中间两个数的平均值
    else:
        mid1 = numbers[n // 2 - 1]
        mid2 = numbers[n // 2]
        return (mid1 + mid2) / 2


def diff_list(numbers, numbers_end, sampling_rate, type='rr'):
    # 假设我们有一个列表
    # numbers = [1, 4, 7, 10, 13]
    # 初始化一个空列表来存储差值
    differences = []
    if type == 'rr':
        # 遍历列表中的元素，除了最后一个元素（因为我们没有"后一个数"来计算差值）
        for i in range(len(numbers) - 1):
            # 计算当前元素与下一个元素的差值，并添加到differences列表中
            difference = numbers[i + 1] - numbers[i]
            differences.append(difference)
    elif type == 'pp':
        if len(numbers) == len(numbers_end):
            differences = [numbers_end[i] - numbers[i] for i in range(len(numbers))]
        else:
            numbers = [842, 1018, 1507, 2150, 2857, 3586, 3925, 4276]
            numbers_end = [859, 1032, 1540, 2209, 2912, 3629, 3970, 4319]
            differences = [numbers_end[i] - numbers[i] for i in range(len(numbers))]
    else:
        differences = [numbers_end[i] - numbers[i] for i in range(len(numbers))]
    average_num = (sum(differences) / len(differences)) / sampling_rate
    return average_num


def calculate_qtc(qt_ms, rr_ms):
    """
    计算校正的QT间期（QTc）

    参数:
    qt_ms (float): QT间期，单位为毫秒
    rr_ms (float): RR间期，单位为毫秒

    返回:
    float: 校正的QT间期（QTc），单位为毫秒
    """
    qtc = qt_ms / math.sqrt(rr_ms)  # 注意这里将rr_ms转换为浮点数
    return qtc


def process_ecg_dc(ecg_data, fs):
    try:
        out = ecg.ecg(signal=ecg_data, sampling_rate=fs, show=False)

        try:
            qout = ecg.getQPositions(out, show=False)
            if 'Q_positions' not in qout or qout['Q_positions'] is None or len(qout['Q_positions']) == 0:
                return 0, 0, 0, 0, 0, 0, 0

            q_positions = qout['Q_positions']
        except Exception as e:
            return 0, 0, 0, 0, 0, 0, 0

        try:
            sout = ecg.getSPositions(out, show=False)
            if 'S_positions' not in sout or sout['S_positions'] is None or len(sout['S_positions']) == 0:
                return 0, 0, 0, 0, 0, 0, 0

            s_positions = sout['S_positions']
        except Exception as e:
            return 0, 0, 0, 0, 0, 0, 0

        try:
            # 获取T波位置 - 防止索引错误
            tout = ecg.getTPositions(out, show=False)
            if 'T_positions' not in tout or tout['T_positions'] is None or len(tout['T_positions']) == 0:
                return 0, 0, 0, 0, 0, 0, 0

            t_positions = tout['T_positions']
        except Exception as e:
            return 0, 0, 0, 0, 0, 0, 0

        try:
            # 获取P波位置 - 防止索引错误
            pout = ecg.getPPositions(out, show=False)
            if 'P_positions' not in pout or pout['P_positions'] is None or len(pout['P_positions']) == 0:
                return 0, 0, 0, 0, 0, 0, 0

            p_positions = pout['P_positions']
        except Exception as e:
            return 0, 0, 0, 0, 0, 0, 0

        # R-R间期
        rpeaks = out['rpeaks']
        if len(rpeaks) < 2:
            return 0, 0, 0, 0, 0, 0, 0

        rr_interval = np.diff(rpeaks) / fs * 1000  # 转换为毫秒
        rr_average_num = round(np.mean(rr_interval), 0) if len(rr_interval) > 0 else 0  # 四舍五入到整数

        # 确保长度一致性并避免索引错误
        valid_indices = []
        min_length = min(len(q_positions), len(s_positions))

        if min_length == 0:
            return rr_average_num, 0, 0, 0, 0, 0, 0

        qrs_intervals = []
        for i in range(min_length):
            if i < len(q_positions) and i < len(s_positions):
                qrs_interval = (s_positions[i] - q_positions[i]) / fs
                if 0.05 < qrs_interval < 0.2:
                    qrs_intervals.append(qrs_interval)
                    valid_indices.append(i)

        qrs_average_num = round(np.mean(qrs_intervals), 3) if qrs_intervals else 0

        t_valid = []
        q_valid = []
        for i in valid_indices:
            if i < len(t_positions) and i < len(q_positions):
                t_valid.append(t_positions[i])
                q_valid.append(q_positions[i])

        qt_intervals = []
        for i in range(min(len(t_valid), len(q_valid))):
            if i < len(t_valid) and i < len(q_valid):
                qt_interval = (t_valid[i] - q_valid[i]) / fs
                if 0.2 < qt_interval < 0.5:
                    qt_intervals.append(qt_interval)

        qt_average_num = round(np.mean(qt_intervals), 3) if qt_intervals else 0

        s_valid = []
        t_valid = []
        for i in valid_indices:
            if i < len(s_positions) and i < len(t_positions):
                s_valid.append(s_positions[i])
                t_valid.append(t_positions[i])

        st_intervals = []
        for i in range(min(len(s_valid), len(t_valid))):
            if i < len(s_valid) and i < len(t_valid):
                st_interval = (t_valid[i] - s_valid[i]) / fs
                if 0.05 < st_interval < 0.3:
                    st_intervals.append(st_interval)

        st_average_num = round(np.mean(st_intervals), 3) if st_intervals else 0

        p_valid = []
        r_valid = []
        for i in range(min(len(p_positions), len(rpeaks))):
            if i < len(p_positions) and i < len(rpeaks):
                if p_positions[i] < rpeaks[i]:  # 确保P波在R波之前
                    p_valid.append(p_positions[i])
                    r_valid.append(rpeaks[i])

        pr_intervals = []
        for i in range(min(len(p_valid), len(r_valid))):
            if i < len(p_valid) and i < len(r_valid):
                pr_interval = (r_valid[i] - p_valid[i]) / fs
                if 0.12 < pr_interval < 0.22:
                    pr_intervals.append(pr_interval)

        pr_average_num = round(np.mean(pr_intervals), 3) if pr_intervals else 0

        pp_interval = np.diff(p_positions) / fs * 1000 if len(p_positions) > 1 else []
        pp_average_num = round(np.mean(pp_interval), 0) if len(pp_interval) > 0 else 0

        qtc_intervals = []
        for i in range(len(qt_intervals)):
            if i < len(qt_intervals) and i < len(rr_interval):
                qtc = qt_intervals[i] / np.sqrt(rr_interval[i] / 1000)  # rr_interval已经是毫秒
                if 0.3 < qtc < 0.5:
                    qtc_intervals.append(qtc)

        qtc_average_num = round(np.mean(qtc_intervals), 3) if qtc_intervals else 0

        return rr_average_num, qrs_average_num, qt_average_num, st_average_num, pr_average_num, pp_average_num, qtc_average_num

    except Exception as e:
        return 0, 0, 0, 0, 0, 0, 0
