/* Autogenerated by mlir-tblgen; don't manually edit */
#ifdef GEN_PASS_CLASSES

//===----------------------------------------------------------------------===//
// RemoveShapeConstraints
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class RemoveShapeConstraintsBase : public ::mlir::FunctionPass {
public:
  using Base = RemoveShapeConstraintsBase;

  RemoveShapeConstraintsBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  RemoveShapeConstraintsBase(const RemoveShapeConstraintsBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("remove-shape-constraints");
  }
  ::llvm::StringRef getArgument() const override { return "remove-shape-constraints"; }

  ::llvm::StringRef getDescription() const override { return "Replace all cstr_ ops with a true witness"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveShapeConstraints");
  }
  ::llvm::StringRef getName() const override { return "RemoveShapeConstraints"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};

//===----------------------------------------------------------------------===//
// ShapeBufferize
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ShapeBufferizeBase : public ::mlir::FunctionPass {
public:
  using Base = ShapeBufferizeBase;

  ShapeBufferizeBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ShapeBufferizeBase(const ShapeBufferizeBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-bufferize");
  }
  ::llvm::StringRef getArgument() const override { return "shape-bufferize"; }

  ::llvm::StringRef getDescription() const override { return "Bufferize the shape dialect."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeBufferize");
  }
  ::llvm::StringRef getName() const override { return "ShapeBufferize"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  registry.insert<memref::MemRefDialect>();

  }

protected:
};

//===----------------------------------------------------------------------===//
// ShapeToShapeLowering
//===----------------------------------------------------------------------===//

template <typename DerivedT>
class ShapeToShapeLoweringBase : public ::mlir::FunctionPass {
public:
  using Base = ShapeToShapeLoweringBase;

  ShapeToShapeLoweringBase() : ::mlir::FunctionPass(::mlir::TypeID::get<DerivedT>()) {}
  ShapeToShapeLoweringBase(const ShapeToShapeLoweringBase &other) : ::mlir::FunctionPass(other) {}

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("shape-to-shape-lowering");
  }
  ::llvm::StringRef getArgument() const override { return "shape-to-shape-lowering"; }

  ::llvm::StringRef getDescription() const override { return "Legalize Shape dialect to be convertible to Standard"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShapeToShapeLowering");
  }
  ::llvm::StringRef getName() const override { return "ShapeToShapeLowering"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// RemoveShapeConstraints Registration
//===----------------------------------------------------------------------===//

inline void registerRemoveShapeConstraintsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createRemoveShapeConstraintsPass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeBufferize Registration
//===----------------------------------------------------------------------===//

inline void registerShapeBufferizePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createShapeBufferizePass();
  });
}

//===----------------------------------------------------------------------===//
// ShapeToShapeLowering Registration
//===----------------------------------------------------------------------===//

inline void registerShapeToShapeLoweringPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return mlir::createShapeToShapeLowering();
  });
}

//===----------------------------------------------------------------------===//
// Shape Registration
//===----------------------------------------------------------------------===//

inline void registerShapePasses() {
  registerRemoveShapeConstraintsPass();
  registerShapeBufferizePass();
  registerShapeToShapeLoweringPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
