# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2014-2015,2019
# <PERSON> <<EMAIL>>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-18 16:02+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: French (http://www.transifex.com/django/django/language/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Advanced options"
msgstr "Options avancées"

msgid "Flat Pages"
msgstr "Pages statiques"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Par exemple, « /a_propos/contact/ ». Vérifiez la présence du caractère « / » "
"en début et en fin de chaîne."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Cette valeur ne peut contenir que des lettres, des chiffres, des points, des "
"soulignés, des tirets, des barres obliques ou des tildes."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Par exemple, « /a_propos/contact ». Vérifiez la présence du caractère « / » "
"en début de chaîne."

msgid "URL is missing a leading slash."
msgstr "Le caractère « / » n'est pas présent en début de chaîne."

msgid "URL is missing a trailing slash."
msgstr "Le caractère « / » n'est pas présent en fin de chaîne."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "La page statique à l’URL %(url)s existe déjà pour le site %(site)s"

msgid "title"
msgstr "titre"

msgid "content"
msgstr "contenu"

msgid "enable comments"
msgstr "autoriser les commentaires"

msgid "template name"
msgstr "nom du gabarit"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Par exemple, « flatpages/contact_page.html ». Sans définition, le système "
"utilisera « flatpages/default.html »."

msgid "registration required"
msgstr "enregistrement requis"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Si coché, seuls les utilisateurs connectés auront la possibilité de voir "
"cette page."

msgid "sites"
msgstr "sites"

msgid "flat page"
msgstr "page statique"

msgid "flat pages"
msgstr "pages statiques"
