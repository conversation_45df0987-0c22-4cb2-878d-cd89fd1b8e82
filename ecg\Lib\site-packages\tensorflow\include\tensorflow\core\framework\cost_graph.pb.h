// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/cost_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
namespace tensorflow {
class CostGraphDef;
class CostGraphDefDefaultTypeInternal;
extern CostGraphDefDefaultTypeInternal _CostGraphDef_default_instance_;
class CostGraphDef_AggregatedCost;
class CostGraphDef_AggregatedCostDefaultTypeInternal;
extern CostGraphDef_AggregatedCostDefaultTypeInternal _CostGraphDef_AggregatedCost_default_instance_;
class CostGraphDef_Node;
class CostGraphDef_NodeDefaultTypeInternal;
extern CostGraphDef_NodeDefaultTypeInternal _CostGraphDef_Node_default_instance_;
class CostGraphDef_Node_InputInfo;
class CostGraphDef_Node_InputInfoDefaultTypeInternal;
extern CostGraphDef_Node_InputInfoDefaultTypeInternal _CostGraphDef_Node_InputInfo_default_instance_;
class CostGraphDef_Node_OutputInfo;
class CostGraphDef_Node_OutputInfoDefaultTypeInternal;
extern CostGraphDef_Node_OutputInfoDefaultTypeInternal _CostGraphDef_Node_OutputInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CostGraphDef* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef>(Arena*);
template<> ::tensorflow::CostGraphDef_AggregatedCost* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_AggregatedCost>(Arena*);
template<> ::tensorflow::CostGraphDef_Node* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node>(Arena*);
template<> ::tensorflow::CostGraphDef_Node_InputInfo* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node_InputInfo>(Arena*);
template<> ::tensorflow::CostGraphDef_Node_OutputInfo* Arena::CreateMaybeMessage<::tensorflow::CostGraphDef_Node_OutputInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CostGraphDef_Node_InputInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node.InputInfo) */ {
 public:
  CostGraphDef_Node_InputInfo();
  virtual ~CostGraphDef_Node_InputInfo();

  CostGraphDef_Node_InputInfo(const CostGraphDef_Node_InputInfo& from);
  CostGraphDef_Node_InputInfo(CostGraphDef_Node_InputInfo&& from) noexcept
    : CostGraphDef_Node_InputInfo() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node_InputInfo& operator=(const CostGraphDef_Node_InputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_Node_InputInfo& operator=(CostGraphDef_Node_InputInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CostGraphDef_Node_InputInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_Node_InputInfo* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node_InputInfo*>(
               &_CostGraphDef_Node_InputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CostGraphDef_Node_InputInfo& a, CostGraphDef_Node_InputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_Node_InputInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_Node_InputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_Node_InputInfo* New() const final {
    return CreateMaybeMessage<CostGraphDef_Node_InputInfo>(nullptr);
  }

  CostGraphDef_Node_InputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_Node_InputInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CostGraphDef_Node_InputInfo& from);
  void MergeFrom(const CostGraphDef_Node_InputInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node_InputInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.Node.InputInfo";
  }
  protected:
  explicit CostGraphDef_Node_InputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPrecedingNodeFieldNumber = 1,
    kPrecedingPortFieldNumber = 2,
  };
  // int32 preceding_node = 1;
  void clear_preceding_node();
  ::PROTOBUF_NAMESPACE_ID::int32 preceding_node() const;
  void set_preceding_node(::PROTOBUF_NAMESPACE_ID::int32 value);

  // int32 preceding_port = 2;
  void clear_preceding_port();
  ::PROTOBUF_NAMESPACE_ID::int32 preceding_port() const;
  void set_preceding_port(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node.InputInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 preceding_node_;
  ::PROTOBUF_NAMESPACE_ID::int32 preceding_port_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef_Node_OutputInfo :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node.OutputInfo) */ {
 public:
  CostGraphDef_Node_OutputInfo();
  virtual ~CostGraphDef_Node_OutputInfo();

  CostGraphDef_Node_OutputInfo(const CostGraphDef_Node_OutputInfo& from);
  CostGraphDef_Node_OutputInfo(CostGraphDef_Node_OutputInfo&& from) noexcept
    : CostGraphDef_Node_OutputInfo() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node_OutputInfo& operator=(const CostGraphDef_Node_OutputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_Node_OutputInfo& operator=(CostGraphDef_Node_OutputInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CostGraphDef_Node_OutputInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_Node_OutputInfo* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node_OutputInfo*>(
               &_CostGraphDef_Node_OutputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CostGraphDef_Node_OutputInfo& a, CostGraphDef_Node_OutputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_Node_OutputInfo* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_Node_OutputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_Node_OutputInfo* New() const final {
    return CreateMaybeMessage<CostGraphDef_Node_OutputInfo>(nullptr);
  }

  CostGraphDef_Node_OutputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_Node_OutputInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CostGraphDef_Node_OutputInfo& from);
  void MergeFrom(const CostGraphDef_Node_OutputInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node_OutputInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.Node.OutputInfo";
  }
  protected:
  explicit CostGraphDef_Node_OutputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 3,
    kSizeFieldNumber = 1,
    kAliasInputPortFieldNumber = 2,
    kDtypeFieldNumber = 4,
  };
  // .tensorflow.TensorShapeProto shape = 3;
  bool has_shape() const;
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int64 size = 1;
  void clear_size();
  ::PROTOBUF_NAMESPACE_ID::int64 size() const;
  void set_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 alias_input_port = 2;
  void clear_alias_input_port();
  ::PROTOBUF_NAMESPACE_ID::int64 alias_input_port() const;
  void set_alias_input_port(::PROTOBUF_NAMESPACE_ID::int64 value);

  // .tensorflow.DataType dtype = 4;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node.OutputInfo)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::tensorflow::TensorShapeProto* shape_;
  ::PROTOBUF_NAMESPACE_ID::int64 size_;
  ::PROTOBUF_NAMESPACE_ID::int64 alias_input_port_;
  int dtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef_Node :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.Node) */ {
 public:
  CostGraphDef_Node();
  virtual ~CostGraphDef_Node();

  CostGraphDef_Node(const CostGraphDef_Node& from);
  CostGraphDef_Node(CostGraphDef_Node&& from) noexcept
    : CostGraphDef_Node() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_Node& operator=(const CostGraphDef_Node& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_Node& operator=(CostGraphDef_Node&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CostGraphDef_Node& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_Node* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_Node*>(
               &_CostGraphDef_Node_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CostGraphDef_Node& a, CostGraphDef_Node& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_Node* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_Node* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_Node* New() const final {
    return CreateMaybeMessage<CostGraphDef_Node>(nullptr);
  }

  CostGraphDef_Node* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_Node>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CostGraphDef_Node& from);
  void MergeFrom(const CostGraphDef_Node& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_Node* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.Node";
  }
  protected:
  explicit CostGraphDef_Node(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef CostGraphDef_Node_InputInfo InputInfo;
  typedef CostGraphDef_Node_OutputInfo OutputInfo;

  // accessors -------------------------------------------------------

  enum : int {
    kInputInfoFieldNumber = 4,
    kOutputInfoFieldNumber = 5,
    kControlInputFieldNumber = 8,
    kNameFieldNumber = 1,
    kDeviceFieldNumber = 2,
    kTemporaryMemorySizeFieldNumber = 6,
    kComputeCostFieldNumber = 9,
    kIdFieldNumber = 3,
    kIsFinalFieldNumber = 7,
    kInaccurateFieldNumber = 17,
    kHostTempMemorySizeFieldNumber = 10,
    kDeviceTempMemorySizeFieldNumber = 11,
    kPersistentMemorySizeFieldNumber = 12,
    kComputeTimeFieldNumber = 14,
    kMemoryTimeFieldNumber = 15,
    kDevicePersistentMemorySizeFieldNumber = 16,
  };
  // repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
  int input_info_size() const;
  void clear_input_info();
  ::tensorflow::CostGraphDef_Node_InputInfo* mutable_input_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >*
      mutable_input_info();
  const ::tensorflow::CostGraphDef_Node_InputInfo& input_info(int index) const;
  ::tensorflow::CostGraphDef_Node_InputInfo* add_input_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >&
      input_info() const;

  // repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
  int output_info_size() const;
  void clear_output_info();
  ::tensorflow::CostGraphDef_Node_OutputInfo* mutable_output_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >*
      mutable_output_info();
  const ::tensorflow::CostGraphDef_Node_OutputInfo& output_info(int index) const;
  ::tensorflow::CostGraphDef_Node_OutputInfo* add_output_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >&
      output_info() const;

  // repeated int32 control_input = 8;
  int control_input_size() const;
  void clear_control_input();
  ::PROTOBUF_NAMESPACE_ID::int32 control_input(int index) const;
  void set_control_input(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_control_input(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      control_input() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_control_input();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // string device = 2;
  void clear_device();
  const std::string& device() const;
  void set_device(const std::string& value);
  void set_device(std::string&& value);
  void set_device(const char* value);
  void set_device(const char* value, size_t size);
  std::string* mutable_device();
  std::string* release_device();
  void set_allocated_device(std::string* device);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device(
      std::string* device);

  // int64 temporary_memory_size = 6;
  void clear_temporary_memory_size();
  ::PROTOBUF_NAMESPACE_ID::int64 temporary_memory_size() const;
  void set_temporary_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 compute_cost = 9;
  void clear_compute_cost();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_cost() const;
  void set_compute_cost(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int32 id = 3;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);

  // bool is_final = 7;
  void clear_is_final();
  bool is_final() const;
  void set_is_final(bool value);

  // bool inaccurate = 17;
  void clear_inaccurate();
  bool inaccurate() const;
  void set_inaccurate(bool value);

  // int64 host_temp_memory_size = 10 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_host_temp_memory_size();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 host_temp_memory_size() const;
  PROTOBUF_DEPRECATED void set_host_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_temp_memory_size = 11 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_temp_memory_size();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_temp_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 persistent_memory_size = 12;
  void clear_persistent_memory_size();
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_memory_size() const;
  void set_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 compute_time = 14;
  void clear_compute_time();
  ::PROTOBUF_NAMESPACE_ID::int64 compute_time() const;
  void set_compute_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 memory_time = 15;
  void clear_memory_time();
  ::PROTOBUF_NAMESPACE_ID::int64 memory_time() const;
  void set_memory_time(::PROTOBUF_NAMESPACE_ID::int64 value);

  // int64 device_persistent_memory_size = 16 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_persistent_memory_size();
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.Node)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo > input_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo > output_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > control_input_;
  mutable std::atomic<int> _control_input_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::PROTOBUF_NAMESPACE_ID::int64 temporary_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_cost_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  bool is_final_;
  bool inaccurate_;
  ::PROTOBUF_NAMESPACE_ID::int64 host_temp_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_temp_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 persistent_memory_size_;
  ::PROTOBUF_NAMESPACE_ID::int64 compute_time_;
  ::PROTOBUF_NAMESPACE_ID::int64 memory_time_;
  ::PROTOBUF_NAMESPACE_ID::int64 device_persistent_memory_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef_AggregatedCost :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef.AggregatedCost) */ {
 public:
  CostGraphDef_AggregatedCost();
  virtual ~CostGraphDef_AggregatedCost();

  CostGraphDef_AggregatedCost(const CostGraphDef_AggregatedCost& from);
  CostGraphDef_AggregatedCost(CostGraphDef_AggregatedCost&& from) noexcept
    : CostGraphDef_AggregatedCost() {
    *this = ::std::move(from);
  }

  inline CostGraphDef_AggregatedCost& operator=(const CostGraphDef_AggregatedCost& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef_AggregatedCost& operator=(CostGraphDef_AggregatedCost&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CostGraphDef_AggregatedCost& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef_AggregatedCost* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef_AggregatedCost*>(
               &_CostGraphDef_AggregatedCost_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CostGraphDef_AggregatedCost& a, CostGraphDef_AggregatedCost& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef_AggregatedCost* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef_AggregatedCost* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef_AggregatedCost* New() const final {
    return CreateMaybeMessage<CostGraphDef_AggregatedCost>(nullptr);
  }

  CostGraphDef_AggregatedCost* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef_AggregatedCost>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CostGraphDef_AggregatedCost& from);
  void MergeFrom(const CostGraphDef_AggregatedCost& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef_AggregatedCost* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef.AggregatedCost";
  }
  protected:
  explicit CostGraphDef_AggregatedCost(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionFieldNumber = 2,
    kCostFieldNumber = 1,
  };
  // string dimension = 2;
  void clear_dimension();
  const std::string& dimension() const;
  void set_dimension(const std::string& value);
  void set_dimension(std::string&& value);
  void set_dimension(const char* value);
  void set_dimension(const char* value, size_t size);
  std::string* mutable_dimension();
  std::string* release_dimension();
  void set_allocated_dimension(std::string* dimension);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_dimension();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_dimension(
      std::string* dimension);

  // float cost = 1;
  void clear_cost();
  float cost() const;
  void set_cost(float value);

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef.AggregatedCost)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dimension_;
  float cost_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CostGraphDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CostGraphDef) */ {
 public:
  CostGraphDef();
  virtual ~CostGraphDef();

  CostGraphDef(const CostGraphDef& from);
  CostGraphDef(CostGraphDef&& from) noexcept
    : CostGraphDef() {
    *this = ::std::move(from);
  }

  inline CostGraphDef& operator=(const CostGraphDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline CostGraphDef& operator=(CostGraphDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const CostGraphDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CostGraphDef* internal_default_instance() {
    return reinterpret_cast<const CostGraphDef*>(
               &_CostGraphDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(CostGraphDef& a, CostGraphDef& b) {
    a.Swap(&b);
  }
  inline void Swap(CostGraphDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CostGraphDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline CostGraphDef* New() const final {
    return CreateMaybeMessage<CostGraphDef>(nullptr);
  }

  CostGraphDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<CostGraphDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const CostGraphDef& from);
  void MergeFrom(const CostGraphDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CostGraphDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CostGraphDef";
  }
  protected:
  explicit CostGraphDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef CostGraphDef_Node Node;
  typedef CostGraphDef_AggregatedCost AggregatedCost;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 1,
    kCostFieldNumber = 2,
  };
  // repeated .tensorflow.CostGraphDef.Node node = 1;
  int node_size() const;
  void clear_node();
  ::tensorflow::CostGraphDef_Node* mutable_node(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >*
      mutable_node();
  const ::tensorflow::CostGraphDef_Node& node(int index) const;
  ::tensorflow::CostGraphDef_Node* add_node();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >&
      node() const;

  // repeated .tensorflow.CostGraphDef.AggregatedCost cost = 2;
  int cost_size() const;
  void clear_cost();
  ::tensorflow::CostGraphDef_AggregatedCost* mutable_cost(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >*
      mutable_cost();
  const ::tensorflow::CostGraphDef_AggregatedCost& cost(int index) const;
  ::tensorflow::CostGraphDef_AggregatedCost* add_cost();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >&
      cost() const;

  // @@protoc_insertion_point(class_scope:tensorflow.CostGraphDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node > node_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost > cost_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CostGraphDef_Node_InputInfo

// int32 preceding_node = 1;
inline void CostGraphDef_Node_InputInfo::clear_preceding_node() {
  preceding_node_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CostGraphDef_Node_InputInfo::preceding_node() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.InputInfo.preceding_node)
  return preceding_node_;
}
inline void CostGraphDef_Node_InputInfo::set_preceding_node(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  preceding_node_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.InputInfo.preceding_node)
}

// int32 preceding_port = 2;
inline void CostGraphDef_Node_InputInfo::clear_preceding_port() {
  preceding_port_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CostGraphDef_Node_InputInfo::preceding_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.InputInfo.preceding_port)
  return preceding_port_;
}
inline void CostGraphDef_Node_InputInfo::set_preceding_port(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  preceding_port_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.InputInfo.preceding_port)
}

// -------------------------------------------------------------------

// CostGraphDef_Node_OutputInfo

// int64 size = 1;
inline void CostGraphDef_Node_OutputInfo::clear_size() {
  size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node_OutputInfo::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.size)
  return size_;
}
inline void CostGraphDef_Node_OutputInfo::set_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.size)
}

// int64 alias_input_port = 2;
inline void CostGraphDef_Node_OutputInfo::clear_alias_input_port() {
  alias_input_port_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node_OutputInfo::alias_input_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.alias_input_port)
  return alias_input_port_;
}
inline void CostGraphDef_Node_OutputInfo::set_alias_input_port(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  alias_input_port_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.alias_input_port)
}

// .tensorflow.TensorShapeProto shape = 3;
inline bool CostGraphDef_Node_OutputInfo::has_shape() const {
  return this != internal_default_instance() && shape_ != nullptr;
}
inline const ::tensorflow::TensorShapeProto& CostGraphDef_Node_OutputInfo::shape() const {
  const ::tensorflow::TensorShapeProto* p = shape_;
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::TensorShapeProto*>(
      &::tensorflow::_TensorShapeProto_default_instance_);
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  
  ::tensorflow::TensorShapeProto* temp = shape_;
  shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CostGraphDef_Node_OutputInfo::mutable_shape() {
  
  if (shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaNoVirtual());
    shape_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.OutputInfo.shape)
  return shape_;
}
inline void CostGraphDef_Node_OutputInfo::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape)->GetArena();
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.OutputInfo.shape)
}

// .tensorflow.DataType dtype = 4;
inline void CostGraphDef_Node_OutputInfo::clear_dtype() {
  dtype_ = 0;
}
inline ::tensorflow::DataType CostGraphDef_Node_OutputInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.OutputInfo.dtype)
  return static_cast< ::tensorflow::DataType >(dtype_);
}
inline void CostGraphDef_Node_OutputInfo::set_dtype(::tensorflow::DataType value) {
  
  dtype_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.OutputInfo.dtype)
}

// -------------------------------------------------------------------

// CostGraphDef_Node

// string name = 1;
inline void CostGraphDef_Node::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CostGraphDef_Node::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.name)
  return name_.Get();
}
inline void CostGraphDef_Node::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.name)
}
inline void CostGraphDef_Node::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CostGraphDef.Node.name)
}
inline void CostGraphDef_Node::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CostGraphDef.Node.name)
}
inline void CostGraphDef_Node::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CostGraphDef.Node.name)
}
inline std::string* CostGraphDef_Node::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CostGraphDef_Node::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CostGraphDef_Node::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.name)
}
inline std::string* CostGraphDef_Node::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.Node.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CostGraphDef_Node::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.Node.name)
}

// string device = 2;
inline void CostGraphDef_Node::clear_device() {
  device_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CostGraphDef_Node::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device)
  return device_.Get();
}
inline void CostGraphDef_Node::set_device(const std::string& value) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device)
}
inline void CostGraphDef_Node::set_device(std::string&& value) {
  
  device_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CostGraphDef.Node.device)
}
inline void CostGraphDef_Node::set_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CostGraphDef.Node.device)
}
inline void CostGraphDef_Node::set_device(const char* value,
    size_t size) {
  
  device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CostGraphDef.Node.device)
}
inline std::string* CostGraphDef_Node::mutable_device() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.device)
  return device_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CostGraphDef_Node::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.Node.device)
  
  return device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CostGraphDef_Node::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.Node.device)
}
inline std::string* CostGraphDef_Node::unsafe_arena_release_device() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.Node.device)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CostGraphDef_Node::unsafe_arena_set_allocated_device(
    std::string* device) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device != nullptr) {
    
  } else {
    
  }
  device_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.Node.device)
}

// int32 id = 3;
inline void CostGraphDef_Node::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CostGraphDef_Node::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.id)
  return id_;
}
inline void CostGraphDef_Node::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.id)
}

// repeated .tensorflow.CostGraphDef.Node.InputInfo input_info = 4;
inline int CostGraphDef_Node::input_info_size() const {
  return input_info_.size();
}
inline void CostGraphDef_Node::clear_input_info() {
  input_info_.Clear();
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::mutable_input_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.input_info)
  return input_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >*
CostGraphDef_Node::mutable_input_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.input_info)
  return &input_info_;
}
inline const ::tensorflow::CostGraphDef_Node_InputInfo& CostGraphDef_Node::input_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.input_info)
  return input_info_.Get(index);
}
inline ::tensorflow::CostGraphDef_Node_InputInfo* CostGraphDef_Node::add_input_info() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.input_info)
  return input_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_InputInfo >&
CostGraphDef_Node::input_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.input_info)
  return input_info_;
}

// repeated .tensorflow.CostGraphDef.Node.OutputInfo output_info = 5;
inline int CostGraphDef_Node::output_info_size() const {
  return output_info_.size();
}
inline void CostGraphDef_Node::clear_output_info() {
  output_info_.Clear();
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::mutable_output_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.Node.output_info)
  return output_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >*
CostGraphDef_Node::mutable_output_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.output_info)
  return &output_info_;
}
inline const ::tensorflow::CostGraphDef_Node_OutputInfo& CostGraphDef_Node::output_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.output_info)
  return output_info_.Get(index);
}
inline ::tensorflow::CostGraphDef_Node_OutputInfo* CostGraphDef_Node::add_output_info() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.output_info)
  return output_info_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node_OutputInfo >&
CostGraphDef_Node::output_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.output_info)
  return output_info_;
}

// int64 temporary_memory_size = 6;
inline void CostGraphDef_Node::clear_temporary_memory_size() {
  temporary_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::temporary_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.temporary_memory_size)
  return temporary_memory_size_;
}
inline void CostGraphDef_Node::set_temporary_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  temporary_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.temporary_memory_size)
}

// int64 persistent_memory_size = 12;
inline void CostGraphDef_Node::clear_persistent_memory_size() {
  persistent_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.persistent_memory_size)
  return persistent_memory_size_;
}
inline void CostGraphDef_Node::set_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.persistent_memory_size)
}

// int64 host_temp_memory_size = 10 [deprecated = true];
inline void CostGraphDef_Node::clear_host_temp_memory_size() {
  host_temp_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::host_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.host_temp_memory_size)
  return host_temp_memory_size_;
}
inline void CostGraphDef_Node::set_host_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  host_temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.host_temp_memory_size)
}

// int64 device_temp_memory_size = 11 [deprecated = true];
inline void CostGraphDef_Node::clear_device_temp_memory_size() {
  device_temp_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::device_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device_temp_memory_size)
  return device_temp_memory_size_;
}
inline void CostGraphDef_Node::set_device_temp_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_temp_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device_temp_memory_size)
}

// int64 device_persistent_memory_size = 16 [deprecated = true];
inline void CostGraphDef_Node::clear_device_persistent_memory_size() {
  device_persistent_memory_size_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::device_persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.device_persistent_memory_size)
  return device_persistent_memory_size_;
}
inline void CostGraphDef_Node::set_device_persistent_memory_size(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  device_persistent_memory_size_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.device_persistent_memory_size)
}

// int64 compute_cost = 9;
inline void CostGraphDef_Node::clear_compute_cost() {
  compute_cost_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::compute_cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.compute_cost)
  return compute_cost_;
}
inline void CostGraphDef_Node::set_compute_cost(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_cost_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.compute_cost)
}

// int64 compute_time = 14;
inline void CostGraphDef_Node::clear_compute_time() {
  compute_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::compute_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.compute_time)
  return compute_time_;
}
inline void CostGraphDef_Node::set_compute_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  compute_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.compute_time)
}

// int64 memory_time = 15;
inline void CostGraphDef_Node::clear_memory_time() {
  memory_time_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 CostGraphDef_Node::memory_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.memory_time)
  return memory_time_;
}
inline void CostGraphDef_Node::set_memory_time(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  memory_time_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.memory_time)
}

// bool is_final = 7;
inline void CostGraphDef_Node::clear_is_final() {
  is_final_ = false;
}
inline bool CostGraphDef_Node::is_final() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.is_final)
  return is_final_;
}
inline void CostGraphDef_Node::set_is_final(bool value) {
  
  is_final_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.is_final)
}

// repeated int32 control_input = 8;
inline int CostGraphDef_Node::control_input_size() const {
  return control_input_.size();
}
inline void CostGraphDef_Node::clear_control_input() {
  control_input_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 CostGraphDef_Node::control_input(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.control_input)
  return control_input_.Get(index);
}
inline void CostGraphDef_Node::set_control_input(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  control_input_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.control_input)
}
inline void CostGraphDef_Node::add_control_input(::PROTOBUF_NAMESPACE_ID::int32 value) {
  control_input_.Add(value);
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.Node.control_input)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
CostGraphDef_Node::control_input() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.Node.control_input)
  return control_input_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
CostGraphDef_Node::mutable_control_input() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.Node.control_input)
  return &control_input_;
}

// bool inaccurate = 17;
inline void CostGraphDef_Node::clear_inaccurate() {
  inaccurate_ = false;
}
inline bool CostGraphDef_Node::inaccurate() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.Node.inaccurate)
  return inaccurate_;
}
inline void CostGraphDef_Node::set_inaccurate(bool value) {
  
  inaccurate_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.Node.inaccurate)
}

// -------------------------------------------------------------------

// CostGraphDef_AggregatedCost

// float cost = 1;
inline void CostGraphDef_AggregatedCost::clear_cost() {
  cost_ = 0;
}
inline float CostGraphDef_AggregatedCost::cost() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.AggregatedCost.cost)
  return cost_;
}
inline void CostGraphDef_AggregatedCost::set_cost(float value) {
  
  cost_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.AggregatedCost.cost)
}

// string dimension = 2;
inline void CostGraphDef_AggregatedCost::clear_dimension() {
  dimension_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& CostGraphDef_AggregatedCost::dimension() const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.AggregatedCost.dimension)
  return dimension_.Get();
}
inline void CostGraphDef_AggregatedCost::set_dimension(const std::string& value) {
  
  dimension_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.CostGraphDef.AggregatedCost.dimension)
}
inline void CostGraphDef_AggregatedCost::set_dimension(std::string&& value) {
  
  dimension_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.CostGraphDef.AggregatedCost.dimension)
}
inline void CostGraphDef_AggregatedCost::set_dimension(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  dimension_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.CostGraphDef.AggregatedCost.dimension)
}
inline void CostGraphDef_AggregatedCost::set_dimension(const char* value,
    size_t size) {
  
  dimension_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CostGraphDef.AggregatedCost.dimension)
}
inline std::string* CostGraphDef_AggregatedCost::mutable_dimension() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.AggregatedCost.dimension)
  return dimension_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* CostGraphDef_AggregatedCost::release_dimension() {
  // @@protoc_insertion_point(field_release:tensorflow.CostGraphDef.AggregatedCost.dimension)
  
  return dimension_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void CostGraphDef_AggregatedCost::set_allocated_dimension(std::string* dimension) {
  if (dimension != nullptr) {
    
  } else {
    
  }
  dimension_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), dimension,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CostGraphDef.AggregatedCost.dimension)
}
inline std::string* CostGraphDef_AggregatedCost::unsafe_arena_release_dimension() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CostGraphDef.AggregatedCost.dimension)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return dimension_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void CostGraphDef_AggregatedCost::unsafe_arena_set_allocated_dimension(
    std::string* dimension) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (dimension != nullptr) {
    
  } else {
    
  }
  dimension_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      dimension, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CostGraphDef.AggregatedCost.dimension)
}

// -------------------------------------------------------------------

// CostGraphDef

// repeated .tensorflow.CostGraphDef.Node node = 1;
inline int CostGraphDef::node_size() const {
  return node_.size();
}
inline void CostGraphDef::clear_node() {
  node_.Clear();
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::mutable_node(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.node)
  return node_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >*
CostGraphDef::mutable_node() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.node)
  return &node_;
}
inline const ::tensorflow::CostGraphDef_Node& CostGraphDef::node(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.node)
  return node_.Get(index);
}
inline ::tensorflow::CostGraphDef_Node* CostGraphDef::add_node() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.node)
  return node_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_Node >&
CostGraphDef::node() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.node)
  return node_;
}

// repeated .tensorflow.CostGraphDef.AggregatedCost cost = 2;
inline int CostGraphDef::cost_size() const {
  return cost_.size();
}
inline void CostGraphDef::clear_cost() {
  cost_.Clear();
}
inline ::tensorflow::CostGraphDef_AggregatedCost* CostGraphDef::mutable_cost(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CostGraphDef.cost)
  return cost_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >*
CostGraphDef::mutable_cost() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CostGraphDef.cost)
  return &cost_;
}
inline const ::tensorflow::CostGraphDef_AggregatedCost& CostGraphDef::cost(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CostGraphDef.cost)
  return cost_.Get(index);
}
inline ::tensorflow::CostGraphDef_AggregatedCost* CostGraphDef::add_cost() {
  // @@protoc_insertion_point(field_add:tensorflow.CostGraphDef.cost)
  return cost_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::CostGraphDef_AggregatedCost >&
CostGraphDef::cost() const {
  // @@protoc_insertion_point(field_list:tensorflow.CostGraphDef.cost)
  return cost_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fcost_5fgraph_2eproto
