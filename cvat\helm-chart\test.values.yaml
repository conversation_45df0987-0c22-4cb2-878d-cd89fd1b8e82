cvat:
  backend:
    defaultStorage:
      accessModes:
      - ReadWriteOnce
    server:
      additionalVolumeMounts:
      - mountPath: /home/<USER>/share
        name: cvat-backend-data
        subPath: share
      additionalEnv:
        - name: DJANGO_SETTINGS_MODULE
          value: cvat.settings.testing_rest
    worker:
      import:
        replicas: 1
        additionalVolumeMounts:
        - mountPath: /home/<USER>/share
          name: cvat-backend-data
          subPath: share
      export:
        replicas: 1
      chunks:
        replicas: 1
        additionalVolumeMounts:
        - mountPath: /home/<USER>/share
          name: cvat-backend-data
          subPath: share
    utils:
      additionalEnv:
        - name: DJANGO_SETTINGS_MODULE
          value: cvat.settings.testing_rest
    annotation:
      replicas: 0
    # Images are already present in the node
    imagePullPolicy: Never
  frontend:
    imagePullPolicy: Never

redis:
  master:
    # The "flushall" command, which we use in tests, is disabled in helm by default
    # https://artifacthub.io/packages/helm/bitnami/redis#redis-master-configuration-parameters
    disableCommands: []

keydb:
  resources:
    requests:

traefik:
  logs:
    general:
      level: DEBUG
    access:
      enabled: true

