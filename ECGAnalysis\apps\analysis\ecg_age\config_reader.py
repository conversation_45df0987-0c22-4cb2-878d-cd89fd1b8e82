import os
import json
import pandas as pd
from typing import Dict, Optional

class PersonalizedConfig:
    def __init__(self, config_path: str):
        """
        初始化配置读取器
        :param config_path: 配置文件路径（csv文件）
        """
        self.config_path = config_path
        self.config_data = self._load_config()
    
    def _load_config(self) -> Dict:
        """
        加载配置文件
        配置文件格式（CSV）：
        姓名,手机号码1,手机号码2,union_id,ecg_analysis,gender
        """
        if not os.path.exists(self.config_path):
            return {}
            
        try:
            df = pd.read_csv(self.config_path)
            df = df.dropna(subset=['union_id', 'ecg_analysis', 'gender'])
            
            # 构建配置字典
            config_dict = {}
            for _, row in df.iterrows():
                try:
                    # 从ecg_analysis字段解析JSON数据
                    ecg_analysis = json.loads(row['ecg_analysis'])
                    age = ecg_analysis.get('ECGAge', 0)  # 如果没有ECGAge，默认为0
                    
                    config_dict[row['union_id']] = {
                        'real_age': int(age),
                        'gender': int(row['gender']),  # 1为男性，2为女性
                        'alpha': 0.7,  # 默认阈值
                        'beta': 0.3    # 默认阈值
                    }
                except (json.JSONDecodeError, KeyError, ValueError) as e:
                    print(f"处理行数据时出错: {str(e)}")
                    continue
                    
            return config_dict
            
        except Exception as e:
            print(f"读取配置文件失败: {str(e)}")
            return {}
    
    def get_user_config(self, union_id: str) -> Optional[Dict]:
        """
        获取用户配置
        :param union_id: 用户ID
        :return: 用户配置或None
        """
        return self.config_data.get(union_id)
    
    def reload_config(self):
        """
        重新加载配置文件
        """
        self.config_data = self._load_config()


