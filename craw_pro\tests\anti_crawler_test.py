#!/usr/bin/env python3
"""
中羽在线反爬机制分析和突破测试脚本
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import time
import random
from urllib.parse import urljoin
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AntiCrawlerTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.badmintoncn.com"
        self.setup_session()
        
    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }
        self.session.headers.update(headers)
        
    def analyze_verification_page(self, url):
        """分析验证页面的详细结构"""
        print(f"\n=== 分析验证页面: {url} ===")
        
        response = self.session.get(url, timeout=15)
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"Content-Length: {len(response.text)}")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 分析页面标题
        title = soup.find('title')
        print(f"页面标题: {title.text if title else '无标题'}")
        
        # 分析表单结构
        forms = soup.find_all('form')
        print(f"表单数量: {len(forms)}")
        
        for i, form in enumerate(forms):
            print(f"\n表单 {i+1}:")
            print(f"  Action: {form.get('action', 'N/A')}")
            print(f"  Method: {form.get('method', 'GET')}")
            
            # 分析输入字段
            inputs = form.find_all('input')
            print(f"  输入字段数量: {len(inputs)}")
            for inp in inputs:
                print(f"    类型: {inp.get('type', 'text')}, 名称: {inp.get('name', 'N/A')}, 值: {inp.get('value', 'N/A')}")
        
        # 查找验证问题
        page_text = soup.get_text()
        
        # 各种验证问题模式
        patterns = {
            '数学题': [
                r'(\d+\s*[+×*x-]\s*\d+)\s*=\s*？',
                r'(\d+[+×*x-]\d+)=？',
                r'请计算\s*(\d+\s*[+×*x-]\s*\d+)',
            ],
            '常识题': [
                r'羽毛球有几根毛',
                r'ZYZX.*?怎么写',
                r'中羽.*?缩写'
            ]
        }
        
        print(f"\n验证问题分析:")
        for category, pattern_list in patterns.items():
            print(f"  {category}:")
            for pattern in pattern_list:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                if matches:
                    print(f"    找到: {matches}")
        
        # 保存完整页面文本用于分析
        lines = page_text.split('\n')
        relevant_lines = [line.strip() for line in lines if line.strip() and 
                         any(keyword in line for keyword in ['=', '？', '计算', '验证', '羽毛球', 'ZYZX', '中羽'])]
        
        print(f"\n相关文本行:")
        for line in relevant_lines[:10]:  # 只显示前10行
            print(f"  {line}")
            
        return soup, page_text
    
    def extract_verification_question(self, page_text):
        """提取验证问题"""
        # 精确的问题提取
        patterns = [
            r'(\d+\s*\+\s*\d+)\s*=\s*？',  # 加法
            r'(\d+\s*[×*x]\s*\d+)\s*=\s*？',  # 乘法
            r'(\d+\s*-\s*\d+)\s*=\s*？',  # 减法
            r'羽毛球有几根毛[？?]?',  # 羽毛球常识
            r'ZYZX.*?怎么写',  # ZYZX问题
            r'中羽.*?缩写'  # 中羽缩写
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                return matches[0]
        
        # 如果没找到，尝试更宽泛的搜索
        lines = page_text.split('\n')
        for line in lines:
            line = line.strip()
            if '=' in line and '？' in line:
                return line
            elif any(keyword in line for keyword in ['羽毛球有几根毛', 'ZYZX', '中羽']):
                return line
                
        return None
    
    def solve_question(self, question):
        """解答验证问题"""
        if not question:
            return None
            
        # 数学计算
        math_pattern = r'(\d+)\s*([+×*x-])\s*(\d+)'
        match = re.search(math_pattern, question)
        if match:
            num1, operator, num2 = match.groups()
            num1, num2 = int(num1), int(num2)
            
            if operator == '+':
                return str(num1 + num2)
            elif operator in ['×', '*', 'x']:
                return str(num1 * num2)
            elif operator == '-':
                return str(num1 - num2)
        
        # 常识问题
        if '羽毛球有几根毛' in question:
            return "16"
        elif 'ZYZX' in question and '怎么写' in question:
            return "zyzx"
        elif '中羽' in question and '缩写' in question:
            return "zyzx"
            
        return None
    
    def test_verification_process(self, url):
        """测试完整的验证流程"""
        print(f"\n=== 测试验证流程: {url} ===")
        
        # 第一步：获取验证页面
        soup, page_text = self.analyze_verification_page(url)
        
        # 第二步：提取验证问题
        question = self.extract_verification_question(page_text)
        print(f"提取的问题: {question}")
        
        if not question:
            print("❌ 无法提取验证问题")
            return False
        
        # 第三步：解答问题
        answer = self.solve_question(question)
        print(f"计算的答案: {answer}")
        
        if not answer:
            print("❌ 无法计算答案")
            return False
        
        # 第四步：查找表单并提交
        form = soup.find('form')
        if not form:
            print("❌ 未找到表单")
            return False
        
        # 构建表单数据
        form_data = {}
        
        # 获取所有隐藏字段
        for hidden_input in form.find_all('input', {'type': 'hidden'}):
            name = hidden_input.get('name')
            value = hidden_input.get('value', '')
            if name:
                form_data[name] = value
        
        # 查找答案输入字段
        input_field = form.find('input', {'type': 'text'})
        if not input_field:
            # 尝试查找其他类型的输入字段
            for inp in form.find_all('input'):
                if inp.get('type') not in ['hidden', 'submit', 'button']:
                    input_field = inp
                    break
        
        if not input_field:
            print("❌ 未找到答案输入字段")
            return False
        
        input_name = input_field.get('name') or input_field.get('id')
        if input_name:
            form_data[input_name] = answer
        
        # 获取提交URL
        form_action = form.get('action', '')
        if form_action:
            if form_action.startswith('http'):
                submit_url = form_action
            else:
                submit_url = urljoin(url, form_action)
        else:
            submit_url = url
        
        print(f"提交URL: {submit_url}")
        print(f"表单数据: {form_data}")
        
        # 第五步：提交表单
        print("\n提交验证...")
        response = self.session.post(submit_url, data=form_data, timeout=15)
        print(f"提交状态码: {response.status_code}")
        
        # 第六步：检查结果
        if response.status_code == 200:
            result_soup = BeautifulSoup(response.text, 'html.parser')
            result_text = result_soup.get_text()
            
            # 检查是否还有验证问题
            has_verification = any(pattern in result_text for pattern in ['验证', '计算', '=？', '羽毛球有几根毛'])
            
            if not has_verification:
                print("✅ 验证成功！")
                return True
            else:
                print("⚠️ 验证后仍有验证问题")
                # 保存结果页面用于分析
                with open('verification_result.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("验证结果页面已保存到 verification_result.html")
        
        print("❌ 验证失败")
        return False
    
    def test_different_approaches(self):
        """测试不同的访问方法"""
        test_urls = [
            f"{self.base_url}/cbo_eq/view.php?eid=22974",
            f"{self.base_url}/cbo_eq/list.php",
            f"{self.base_url}/cbo_eq/",
        ]
        
        for url in test_urls:
            print(f"\n{'='*60}")
            success = self.test_verification_process(url)
            if success:
                print(f"✅ 成功访问: {url}")
                # 尝试获取实际内容
                time.sleep(2)
                final_response = self.session.get(url)
                final_soup = BeautifulSoup(final_response.text, 'html.parser')
                final_title = final_soup.find('title')
                print(f"最终页面标题: {final_title.text if final_title else '无标题'}")
                
                return True
            else:
                print(f"❌ 访问失败: {url}")
                
            time.sleep(3)  # 避免过于频繁的请求
        
        return False

def main():
    tester = AntiCrawlerTester()
    
    print("🧪 开始反爬机制分析和测试...")
    
    success = tester.test_different_approaches()
    
    if success:
        print("\n🎉 找到可行的访问方法！")
    else:
        print("\n❌ 所有测试方法都失败了")
        print("建议:")
        print("1. 网站可能需要更复杂的会话管理")
        print("2. 可能需要模拟更真实的浏览器行为")
        print("3. 可能存在IP限制或其他安全措施")

if __name__ == "__main__":
    main() 