
class I{{classname}}(IModelData):
    """
    NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

{{#vars}}
    {{#-first}}
    # member type declarations
    {{/-first}}
    {{name}}: {{> model_templates/type_annotation_cleaned }} # noqa: E501
    """{{^required}}
    [optional{{#defaultValue}}, default: {{{.}}}{{/defaultValue}}]{{/required}}{{#isContainer}}
    {{{dataType}}}{{/isContainer}}{{#description}}
    {{{.}}}.{{/description}}
    """

{{/vars}}

class {{classname}}(ModelNormal, I{{classname}}):
    """
    NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.

    Attributes:
{{#requiredVars}}
      {{name}} ({{{dataType}}}):{{#description}} {{{.}}}{{/description}}

{{/requiredVars}}
{{#optionalVars}}
      {{name}} ({{{dataType}}}):{{#description}} {{{.}}}.{{/description}} [optional]{{#defaultValue}} if omitted the server will use the default value of {{{.}}}{{/defaultValue}}  # noqa: E501

{{/optionalVars}}

{{> model_templates/docstring_allowed }}
      attribute_map (dict): The key is attribute name
          and the value is json key in definition.
      discriminator_value_class_map (dict): A dict to go from the discriminator
          variable value to the discriminator class name.
{{> model_templates/docstring_openapi_validations }}

    """

{{> model_templates/classvars }}

    attribute_map = {
{{#requiredVars}}
        '{{name}}': '{{baseName}}',  # noqa: E501
{{/requiredVars}}
{{#optionalVars}}
        '{{name}}': '{{baseName}}',  # noqa: E501
{{/optionalVars}}
    }

    read_only_vars = {
{{#requiredVars}}
{{#isReadOnly}}
        '{{name}}',  # noqa: E501
{{/isReadOnly}}
{{/requiredVars}}
{{#optionalVars}}
{{#isReadOnly}}
        '{{name}}',  # noqa: E501
{{/isReadOnly}}
{{/optionalVars}}
    }

    _composed_schemas = {}

{{> model_templates/method_from_openapi_data_normal}}

{{> model_templates/method_init_normal}}
