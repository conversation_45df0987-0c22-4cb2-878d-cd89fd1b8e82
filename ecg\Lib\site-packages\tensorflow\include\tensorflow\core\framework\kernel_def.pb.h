// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/kernel_def.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3009000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3009002 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/inlined_string_field.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxillaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
namespace tensorflow {
class KernelDef;
class KernelDefDefaultTypeInternal;
extern KernelDefDefaultTypeInternal _KernelDef_default_instance_;
class KernelDef_AttrConstraint;
class KernelDef_AttrConstraintDefaultTypeInternal;
extern KernelDef_AttrConstraintDefaultTypeInternal _KernelDef_AttrConstraint_default_instance_;
class KernelList;
class KernelListDefaultTypeInternal;
extern KernelListDefaultTypeInternal _KernelList_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::KernelDef* Arena::CreateMaybeMessage<::tensorflow::KernelDef>(Arena*);
template<> ::tensorflow::KernelDef_AttrConstraint* Arena::CreateMaybeMessage<::tensorflow::KernelDef_AttrConstraint>(Arena*);
template<> ::tensorflow::KernelList* Arena::CreateMaybeMessage<::tensorflow::KernelList>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class KernelDef_AttrConstraint :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KernelDef.AttrConstraint) */ {
 public:
  KernelDef_AttrConstraint();
  virtual ~KernelDef_AttrConstraint();

  KernelDef_AttrConstraint(const KernelDef_AttrConstraint& from);
  KernelDef_AttrConstraint(KernelDef_AttrConstraint&& from) noexcept
    : KernelDef_AttrConstraint() {
    *this = ::std::move(from);
  }

  inline KernelDef_AttrConstraint& operator=(const KernelDef_AttrConstraint& from) {
    CopyFrom(from);
    return *this;
  }
  inline KernelDef_AttrConstraint& operator=(KernelDef_AttrConstraint&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const KernelDef_AttrConstraint& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KernelDef_AttrConstraint* internal_default_instance() {
    return reinterpret_cast<const KernelDef_AttrConstraint*>(
               &_KernelDef_AttrConstraint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(KernelDef_AttrConstraint& a, KernelDef_AttrConstraint& b) {
    a.Swap(&b);
  }
  inline void Swap(KernelDef_AttrConstraint* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KernelDef_AttrConstraint* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline KernelDef_AttrConstraint* New() const final {
    return CreateMaybeMessage<KernelDef_AttrConstraint>(nullptr);
  }

  KernelDef_AttrConstraint* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<KernelDef_AttrConstraint>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const KernelDef_AttrConstraint& from);
  void MergeFrom(const KernelDef_AttrConstraint& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KernelDef_AttrConstraint* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KernelDef.AttrConstraint";
  }
  protected:
  explicit KernelDef_AttrConstraint(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kAllowedValuesFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  void set_name(const std::string& value);
  void set_name(std::string&& value);
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  std::string* mutable_name();
  std::string* release_name();
  void set_allocated_name(std::string* name);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_name();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_name(
      std::string* name);

  // .tensorflow.AttrValue allowed_values = 2;
  bool has_allowed_values() const;
  void clear_allowed_values();
  const ::tensorflow::AttrValue& allowed_values() const;
  ::tensorflow::AttrValue* release_allowed_values();
  ::tensorflow::AttrValue* mutable_allowed_values();
  void set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values);
  void unsafe_arena_set_allocated_allowed_values(
      ::tensorflow::AttrValue* allowed_values);
  ::tensorflow::AttrValue* unsafe_arena_release_allowed_values();

  // @@protoc_insertion_point(class_scope:tensorflow.KernelDef.AttrConstraint)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::tensorflow::AttrValue* allowed_values_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
};
// -------------------------------------------------------------------

class KernelDef :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KernelDef) */ {
 public:
  KernelDef();
  virtual ~KernelDef();

  KernelDef(const KernelDef& from);
  KernelDef(KernelDef&& from) noexcept
    : KernelDef() {
    *this = ::std::move(from);
  }

  inline KernelDef& operator=(const KernelDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline KernelDef& operator=(KernelDef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const KernelDef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KernelDef* internal_default_instance() {
    return reinterpret_cast<const KernelDef*>(
               &_KernelDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(KernelDef& a, KernelDef& b) {
    a.Swap(&b);
  }
  inline void Swap(KernelDef* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KernelDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline KernelDef* New() const final {
    return CreateMaybeMessage<KernelDef>(nullptr);
  }

  KernelDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<KernelDef>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const KernelDef& from);
  void MergeFrom(const KernelDef& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KernelDef* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KernelDef";
  }
  protected:
  explicit KernelDef(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef KernelDef_AttrConstraint AttrConstraint;

  // accessors -------------------------------------------------------

  enum : int {
    kConstraintFieldNumber = 3,
    kHostMemoryArgFieldNumber = 4,
    kOpFieldNumber = 1,
    kDeviceTypeFieldNumber = 2,
    kLabelFieldNumber = 5,
    kPriorityFieldNumber = 6,
  };
  // repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
  int constraint_size() const;
  void clear_constraint();
  ::tensorflow::KernelDef_AttrConstraint* mutable_constraint(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >*
      mutable_constraint();
  const ::tensorflow::KernelDef_AttrConstraint& constraint(int index) const;
  ::tensorflow::KernelDef_AttrConstraint* add_constraint();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >&
      constraint() const;

  // repeated string host_memory_arg = 4;
  int host_memory_arg_size() const;
  void clear_host_memory_arg();
  const std::string& host_memory_arg(int index) const;
  std::string* mutable_host_memory_arg(int index);
  void set_host_memory_arg(int index, const std::string& value);
  void set_host_memory_arg(int index, std::string&& value);
  void set_host_memory_arg(int index, const char* value);
  void set_host_memory_arg(int index, const char* value, size_t size);
  std::string* add_host_memory_arg();
  void add_host_memory_arg(const std::string& value);
  void add_host_memory_arg(std::string&& value);
  void add_host_memory_arg(const char* value);
  void add_host_memory_arg(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& host_memory_arg() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_host_memory_arg();

  // string op = 1;
  void clear_op();
  const std::string& op() const;
  void set_op(const std::string& value);
  void set_op(std::string&& value);
  void set_op(const char* value);
  void set_op(const char* value, size_t size);
  std::string* mutable_op();
  std::string* release_op();
  void set_allocated_op(std::string* op);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_op();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_op(
      std::string* op);

  // string device_type = 2;
  void clear_device_type();
  const std::string& device_type() const;
  void set_device_type(const std::string& value);
  void set_device_type(std::string&& value);
  void set_device_type(const char* value);
  void set_device_type(const char* value, size_t size);
  std::string* mutable_device_type();
  std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_device_type();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_device_type(
      std::string* device_type);

  // string label = 5;
  void clear_label();
  const std::string& label() const;
  void set_label(const std::string& value);
  void set_label(std::string&& value);
  void set_label(const char* value);
  void set_label(const char* value, size_t size);
  std::string* mutable_label();
  std::string* release_label();
  void set_allocated_label(std::string* label);
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  std::string* unsafe_arena_release_label();
  GOOGLE_PROTOBUF_RUNTIME_DEPRECATED("The unsafe_arena_ accessors for"
  "    string fields are deprecated and will be removed in a"
  "    future release.")
  void unsafe_arena_set_allocated_label(
      std::string* label);

  // int32 priority = 6;
  void clear_priority();
  ::PROTOBUF_NAMESPACE_ID::int32 priority() const;
  void set_priority(::PROTOBUF_NAMESPACE_ID::int32 value);

  // @@protoc_insertion_point(class_scope:tensorflow.KernelDef)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint > constraint_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> host_memory_arg_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr label_;
  ::PROTOBUF_NAMESPACE_ID::int32 priority_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
};
// -------------------------------------------------------------------

class KernelList :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KernelList) */ {
 public:
  KernelList();
  virtual ~KernelList();

  KernelList(const KernelList& from);
  KernelList(KernelList&& from) noexcept
    : KernelList() {
    *this = ::std::move(from);
  }

  inline KernelList& operator=(const KernelList& from) {
    CopyFrom(from);
    return *this;
  }
  inline KernelList& operator=(KernelList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArena() const final {
    return GetArenaNoVirtual();
  }
  inline void* GetMaybeArenaPointer() const final {
    return MaybeArenaPtr();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const KernelList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const KernelList* internal_default_instance() {
    return reinterpret_cast<const KernelList*>(
               &_KernelList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(KernelList& a, KernelList& b) {
    a.Swap(&b);
  }
  inline void Swap(KernelList* other) {
    if (other == this) return;
    if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KernelList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline KernelList* New() const final {
    return CreateMaybeMessage<KernelList>(nullptr);
  }

  KernelList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<KernelList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const KernelList& from);
  void MergeFrom(const KernelList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  #if GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  #else
  bool MergePartialFromCodedStream(
      ::PROTOBUF_NAMESPACE_ID::io::CodedInputStream* input) final;
  #endif  // GOOGLE_PROTOBUF_ENABLE_EXPERIMENTAL_PARSER
  void SerializeWithCachedSizes(
      ::PROTOBUF_NAMESPACE_ID::io::CodedOutputStream* output) const final;
  ::PROTOBUF_NAMESPACE_ID::uint8* InternalSerializeWithCachedSizesToArray(
      ::PROTOBUF_NAMESPACE_ID::uint8* target) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KernelList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KernelList";
  }
  protected:
  explicit KernelList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  inline ::PROTOBUF_NAMESPACE_ID::Arena* GetArenaNoVirtual() const {
    return _internal_metadata_.arena();
  }
  inline void* MaybeArenaPtr() const {
    return _internal_metadata_.raw_arena_ptr();
  }
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto);
    return ::descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelFieldNumber = 1,
  };
  // repeated .tensorflow.KernelDef kernel = 1;
  int kernel_size() const;
  void clear_kernel();
  ::tensorflow::KernelDef* mutable_kernel(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >*
      mutable_kernel();
  const ::tensorflow::KernelDef& kernel(int index) const;
  ::tensorflow::KernelDef* add_kernel();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >&
      kernel() const;

  // @@protoc_insertion_point(class_scope:tensorflow.KernelList)
 private:
  class _Internal;

  ::PROTOBUF_NAMESPACE_ID::internal::InternalMetadataWithArena _internal_metadata_;
  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef > kernel_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// KernelDef_AttrConstraint

// string name = 1;
inline void KernelDef_AttrConstraint::clear_name() {
  name_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& KernelDef_AttrConstraint::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.AttrConstraint.name)
  return name_.Get();
}
inline void KernelDef_AttrConstraint::set_name(const std::string& value) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.AttrConstraint.name)
}
inline void KernelDef_AttrConstraint::set_name(std::string&& value) {
  
  name_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.KernelDef.AttrConstraint.name)
}
inline void KernelDef_AttrConstraint::set_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.KernelDef.AttrConstraint.name)
}
inline void KernelDef_AttrConstraint::set_name(const char* value,
    size_t size) {
  
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.KernelDef.AttrConstraint.name)
}
inline std::string* KernelDef_AttrConstraint::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.AttrConstraint.name)
  return name_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* KernelDef_AttrConstraint::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.AttrConstraint.name)
  
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void KernelDef_AttrConstraint::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.AttrConstraint.name)
}
inline std::string* KernelDef_AttrConstraint::unsafe_arena_release_name() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.KernelDef.AttrConstraint.name)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return name_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void KernelDef_AttrConstraint::unsafe_arena_set_allocated_name(
    std::string* name) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (name != nullptr) {
    
  } else {
    
  }
  name_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      name, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.KernelDef.AttrConstraint.name)
}

// .tensorflow.AttrValue allowed_values = 2;
inline bool KernelDef_AttrConstraint::has_allowed_values() const {
  return this != internal_default_instance() && allowed_values_ != nullptr;
}
inline const ::tensorflow::AttrValue& KernelDef_AttrConstraint::allowed_values() const {
  const ::tensorflow::AttrValue* p = allowed_values_;
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.AttrConstraint.allowed_values)
  return p != nullptr ? *p : *reinterpret_cast<const ::tensorflow::AttrValue*>(
      &::tensorflow::_AttrValue_default_instance_);
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::release_allowed_values() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.AttrConstraint.allowed_values)
  
  ::tensorflow::AttrValue* temp = allowed_values_;
  if (GetArenaNoVirtual() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  allowed_values_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::unsafe_arena_release_allowed_values() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.KernelDef.AttrConstraint.allowed_values)
  
  ::tensorflow::AttrValue* temp = allowed_values_;
  allowed_values_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::mutable_allowed_values() {
  
  if (allowed_values_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaNoVirtual());
    allowed_values_ = p;
  }
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.AttrConstraint.allowed_values)
  return allowed_values_;
}
inline void KernelDef_AttrConstraint::set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(allowed_values_);
  }
  if (allowed_values) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(allowed_values)->GetArena();
    if (message_arena != submessage_arena) {
      allowed_values = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, allowed_values, submessage_arena);
    }
    
  } else {
    
  }
  allowed_values_ = allowed_values;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.AttrConstraint.allowed_values)
}

// -------------------------------------------------------------------

// KernelDef

// string op = 1;
inline void KernelDef::clear_op() {
  op_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& KernelDef::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.op)
  return op_.Get();
}
inline void KernelDef::set_op(const std::string& value) {
  
  op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.op)
}
inline void KernelDef::set_op(std::string&& value) {
  
  op_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.KernelDef.op)
}
inline void KernelDef::set_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.KernelDef.op)
}
inline void KernelDef::set_op(const char* value,
    size_t size) {
  
  op_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.KernelDef.op)
}
inline std::string* KernelDef::mutable_op() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.op)
  return op_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* KernelDef::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.op)
  
  return op_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void KernelDef::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  op_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), op,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.op)
}
inline std::string* KernelDef::unsafe_arena_release_op() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.KernelDef.op)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return op_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void KernelDef::unsafe_arena_set_allocated_op(
    std::string* op) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (op != nullptr) {
    
  } else {
    
  }
  op_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      op, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.KernelDef.op)
}

// string device_type = 2;
inline void KernelDef::clear_device_type() {
  device_type_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& KernelDef::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.device_type)
  return device_type_.Get();
}
inline void KernelDef::set_device_type(const std::string& value) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.device_type)
}
inline void KernelDef::set_device_type(std::string&& value) {
  
  device_type_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.KernelDef.device_type)
}
inline void KernelDef::set_device_type(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.KernelDef.device_type)
}
inline void KernelDef::set_device_type(const char* value,
    size_t size) {
  
  device_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.KernelDef.device_type)
}
inline std::string* KernelDef::mutable_device_type() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.device_type)
  return device_type_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* KernelDef::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.device_type)
  
  return device_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void KernelDef::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device_type,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.device_type)
}
inline std::string* KernelDef::unsafe_arena_release_device_type() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.KernelDef.device_type)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return device_type_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void KernelDef::unsafe_arena_set_allocated_device_type(
    std::string* device_type) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (device_type != nullptr) {
    
  } else {
    
  }
  device_type_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      device_type, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.KernelDef.device_type)
}

// repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
inline int KernelDef::constraint_size() const {
  return constraint_.size();
}
inline void KernelDef::clear_constraint() {
  constraint_.Clear();
}
inline ::tensorflow::KernelDef_AttrConstraint* KernelDef::mutable_constraint(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.constraint)
  return constraint_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >*
KernelDef::mutable_constraint() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.KernelDef.constraint)
  return &constraint_;
}
inline const ::tensorflow::KernelDef_AttrConstraint& KernelDef::constraint(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.constraint)
  return constraint_.Get(index);
}
inline ::tensorflow::KernelDef_AttrConstraint* KernelDef::add_constraint() {
  // @@protoc_insertion_point(field_add:tensorflow.KernelDef.constraint)
  return constraint_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >&
KernelDef::constraint() const {
  // @@protoc_insertion_point(field_list:tensorflow.KernelDef.constraint)
  return constraint_;
}

// repeated string host_memory_arg = 4;
inline int KernelDef::host_memory_arg_size() const {
  return host_memory_arg_.size();
}
inline void KernelDef::clear_host_memory_arg() {
  host_memory_arg_.Clear();
}
inline const std::string& KernelDef::host_memory_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.host_memory_arg)
  return host_memory_arg_.Get(index);
}
inline std::string* KernelDef::mutable_host_memory_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.host_memory_arg)
  return host_memory_arg_.Mutable(index);
}
inline void KernelDef::set_host_memory_arg(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.host_memory_arg)
  host_memory_arg_.Mutable(index)->assign(value);
}
inline void KernelDef::set_host_memory_arg(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.host_memory_arg)
  host_memory_arg_.Mutable(index)->assign(std::move(value));
}
inline void KernelDef::set_host_memory_arg(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  host_memory_arg_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::set_host_memory_arg(int index, const char* value, size_t size) {
  host_memory_arg_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.KernelDef.host_memory_arg)
}
inline std::string* KernelDef::add_host_memory_arg() {
  // @@protoc_insertion_point(field_add_mutable:tensorflow.KernelDef.host_memory_arg)
  return host_memory_arg_.Add();
}
inline void KernelDef::add_host_memory_arg(const std::string& value) {
  host_memory_arg_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::add_host_memory_arg(std::string&& value) {
  host_memory_arg_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::add_host_memory_arg(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  host_memory_arg_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::add_host_memory_arg(const char* value, size_t size) {
  host_memory_arg_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.KernelDef.host_memory_arg)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
KernelDef::host_memory_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.KernelDef.host_memory_arg)
  return host_memory_arg_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
KernelDef::mutable_host_memory_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.KernelDef.host_memory_arg)
  return &host_memory_arg_;
}

// string label = 5;
inline void KernelDef::clear_label() {
  label_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline const std::string& KernelDef::label() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.label)
  return label_.Get();
}
inline void KernelDef::set_label(const std::string& value) {
  
  label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.label)
}
inline void KernelDef::set_label(std::string&& value) {
  
  label_.Set(
    &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::move(value), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_rvalue:tensorflow.KernelDef.label)
}
inline void KernelDef::set_label(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:tensorflow.KernelDef.label)
}
inline void KernelDef::set_label(const char* value,
    size_t size) {
  
  label_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:tensorflow.KernelDef.label)
}
inline std::string* KernelDef::mutable_label() {
  
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.label)
  return label_.Mutable(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline std::string* KernelDef::release_label() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.label)
  
  return label_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
inline void KernelDef::set_allocated_label(std::string* label) {
  if (label != nullptr) {
    
  } else {
    
  }
  label_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), label,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.label)
}
inline std::string* KernelDef::unsafe_arena_release_label() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.KernelDef.label)
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  
  return label_.UnsafeArenaRelease(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
inline void KernelDef::unsafe_arena_set_allocated_label(
    std::string* label) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != nullptr);
  if (label != nullptr) {
    
  } else {
    
  }
  label_.UnsafeArenaSetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      label, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.KernelDef.label)
}

// int32 priority = 6;
inline void KernelDef::clear_priority() {
  priority_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 KernelDef::priority() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.priority)
  return priority_;
}
inline void KernelDef::set_priority(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  priority_ = value;
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.priority)
}

// -------------------------------------------------------------------

// KernelList

// repeated .tensorflow.KernelDef kernel = 1;
inline int KernelList::kernel_size() const {
  return kernel_.size();
}
inline void KernelList::clear_kernel() {
  kernel_.Clear();
}
inline ::tensorflow::KernelDef* KernelList::mutable_kernel(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelList.kernel)
  return kernel_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >*
KernelList::mutable_kernel() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.KernelList.kernel)
  return &kernel_;
}
inline const ::tensorflow::KernelDef& KernelList::kernel(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelList.kernel)
  return kernel_.Get(index);
}
inline ::tensorflow::KernelDef* KernelList::add_kernel() {
  // @@protoc_insertion_point(field_add:tensorflow.KernelList.kernel)
  return kernel_.Add();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >&
KernelList::kernel() const {
  // @@protoc_insertion_point(field_list:tensorflow.KernelList.kernel)
  return kernel_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
