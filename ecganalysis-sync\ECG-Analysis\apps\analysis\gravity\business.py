import json
import traceback
from datetime import datetime
from apps.analysis.gravity.state_analysis import process as state_analysis_process
from apps.models.analysis_models import MotionInfoEntity
from apps.models.ecg_analysis_modes import TGravityInfo
from apps.utils.logger_helper import Logger


def process(union_id, data):
    try:
        info_data = json.loads(data.replace('\\', ''))
        total_count = len(info_data)
        if total_count == 0:
            motion_proportion = 0
        else:
            motion_count = len([gravity for gravity in info_data if gravity.get('active') == 1])
            motion_proportion = round(motion_count / total_count, 2)

        status, features, tumble_history = state_analysis_process(info_data)  # 状态分析

        save_data(union_id, info_data, status, features, tumble_history)  # 保存数据

        motion_info = MotionInfoEntity()
        motion_intensity = 0  # 运动强度 0-静止 1-低 2-中 3-高

        if 0.2 < motion_proportion <= 0.5:
            motion_intensity = 1
        elif 0.5 < motion_proportion <= 0.8:
            motion_intensity = 2
        elif motion_proportion > 0.8:
            motion_intensity = 3

        motion_info.motion_intensity = motion_intensity  # 运动强度 0-静止 1-低 2-中 3-高
        motion_info.motion_proportion = motion_proportion  # 运动比例

        return motion_info
    except Exception as e:
        Logger().error(f'加速度处理异常：{traceback.format_exc()}\n{data}')
        return None


def save_data(union_id, data, status, features, tumble_history):
    """
    保存数据
    :param union_id:
    :param data:
    :param status:
    :return:
    """
    gravity_info = TGravityInfo()
    gravity_info.union_id = union_id
    gravity_info.gravity = data
    gravity_info.motion_state = status
    gravity_info.features = features.__str__()
    gravity_info.tumble_history = tumble_history.__str__()
    gravity_info.create_date = datetime.now()
    gravity_info.save()
