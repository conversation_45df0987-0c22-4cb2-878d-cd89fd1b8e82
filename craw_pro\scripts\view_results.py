#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv

def view_csv_data():
    """查看CSV文件中的数据内容"""
    filename = 'browser_crawl_20250617_115923.csv'
    
    with open(filename, 'r', encoding='utf-8-sig') as f:  # 使用utf-8-sig处理BOM
        reader = csv.DictReader(f)
        rows = list(reader)
        
        print(f"📊 CSV文件: {filename}")
        print(f"📈 总记录数: {len(rows)}")
        print(f"🔍 字段列表: {list(rows[0].keys()) if rows else '无数据'}")
        print("\n" + "="*60)
        
        # 查看前3条记录
        for i, row in enumerate(rows[:3]):
            print(f"\n=== 记录 {i+1}: {row.get('name', 'N/A')} ===")
            print(f"品牌: {row.get('brand', 'N/A')}")
            print(f"系列: {row.get('series', 'N/A')}")
            print(f"吊牌价: {row.get('msrp_price', 'N/A')}元")
            print(f"描述 (面包屑导航):")
            description = row.get('description', 'N/A')
            if description and description != 'N/A':
                for line in description.split('\n'):
                    if line.strip():
                        print(f"  {line.strip()}")
            else:
                print("  未提取到面包屑导航")
            
            # 显示技术参数
            tech_params = {}
            for key in ['shaft_material', 'frame_material', 'technology']:
                value = row.get(key, '')
                if value:
                    tech_params[key] = value
            
            if tech_params:
                print("技术参数:")
                for key, value in tech_params.items():
                    key_name = {
                        'shaft_material': '中管材质',
                        'frame_material': '拍框材质', 
                        'technology': '技术特点'
                    }.get(key, key)
                    print(f"  {key_name}: {value}")
            
            # 显示用户标签
            user_tags = row.get('user_tags', '')
            if user_tags:
                print(f"用户标签: {user_tags}")

if __name__ == "__main__":
    view_csv_data() 