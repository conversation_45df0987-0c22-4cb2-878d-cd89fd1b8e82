import gzip
import json
import traceback
import requests
import os
import datetime
import mysql.connector
from mysql.connector import Error

# 数据库连接配置
db_config = {
    'host': '**************',
    'port': 3308,
    'user': 'ai',
    'password': 'z8^#g4r4mz',
    'database': 'ecg_analysis_test'
}

# 正确的下载URL配置
DOWNLOAD_AIWEIHE_API = {
    'test': {
        'download_url': "http://download.test.ecggpt.aiweihe.com/download/"
    }
}

# 使用正确的下载URL
download_url = DOWNLOAD_AIWEIHE_API['test']['download_url']

def test_db_connection(config):
    """
    测试数据库连接。
    """
    connection = None
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            print(f"成功连接到数据库: {config['database']}@{config['host']}:{config['port']}")
            return True
        else:
            print("连接已建立，但状态为非连接状态。")
            return False
    except Error as e:
        print(f"连接失败: {e}")
        return False
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭。")

def query_log_by_id(config, log_id):
    """
    根据log_id查询日志记录
    """
    connection = None
    cursor = None
    result = None
    try:
        connection = mysql.connector.connect(**config)
        if connection.is_connected():
            cursor = connection.cursor()
            sql = f'SELECT * FROM `t_analysis_log` WHERE id = {log_id}'
            cursor.execute(sql)
            result = cursor.fetchone()
            if result:
                column_names = [desc[0] for desc in cursor.description]
                return dict(zip(column_names, result))
            else:
                return None
        else:
            print("无法建立数据库连接")
            return None
    except Error as e:
        print(f"查询数据时出错: {e}")
        return None
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def download_log(log_id):
    """
    下载卫和AI服务请求日志
    """
    log_record = query_log_by_id(db_config, log_id)
    
    if not log_record:
        print(f"未找到ID为 {log_id} 的日志记录")
        return None
    
    analysis_info_path = log_record.get('analysis_info_path')
    
    if not analysis_info_path:
        print(f"日志记录中没有路径信息")
        return None
    
    print(f"找到日志路径: {analysis_info_path}")
    
    # 使用正确的URL下载
    try:
        print(f"使用URL: {download_url}")
        response = requests.get(download_url + analysis_info_path, timeout=10)
        
        if response.status_code != 200:
            print(f"下载失败，HTTP状态码: {response.status_code}")
            return None
        
        try:
            # 尝试解压缩并解析JSON
            result = json.loads(gzip.decompress(response.content).decode())
            print("成功解析日志内容")
            return result
        except Exception as e:
            print(f"解析日志内容时出错: {e}")
            # 如果解压失败，记录是二进制内容
            if len(response.content) > 0:
                print(f"获取到 {len(response.content)} 字节的原始内容")
                return response.content
            return None
    except Exception as e:
        print(f"下载日志时出错: {e}")
        print(traceback.format_exc())
        return None

def save_log_content(log_id, content):
    """
    保存日志内容到文件
    """
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    
    if isinstance(content, dict) or isinstance(content, list):
        # 如果是JSON对象，保存为JSON文件
        filename = f'log_{log_id}_{timestamp}.json'
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(content, f, ensure_ascii=False, indent=2)
            print(f"JSON日志内容已保存到文件: {filename}")
            return filename
        except Exception as e:
            print(f"保存JSON内容时出错: {e}")
            return None
    else:
        # 如果是二进制内容，直接保存
        filename = f'log_{log_id}_{timestamp}.bin'
        try:
            with open(filename, 'wb') as f:
                f.write(content)
            print(f"二进制日志内容已保存到文件: {filename}")
            
            # 尝试另存为gz文件以便用户可以手动解压
            gz_filename = f'log_{log_id}_{timestamp}.gz'
            try:
                with open(gz_filename, 'wb') as f:
                    f.write(content)
                print(f"二进制日志内容也已保存为GZ文件: {gz_filename}")
            except Exception as e:
                print(f"保存GZ文件时出错: {e}")
                
            return filename
        except Exception as e:
            print(f"保存二进制内容时出错: {e}")
            return None

def save_log_info_to_csv(log_id, log_record):
    """
    将日志记录信息保存到CSV
    """
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    filename = f'log_info_{log_id}_{timestamp}.csv'
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("字段,值\n")
            for key, value in log_record.items():
                f.write(f"{key},{value}\n")
        print(f"日志记录信息已保存到: {filename}")
        return filename
    except Exception as e:
        print(f"保存CSV时出错: {e}")
        return None

if __name__ == "__main__":
    log_id = 694968  # 要下载的日志ID
    
    if test_db_connection(db_config):
        print(f"\n------ 开始查询日志 ID: {log_id} ------")
        log_record = query_log_by_id(db_config, log_id)
        
        if log_record:
            print("日志记录信息:")
            for key, value in log_record.items():
                print(f"{key}: {value}")
            
            # 保存日志记录信息
            save_log_info_to_csv(log_id, log_record)
            
            print(f"\n------ 开始下载日志内容 ------")
            log_content = download_log(log_id)
            
            if log_content:
                save_log_content(log_id, log_content)
            else:
                # 如果无法下载，提供必要信息以便用户手动下载
                print(f"\n无法自动下载日志内容，请参考以下信息手动下载:")
                print(f"日志ID: {log_id}")
                print(f"日志路径: {log_record.get('analysis_info_path')}")
                print(f"下载URL: {download_url}")
        else:
            print(f"未找到 id = {log_id} 的日志记录")
    else:
        print("数据库连接测试失败，无法执行查询。") 