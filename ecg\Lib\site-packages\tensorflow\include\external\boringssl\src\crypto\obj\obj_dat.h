/* Copyright (C) 1995-1997 <PERSON> (<EMAIL>)
 * All rights reserved.
 *
 * This package is an SSL implementation written
 * by <PERSON> (<EMAIL>).
 * The implementation was written so as to conform with Netscapes SSL.
 *
 * This library is free for commercial and non-commercial use as long as
 * the following conditions are aheared to.  The following conditions
 * apply to all code found in this distribution, be it the RC4, RSA,
 * lhash, DES, etc., code; not just the SSL code.  The SSL documentation
 * included with this distribution is covered by the same copyright terms
 * except that the holder is <PERSON> (<EMAIL>).
 *
 * Copyright remains <PERSON>'s, and as such any Copyright notices in
 * the code are not to be removed.
 * If this package is used in a product, <PERSON> should be given attribution
 * as the author of the parts of the library used.
 * This can be in the form of a textual message at program startup or
 * in documentation (online or textual) provided with the package.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. All advertising materials mentioning features or use of this software
 *    must display the following acknowledgement:
 *    "This product includes cryptographic software written by
 *     <PERSON> (<EMAIL>)"
 *    The word 'cryptographic' can be left out if the rouines from the library
 *    being used are not cryptographic related :-).
 * 4. If you include any Windows specific code (or a derivative thereof) from
 *    the apps directory (application code) you must include an acknowledgement:
 *    "This product includes software written by Tim Hudson (<EMAIL>)"
 *
 * THIS SOFTWARE IS PROVIDED BY ERIC YOUNG ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 * The licence and distribution terms for any publically available version or
 * derivative of this code cannot be changed.  i.e. this code cannot simply be
 * copied and put under another distribution licence
 * [including the GNU Public Licence.] */

/* This file is generated by crypto/obj/objects.go. */


#define NUM_NID 961

static const uint8_t kObjectData[] = {
    /* NID_rsadsi */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    /* NID_pkcs */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    /* NID_md2 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x02,
    /* NID_md5 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x05,
    /* NID_rc4 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x03,
    0x04,
    /* NID_rsaEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x01,
    /* NID_md2WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x02,
    /* NID_md5WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x04,
    /* NID_pbeWithMD2AndDES_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x01,
    /* NID_pbeWithMD5AndDES_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x03,
    /* NID_X500 */
    0x55,
    /* NID_X509 */
    0x55,
    0x04,
    /* NID_commonName */
    0x55,
    0x04,
    0x03,
    /* NID_countryName */
    0x55,
    0x04,
    0x06,
    /* NID_localityName */
    0x55,
    0x04,
    0x07,
    /* NID_stateOrProvinceName */
    0x55,
    0x04,
    0x08,
    /* NID_organizationName */
    0x55,
    0x04,
    0x0a,
    /* NID_organizationalUnitName */
    0x55,
    0x04,
    0x0b,
    /* NID_rsa */
    0x55,
    0x08,
    0x01,
    0x01,
    /* NID_pkcs7 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    /* NID_pkcs7_data */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    0x01,
    /* NID_pkcs7_signed */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    0x02,
    /* NID_pkcs7_enveloped */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    0x03,
    /* NID_pkcs7_signedAndEnveloped */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    0x04,
    /* NID_pkcs7_digest */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    0x05,
    /* NID_pkcs7_encrypted */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x07,
    0x06,
    /* NID_pkcs3 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x03,
    /* NID_dhKeyAgreement */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x03,
    0x01,
    /* NID_des_ecb */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x06,
    /* NID_des_cfb64 */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x09,
    /* NID_des_cbc */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x07,
    /* NID_des_ede_ecb */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x11,
    /* NID_idea_cbc */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x81,
    0x3c,
    0x07,
    0x01,
    0x01,
    0x02,
    /* NID_rc2_cbc */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x03,
    0x02,
    /* NID_sha */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x12,
    /* NID_shaWithRSAEncryption */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x0f,
    /* NID_des_ede3_cbc */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x03,
    0x07,
    /* NID_des_ofb64 */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x08,
    /* NID_pkcs9 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    /* NID_pkcs9_emailAddress */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x01,
    /* NID_pkcs9_unstructuredName */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x02,
    /* NID_pkcs9_contentType */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x03,
    /* NID_pkcs9_messageDigest */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x04,
    /* NID_pkcs9_signingTime */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x05,
    /* NID_pkcs9_countersignature */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x06,
    /* NID_pkcs9_challengePassword */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x07,
    /* NID_pkcs9_unstructuredAddress */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x08,
    /* NID_pkcs9_extCertAttributes */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x09,
    /* NID_netscape */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    /* NID_netscape_cert_extension */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    /* NID_netscape_data_type */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x02,
    /* NID_sha1 */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x1a,
    /* NID_sha1WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x05,
    /* NID_dsaWithSHA */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x0d,
    /* NID_dsa_2 */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x0c,
    /* NID_pbeWithSHA1AndRC2_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x0b,
    /* NID_id_pbkdf2 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x0c,
    /* NID_dsaWithSHA1_2 */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x1b,
    /* NID_netscape_cert_type */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x01,
    /* NID_netscape_base_url */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x02,
    /* NID_netscape_revocation_url */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x03,
    /* NID_netscape_ca_revocation_url */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x04,
    /* NID_netscape_renewal_url */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x07,
    /* NID_netscape_ca_policy_url */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x08,
    /* NID_netscape_ssl_server_name */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x0c,
    /* NID_netscape_comment */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x01,
    0x0d,
    /* NID_netscape_cert_sequence */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x02,
    0x05,
    /* NID_id_ce */
    0x55,
    0x1d,
    /* NID_subject_key_identifier */
    0x55,
    0x1d,
    0x0e,
    /* NID_key_usage */
    0x55,
    0x1d,
    0x0f,
    /* NID_private_key_usage_period */
    0x55,
    0x1d,
    0x10,
    /* NID_subject_alt_name */
    0x55,
    0x1d,
    0x11,
    /* NID_issuer_alt_name */
    0x55,
    0x1d,
    0x12,
    /* NID_basic_constraints */
    0x55,
    0x1d,
    0x13,
    /* NID_crl_number */
    0x55,
    0x1d,
    0x14,
    /* NID_certificate_policies */
    0x55,
    0x1d,
    0x20,
    /* NID_authority_key_identifier */
    0x55,
    0x1d,
    0x23,
    /* NID_bf_cbc */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x97,
    0x55,
    0x01,
    0x02,
    /* NID_mdc2 */
    0x55,
    0x08,
    0x03,
    0x65,
    /* NID_mdc2WithRSA */
    0x55,
    0x08,
    0x03,
    0x64,
    /* NID_givenName */
    0x55,
    0x04,
    0x2a,
    /* NID_surname */
    0x55,
    0x04,
    0x04,
    /* NID_initials */
    0x55,
    0x04,
    0x2b,
    /* NID_crl_distribution_points */
    0x55,
    0x1d,
    0x1f,
    /* NID_md5WithRSA */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x03,
    /* NID_serialNumber */
    0x55,
    0x04,
    0x05,
    /* NID_title */
    0x55,
    0x04,
    0x0c,
    /* NID_description */
    0x55,
    0x04,
    0x0d,
    /* NID_cast5_cbc */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf6,
    0x7d,
    0x07,
    0x42,
    0x0a,
    /* NID_pbeWithMD5AndCast5_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf6,
    0x7d,
    0x07,
    0x42,
    0x0c,
    /* NID_dsaWithSHA1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    0x04,
    0x03,
    /* NID_sha1WithRSA */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x1d,
    /* NID_dsa */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    0x04,
    0x01,
    /* NID_ripemd160 */
    0x2b,
    0x24,
    0x03,
    0x02,
    0x01,
    /* NID_ripemd160WithRSA */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x01,
    0x02,
    /* NID_rc5_cbc */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x03,
    0x08,
    /* NID_zlib_compression */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x08,
    /* NID_ext_key_usage */
    0x55,
    0x1d,
    0x25,
    /* NID_id_pkix */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    /* NID_id_kp */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    /* NID_server_auth */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x01,
    /* NID_client_auth */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x02,
    /* NID_code_sign */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x03,
    /* NID_email_protect */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x04,
    /* NID_time_stamp */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x08,
    /* NID_ms_code_ind */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x02,
    0x01,
    0x15,
    /* NID_ms_code_com */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x02,
    0x01,
    0x16,
    /* NID_ms_ctl_sign */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x0a,
    0x03,
    0x01,
    /* NID_ms_sgc */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x0a,
    0x03,
    0x03,
    /* NID_ms_efs */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x0a,
    0x03,
    0x04,
    /* NID_ns_sgc */
    0x60,
    0x86,
    0x48,
    0x01,
    0x86,
    0xf8,
    0x42,
    0x04,
    0x01,
    /* NID_delta_crl */
    0x55,
    0x1d,
    0x1b,
    /* NID_crl_reason */
    0x55,
    0x1d,
    0x15,
    /* NID_invalidity_date */
    0x55,
    0x1d,
    0x18,
    /* NID_sxnet */
    0x2b,
    0x65,
    0x01,
    0x04,
    0x01,
    /* NID_pbe_WithSHA1And128BitRC4 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x01,
    0x01,
    /* NID_pbe_WithSHA1And40BitRC4 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x01,
    0x02,
    /* NID_pbe_WithSHA1And3_Key_TripleDES_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x01,
    0x03,
    /* NID_pbe_WithSHA1And2_Key_TripleDES_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x01,
    0x04,
    /* NID_pbe_WithSHA1And128BitRC2_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x01,
    0x05,
    /* NID_pbe_WithSHA1And40BitRC2_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x01,
    0x06,
    /* NID_keyBag */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x0a,
    0x01,
    0x01,
    /* NID_pkcs8ShroudedKeyBag */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x0a,
    0x01,
    0x02,
    /* NID_certBag */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x0a,
    0x01,
    0x03,
    /* NID_crlBag */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x0a,
    0x01,
    0x04,
    /* NID_secretBag */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x0a,
    0x01,
    0x05,
    /* NID_safeContentsBag */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x0c,
    0x0a,
    0x01,
    0x06,
    /* NID_friendlyName */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x14,
    /* NID_localKeyID */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x15,
    /* NID_x509Certificate */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x16,
    0x01,
    /* NID_sdsiCertificate */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x16,
    0x02,
    /* NID_x509Crl */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x17,
    0x01,
    /* NID_pbes2 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x0d,
    /* NID_pbmac1 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x0e,
    /* NID_hmacWithSHA1 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x07,
    /* NID_id_qt_cps */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x02,
    0x01,
    /* NID_id_qt_unotice */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x02,
    0x02,
    /* NID_SMIMECapabilities */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x0f,
    /* NID_pbeWithMD2AndRC2_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x04,
    /* NID_pbeWithMD5AndRC2_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x06,
    /* NID_pbeWithSHA1AndDES_CBC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    0x0a,
    /* NID_ms_ext_req */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x02,
    0x01,
    0x0e,
    /* NID_ext_req */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x0e,
    /* NID_name */
    0x55,
    0x04,
    0x29,
    /* NID_dnQualifier */
    0x55,
    0x04,
    0x2e,
    /* NID_id_pe */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    /* NID_id_ad */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    /* NID_info_access */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x01,
    /* NID_ad_OCSP */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    /* NID_ad_ca_issuers */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x02,
    /* NID_OCSP_sign */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x09,
    /* NID_member_body */
    0x2a,
    /* NID_ISO_US */
    0x2a,
    0x86,
    0x48,
    /* NID_X9_57 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    /* NID_X9cm */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    0x04,
    /* NID_pkcs1 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    /* NID_pkcs5 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x05,
    /* NID_SMIME */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    /* NID_id_smime_mod */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    /* NID_id_smime_ct */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    /* NID_id_smime_aa */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    /* NID_id_smime_alg */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    /* NID_id_smime_cd */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x04,
    /* NID_id_smime_spq */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x05,
    /* NID_id_smime_cti */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    /* NID_id_smime_mod_cms */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x01,
    /* NID_id_smime_mod_ess */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x02,
    /* NID_id_smime_mod_oid */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x03,
    /* NID_id_smime_mod_msg_v3 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x04,
    /* NID_id_smime_mod_ets_eSignature_88 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x05,
    /* NID_id_smime_mod_ets_eSignature_97 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x06,
    /* NID_id_smime_mod_ets_eSigPolicy_88 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x07,
    /* NID_id_smime_mod_ets_eSigPolicy_97 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x00,
    0x08,
    /* NID_id_smime_ct_receipt */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x01,
    /* NID_id_smime_ct_authData */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x02,
    /* NID_id_smime_ct_publishCert */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x03,
    /* NID_id_smime_ct_TSTInfo */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x04,
    /* NID_id_smime_ct_TDTInfo */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x05,
    /* NID_id_smime_ct_contentInfo */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x06,
    /* NID_id_smime_ct_DVCSRequestData */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x07,
    /* NID_id_smime_ct_DVCSResponseData */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x08,
    /* NID_id_smime_aa_receiptRequest */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x01,
    /* NID_id_smime_aa_securityLabel */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x02,
    /* NID_id_smime_aa_mlExpandHistory */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x03,
    /* NID_id_smime_aa_contentHint */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x04,
    /* NID_id_smime_aa_msgSigDigest */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x05,
    /* NID_id_smime_aa_encapContentType */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x06,
    /* NID_id_smime_aa_contentIdentifier */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x07,
    /* NID_id_smime_aa_macValue */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x08,
    /* NID_id_smime_aa_equivalentLabels */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x09,
    /* NID_id_smime_aa_contentReference */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x0a,
    /* NID_id_smime_aa_encrypKeyPref */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x0b,
    /* NID_id_smime_aa_signingCertificate */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x0c,
    /* NID_id_smime_aa_smimeEncryptCerts */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x0d,
    /* NID_id_smime_aa_timeStampToken */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x0e,
    /* NID_id_smime_aa_ets_sigPolicyId */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x0f,
    /* NID_id_smime_aa_ets_commitmentType */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x10,
    /* NID_id_smime_aa_ets_signerLocation */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x11,
    /* NID_id_smime_aa_ets_signerAttr */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x12,
    /* NID_id_smime_aa_ets_otherSigCert */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x13,
    /* NID_id_smime_aa_ets_contentTimestamp */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x14,
    /* NID_id_smime_aa_ets_CertificateRefs */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x15,
    /* NID_id_smime_aa_ets_RevocationRefs */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x16,
    /* NID_id_smime_aa_ets_certValues */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x17,
    /* NID_id_smime_aa_ets_revocationValues */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x18,
    /* NID_id_smime_aa_ets_escTimeStamp */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x19,
    /* NID_id_smime_aa_ets_certCRLTimestamp */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x1a,
    /* NID_id_smime_aa_ets_archiveTimeStamp */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x1b,
    /* NID_id_smime_aa_signatureType */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x1c,
    /* NID_id_smime_aa_dvcs_dvc */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x02,
    0x1d,
    /* NID_id_smime_alg_ESDHwith3DES */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x01,
    /* NID_id_smime_alg_ESDHwithRC2 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x02,
    /* NID_id_smime_alg_3DESwrap */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x03,
    /* NID_id_smime_alg_RC2wrap */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x04,
    /* NID_id_smime_alg_ESDH */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x05,
    /* NID_id_smime_alg_CMS3DESwrap */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x06,
    /* NID_id_smime_alg_CMSRC2wrap */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x07,
    /* NID_id_smime_cd_ldap */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x04,
    0x01,
    /* NID_id_smime_spq_ets_sqt_uri */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x05,
    0x01,
    /* NID_id_smime_spq_ets_sqt_unotice */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x05,
    0x02,
    /* NID_id_smime_cti_ets_proofOfOrigin */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    0x01,
    /* NID_id_smime_cti_ets_proofOfReceipt */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    0x02,
    /* NID_id_smime_cti_ets_proofOfDelivery */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    0x03,
    /* NID_id_smime_cti_ets_proofOfSender */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    0x04,
    /* NID_id_smime_cti_ets_proofOfApproval */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    0x05,
    /* NID_id_smime_cti_ets_proofOfCreation */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x06,
    0x06,
    /* NID_md4 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x04,
    /* NID_id_pkix_mod */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    /* NID_id_qt */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x02,
    /* NID_id_it */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    /* NID_id_pkip */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    /* NID_id_alg */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x06,
    /* NID_id_cmc */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    /* NID_id_on */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x08,
    /* NID_id_pda */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x09,
    /* NID_id_aca */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    /* NID_id_qcs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0b,
    /* NID_id_cct */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0c,
    /* NID_id_pkix1_explicit_88 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x01,
    /* NID_id_pkix1_implicit_88 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x02,
    /* NID_id_pkix1_explicit_93 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x03,
    /* NID_id_pkix1_implicit_93 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x04,
    /* NID_id_mod_crmf */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x05,
    /* NID_id_mod_cmc */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x06,
    /* NID_id_mod_kea_profile_88 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x07,
    /* NID_id_mod_kea_profile_93 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x08,
    /* NID_id_mod_cmp */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x09,
    /* NID_id_mod_qualified_cert_88 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x0a,
    /* NID_id_mod_qualified_cert_93 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x0b,
    /* NID_id_mod_attribute_cert */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x0c,
    /* NID_id_mod_timestamp_protocol */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x0d,
    /* NID_id_mod_ocsp */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x0e,
    /* NID_id_mod_dvcs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x0f,
    /* NID_id_mod_cmp2000 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x00,
    0x10,
    /* NID_biometricInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x02,
    /* NID_qcStatements */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x03,
    /* NID_ac_auditEntity */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x04,
    /* NID_ac_targeting */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x05,
    /* NID_aaControls */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x06,
    /* NID_sbgp_ipAddrBlock */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x07,
    /* NID_sbgp_autonomousSysNum */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x08,
    /* NID_sbgp_routerIdentifier */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x09,
    /* NID_textNotice */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x02,
    0x03,
    /* NID_ipsecEndSystem */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x05,
    /* NID_ipsecTunnel */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x06,
    /* NID_ipsecUser */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x07,
    /* NID_dvcs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x03,
    0x0a,
    /* NID_id_it_caProtEncCert */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x01,
    /* NID_id_it_signKeyPairTypes */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x02,
    /* NID_id_it_encKeyPairTypes */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x03,
    /* NID_id_it_preferredSymmAlg */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x04,
    /* NID_id_it_caKeyUpdateInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x05,
    /* NID_id_it_currentCRL */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x06,
    /* NID_id_it_unsupportedOIDs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x07,
    /* NID_id_it_subscriptionRequest */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x08,
    /* NID_id_it_subscriptionResponse */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x09,
    /* NID_id_it_keyPairParamReq */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x0a,
    /* NID_id_it_keyPairParamRep */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x0b,
    /* NID_id_it_revPassphrase */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x0c,
    /* NID_id_it_implicitConfirm */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x0d,
    /* NID_id_it_confirmWaitTime */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x0e,
    /* NID_id_it_origPKIMessage */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x0f,
    /* NID_id_regCtrl */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    /* NID_id_regInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x02,
    /* NID_id_regCtrl_regToken */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    0x01,
    /* NID_id_regCtrl_authenticator */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    0x02,
    /* NID_id_regCtrl_pkiPublicationInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    0x03,
    /* NID_id_regCtrl_pkiArchiveOptions */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    0x04,
    /* NID_id_regCtrl_oldCertID */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    0x05,
    /* NID_id_regCtrl_protocolEncrKey */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x01,
    0x06,
    /* NID_id_regInfo_utf8Pairs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x02,
    0x01,
    /* NID_id_regInfo_certReq */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x05,
    0x02,
    0x02,
    /* NID_id_alg_des40 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x06,
    0x01,
    /* NID_id_alg_noSignature */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x06,
    0x02,
    /* NID_id_alg_dh_sig_hmac_sha1 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x06,
    0x03,
    /* NID_id_alg_dh_pop */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x06,
    0x04,
    /* NID_id_cmc_statusInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x01,
    /* NID_id_cmc_identification */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x02,
    /* NID_id_cmc_identityProof */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x03,
    /* NID_id_cmc_dataReturn */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x04,
    /* NID_id_cmc_transactionId */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x05,
    /* NID_id_cmc_senderNonce */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x06,
    /* NID_id_cmc_recipientNonce */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x07,
    /* NID_id_cmc_addExtensions */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x08,
    /* NID_id_cmc_encryptedPOP */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x09,
    /* NID_id_cmc_decryptedPOP */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x0a,
    /* NID_id_cmc_lraPOPWitness */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x0b,
    /* NID_id_cmc_getCert */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x0f,
    /* NID_id_cmc_getCRL */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x10,
    /* NID_id_cmc_revokeRequest */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x11,
    /* NID_id_cmc_regInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x12,
    /* NID_id_cmc_responseInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x13,
    /* NID_id_cmc_queryPending */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x15,
    /* NID_id_cmc_popLinkRandom */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x16,
    /* NID_id_cmc_popLinkWitness */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x17,
    /* NID_id_cmc_confirmCertAcceptance */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x07,
    0x18,
    /* NID_id_on_personalData */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x08,
    0x01,
    /* NID_id_pda_dateOfBirth */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x09,
    0x01,
    /* NID_id_pda_placeOfBirth */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x09,
    0x02,
    /* NID_id_pda_gender */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x09,
    0x03,
    /* NID_id_pda_countryOfCitizenship */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x09,
    0x04,
    /* NID_id_pda_countryOfResidence */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x09,
    0x05,
    /* NID_id_aca_authenticationInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    0x01,
    /* NID_id_aca_accessIdentity */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    0x02,
    /* NID_id_aca_chargingIdentity */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    0x03,
    /* NID_id_aca_group */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    0x04,
    /* NID_id_aca_role */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    0x05,
    /* NID_id_qcs_pkixQCSyntax_v1 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0b,
    0x01,
    /* NID_id_cct_crs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0c,
    0x01,
    /* NID_id_cct_PKIData */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0c,
    0x02,
    /* NID_id_cct_PKIResponse */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0c,
    0x03,
    /* NID_ad_timeStamping */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x03,
    /* NID_ad_dvcs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x04,
    /* NID_id_pkix_OCSP_basic */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x01,
    /* NID_id_pkix_OCSP_Nonce */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x02,
    /* NID_id_pkix_OCSP_CrlID */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x03,
    /* NID_id_pkix_OCSP_acceptableResponses */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x04,
    /* NID_id_pkix_OCSP_noCheck */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x05,
    /* NID_id_pkix_OCSP_archiveCutoff */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x06,
    /* NID_id_pkix_OCSP_serviceLocator */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x07,
    /* NID_id_pkix_OCSP_extendedStatus */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x08,
    /* NID_id_pkix_OCSP_valid */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x09,
    /* NID_id_pkix_OCSP_path */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x0a,
    /* NID_id_pkix_OCSP_trustRoot */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x01,
    0x0b,
    /* NID_algorithm */
    0x2b,
    0x0e,
    0x03,
    0x02,
    /* NID_rsaSignature */
    0x2b,
    0x0e,
    0x03,
    0x02,
    0x0b,
    /* NID_X500algorithms */
    0x55,
    0x08,
    /* NID_org */
    0x2b,
    /* NID_dod */
    0x2b,
    0x06,
    /* NID_iana */
    0x2b,
    0x06,
    0x01,
    /* NID_Directory */
    0x2b,
    0x06,
    0x01,
    0x01,
    /* NID_Management */
    0x2b,
    0x06,
    0x01,
    0x02,
    /* NID_Experimental */
    0x2b,
    0x06,
    0x01,
    0x03,
    /* NID_Private */
    0x2b,
    0x06,
    0x01,
    0x04,
    /* NID_Security */
    0x2b,
    0x06,
    0x01,
    0x05,
    /* NID_SNMPv2 */
    0x2b,
    0x06,
    0x01,
    0x06,
    /* NID_Mail */
    0x2b,
    0x06,
    0x01,
    0x07,
    /* NID_Enterprises */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    /* NID_dcObject */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x8b,
    0x3a,
    0x82,
    0x58,
    /* NID_domainComponent */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x19,
    /* NID_Domain */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x0d,
    /* NID_selected_attribute_types */
    0x55,
    0x01,
    0x05,
    /* NID_clearance */
    0x55,
    0x01,
    0x05,
    0x37,
    /* NID_md4WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x03,
    /* NID_ac_proxying */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x0a,
    /* NID_sinfo_access */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x0b,
    /* NID_id_aca_encAttrs */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x0a,
    0x06,
    /* NID_role */
    0x55,
    0x04,
    0x48,
    /* NID_policy_constraints */
    0x55,
    0x1d,
    0x24,
    /* NID_target_information */
    0x55,
    0x1d,
    0x37,
    /* NID_no_rev_avail */
    0x55,
    0x1d,
    0x38,
    /* NID_ansi_X9_62 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    /* NID_X9_62_prime_field */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x01,
    0x01,
    /* NID_X9_62_characteristic_two_field */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x01,
    0x02,
    /* NID_X9_62_id_ecPublicKey */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x02,
    0x01,
    /* NID_X9_62_prime192v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x01,
    /* NID_X9_62_prime192v2 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x02,
    /* NID_X9_62_prime192v3 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x03,
    /* NID_X9_62_prime239v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x04,
    /* NID_X9_62_prime239v2 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x05,
    /* NID_X9_62_prime239v3 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x06,
    /* NID_X9_62_prime256v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x01,
    0x07,
    /* NID_ecdsa_with_SHA1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x01,
    /* NID_ms_csp_name */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x11,
    0x01,
    /* NID_aes_128_ecb */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x01,
    /* NID_aes_128_cbc */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x02,
    /* NID_aes_128_ofb128 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x03,
    /* NID_aes_128_cfb128 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x04,
    /* NID_aes_192_ecb */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x15,
    /* NID_aes_192_cbc */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x16,
    /* NID_aes_192_ofb128 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x17,
    /* NID_aes_192_cfb128 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x18,
    /* NID_aes_256_ecb */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x29,
    /* NID_aes_256_cbc */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x2a,
    /* NID_aes_256_ofb128 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x2b,
    /* NID_aes_256_cfb128 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x2c,
    /* NID_hold_instruction_code */
    0x55,
    0x1d,
    0x17,
    /* NID_hold_instruction_none */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    0x02,
    0x01,
    /* NID_hold_instruction_call_issuer */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    0x02,
    0x02,
    /* NID_hold_instruction_reject */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x38,
    0x02,
    0x03,
    /* NID_data */
    0x09,
    /* NID_pss */
    0x09,
    0x92,
    0x26,
    /* NID_ucl */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    /* NID_pilot */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    /* NID_pilotAttributeType */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    /* NID_pilotAttributeSyntax */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x03,
    /* NID_pilotObjectClass */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    /* NID_pilotGroups */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x0a,
    /* NID_iA5StringSyntax */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x03,
    0x04,
    /* NID_caseIgnoreIA5StringSyntax */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x03,
    0x05,
    /* NID_pilotObject */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x03,
    /* NID_pilotPerson */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x04,
    /* NID_account */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x05,
    /* NID_document */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x06,
    /* NID_room */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x07,
    /* NID_documentSeries */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x09,
    /* NID_rFC822localPart */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x0e,
    /* NID_dNSDomain */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x0f,
    /* NID_domainRelatedObject */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x11,
    /* NID_friendlyCountry */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x12,
    /* NID_simpleSecurityObject */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x13,
    /* NID_pilotOrganization */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x14,
    /* NID_pilotDSA */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x15,
    /* NID_qualityLabelledData */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x04,
    0x16,
    /* NID_userId */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x01,
    /* NID_textEncodedORAddress */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x02,
    /* NID_rfc822Mailbox */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x03,
    /* NID_info */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x04,
    /* NID_favouriteDrink */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x05,
    /* NID_roomNumber */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x06,
    /* NID_photo */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x07,
    /* NID_userClass */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x08,
    /* NID_host */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x09,
    /* NID_manager */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x0a,
    /* NID_documentIdentifier */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x0b,
    /* NID_documentTitle */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x0c,
    /* NID_documentVersion */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x0d,
    /* NID_documentAuthor */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x0e,
    /* NID_documentLocation */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x0f,
    /* NID_homeTelephoneNumber */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x14,
    /* NID_secretary */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x15,
    /* NID_otherMailbox */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x16,
    /* NID_lastModifiedTime */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x17,
    /* NID_lastModifiedBy */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x18,
    /* NID_aRecord */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x1a,
    /* NID_pilotAttributeType27 */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x1b,
    /* NID_mXRecord */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x1c,
    /* NID_nSRecord */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x1d,
    /* NID_sOARecord */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x1e,
    /* NID_cNAMERecord */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x1f,
    /* NID_associatedDomain */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x25,
    /* NID_associatedName */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x26,
    /* NID_homePostalAddress */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x27,
    /* NID_personalTitle */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x28,
    /* NID_mobileTelephoneNumber */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x29,
    /* NID_pagerTelephoneNumber */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x2a,
    /* NID_friendlyCountryName */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x2b,
    /* NID_organizationalStatus */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x2d,
    /* NID_janetMailbox */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x2e,
    /* NID_mailPreferenceOption */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x2f,
    /* NID_buildingName */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x30,
    /* NID_dSAQuality */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x31,
    /* NID_singleLevelQuality */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x32,
    /* NID_subtreeMinimumQuality */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x33,
    /* NID_subtreeMaximumQuality */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x34,
    /* NID_personalSignature */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x35,
    /* NID_dITRedirect */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x36,
    /* NID_audio */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x37,
    /* NID_documentPublisher */
    0x09,
    0x92,
    0x26,
    0x89,
    0x93,
    0xf2,
    0x2c,
    0x64,
    0x01,
    0x38,
    /* NID_x500UniqueIdentifier */
    0x55,
    0x04,
    0x2d,
    /* NID_mime_mhs */
    0x2b,
    0x06,
    0x01,
    0x07,
    0x01,
    /* NID_mime_mhs_headings */
    0x2b,
    0x06,
    0x01,
    0x07,
    0x01,
    0x01,
    /* NID_mime_mhs_bodies */
    0x2b,
    0x06,
    0x01,
    0x07,
    0x01,
    0x02,
    /* NID_id_hex_partial_message */
    0x2b,
    0x06,
    0x01,
    0x07,
    0x01,
    0x01,
    0x01,
    /* NID_id_hex_multipart_message */
    0x2b,
    0x06,
    0x01,
    0x07,
    0x01,
    0x01,
    0x02,
    /* NID_generationQualifier */
    0x55,
    0x04,
    0x2c,
    /* NID_pseudonym */
    0x55,
    0x04,
    0x41,
    /* NID_id_set */
    0x67,
    0x2a,
    /* NID_set_ctype */
    0x67,
    0x2a,
    0x00,
    /* NID_set_msgExt */
    0x67,
    0x2a,
    0x01,
    /* NID_set_attr */
    0x67,
    0x2a,
    0x03,
    /* NID_set_policy */
    0x67,
    0x2a,
    0x05,
    /* NID_set_certExt */
    0x67,
    0x2a,
    0x07,
    /* NID_set_brand */
    0x67,
    0x2a,
    0x08,
    /* NID_setct_PANData */
    0x67,
    0x2a,
    0x00,
    0x00,
    /* NID_setct_PANToken */
    0x67,
    0x2a,
    0x00,
    0x01,
    /* NID_setct_PANOnly */
    0x67,
    0x2a,
    0x00,
    0x02,
    /* NID_setct_OIData */
    0x67,
    0x2a,
    0x00,
    0x03,
    /* NID_setct_PI */
    0x67,
    0x2a,
    0x00,
    0x04,
    /* NID_setct_PIData */
    0x67,
    0x2a,
    0x00,
    0x05,
    /* NID_setct_PIDataUnsigned */
    0x67,
    0x2a,
    0x00,
    0x06,
    /* NID_setct_HODInput */
    0x67,
    0x2a,
    0x00,
    0x07,
    /* NID_setct_AuthResBaggage */
    0x67,
    0x2a,
    0x00,
    0x08,
    /* NID_setct_AuthRevReqBaggage */
    0x67,
    0x2a,
    0x00,
    0x09,
    /* NID_setct_AuthRevResBaggage */
    0x67,
    0x2a,
    0x00,
    0x0a,
    /* NID_setct_CapTokenSeq */
    0x67,
    0x2a,
    0x00,
    0x0b,
    /* NID_setct_PInitResData */
    0x67,
    0x2a,
    0x00,
    0x0c,
    /* NID_setct_PI_TBS */
    0x67,
    0x2a,
    0x00,
    0x0d,
    /* NID_setct_PResData */
    0x67,
    0x2a,
    0x00,
    0x0e,
    /* NID_setct_AuthReqTBS */
    0x67,
    0x2a,
    0x00,
    0x10,
    /* NID_setct_AuthResTBS */
    0x67,
    0x2a,
    0x00,
    0x11,
    /* NID_setct_AuthResTBSX */
    0x67,
    0x2a,
    0x00,
    0x12,
    /* NID_setct_AuthTokenTBS */
    0x67,
    0x2a,
    0x00,
    0x13,
    /* NID_setct_CapTokenData */
    0x67,
    0x2a,
    0x00,
    0x14,
    /* NID_setct_CapTokenTBS */
    0x67,
    0x2a,
    0x00,
    0x15,
    /* NID_setct_AcqCardCodeMsg */
    0x67,
    0x2a,
    0x00,
    0x16,
    /* NID_setct_AuthRevReqTBS */
    0x67,
    0x2a,
    0x00,
    0x17,
    /* NID_setct_AuthRevResData */
    0x67,
    0x2a,
    0x00,
    0x18,
    /* NID_setct_AuthRevResTBS */
    0x67,
    0x2a,
    0x00,
    0x19,
    /* NID_setct_CapReqTBS */
    0x67,
    0x2a,
    0x00,
    0x1a,
    /* NID_setct_CapReqTBSX */
    0x67,
    0x2a,
    0x00,
    0x1b,
    /* NID_setct_CapResData */
    0x67,
    0x2a,
    0x00,
    0x1c,
    /* NID_setct_CapRevReqTBS */
    0x67,
    0x2a,
    0x00,
    0x1d,
    /* NID_setct_CapRevReqTBSX */
    0x67,
    0x2a,
    0x00,
    0x1e,
    /* NID_setct_CapRevResData */
    0x67,
    0x2a,
    0x00,
    0x1f,
    /* NID_setct_CredReqTBS */
    0x67,
    0x2a,
    0x00,
    0x20,
    /* NID_setct_CredReqTBSX */
    0x67,
    0x2a,
    0x00,
    0x21,
    /* NID_setct_CredResData */
    0x67,
    0x2a,
    0x00,
    0x22,
    /* NID_setct_CredRevReqTBS */
    0x67,
    0x2a,
    0x00,
    0x23,
    /* NID_setct_CredRevReqTBSX */
    0x67,
    0x2a,
    0x00,
    0x24,
    /* NID_setct_CredRevResData */
    0x67,
    0x2a,
    0x00,
    0x25,
    /* NID_setct_PCertReqData */
    0x67,
    0x2a,
    0x00,
    0x26,
    /* NID_setct_PCertResTBS */
    0x67,
    0x2a,
    0x00,
    0x27,
    /* NID_setct_BatchAdminReqData */
    0x67,
    0x2a,
    0x00,
    0x28,
    /* NID_setct_BatchAdminResData */
    0x67,
    0x2a,
    0x00,
    0x29,
    /* NID_setct_CardCInitResTBS */
    0x67,
    0x2a,
    0x00,
    0x2a,
    /* NID_setct_MeAqCInitResTBS */
    0x67,
    0x2a,
    0x00,
    0x2b,
    /* NID_setct_RegFormResTBS */
    0x67,
    0x2a,
    0x00,
    0x2c,
    /* NID_setct_CertReqData */
    0x67,
    0x2a,
    0x00,
    0x2d,
    /* NID_setct_CertReqTBS */
    0x67,
    0x2a,
    0x00,
    0x2e,
    /* NID_setct_CertResData */
    0x67,
    0x2a,
    0x00,
    0x2f,
    /* NID_setct_CertInqReqTBS */
    0x67,
    0x2a,
    0x00,
    0x30,
    /* NID_setct_ErrorTBS */
    0x67,
    0x2a,
    0x00,
    0x31,
    /* NID_setct_PIDualSignedTBE */
    0x67,
    0x2a,
    0x00,
    0x32,
    /* NID_setct_PIUnsignedTBE */
    0x67,
    0x2a,
    0x00,
    0x33,
    /* NID_setct_AuthReqTBE */
    0x67,
    0x2a,
    0x00,
    0x34,
    /* NID_setct_AuthResTBE */
    0x67,
    0x2a,
    0x00,
    0x35,
    /* NID_setct_AuthResTBEX */
    0x67,
    0x2a,
    0x00,
    0x36,
    /* NID_setct_AuthTokenTBE */
    0x67,
    0x2a,
    0x00,
    0x37,
    /* NID_setct_CapTokenTBE */
    0x67,
    0x2a,
    0x00,
    0x38,
    /* NID_setct_CapTokenTBEX */
    0x67,
    0x2a,
    0x00,
    0x39,
    /* NID_setct_AcqCardCodeMsgTBE */
    0x67,
    0x2a,
    0x00,
    0x3a,
    /* NID_setct_AuthRevReqTBE */
    0x67,
    0x2a,
    0x00,
    0x3b,
    /* NID_setct_AuthRevResTBE */
    0x67,
    0x2a,
    0x00,
    0x3c,
    /* NID_setct_AuthRevResTBEB */
    0x67,
    0x2a,
    0x00,
    0x3d,
    /* NID_setct_CapReqTBE */
    0x67,
    0x2a,
    0x00,
    0x3e,
    /* NID_setct_CapReqTBEX */
    0x67,
    0x2a,
    0x00,
    0x3f,
    /* NID_setct_CapResTBE */
    0x67,
    0x2a,
    0x00,
    0x40,
    /* NID_setct_CapRevReqTBE */
    0x67,
    0x2a,
    0x00,
    0x41,
    /* NID_setct_CapRevReqTBEX */
    0x67,
    0x2a,
    0x00,
    0x42,
    /* NID_setct_CapRevResTBE */
    0x67,
    0x2a,
    0x00,
    0x43,
    /* NID_setct_CredReqTBE */
    0x67,
    0x2a,
    0x00,
    0x44,
    /* NID_setct_CredReqTBEX */
    0x67,
    0x2a,
    0x00,
    0x45,
    /* NID_setct_CredResTBE */
    0x67,
    0x2a,
    0x00,
    0x46,
    /* NID_setct_CredRevReqTBE */
    0x67,
    0x2a,
    0x00,
    0x47,
    /* NID_setct_CredRevReqTBEX */
    0x67,
    0x2a,
    0x00,
    0x48,
    /* NID_setct_CredRevResTBE */
    0x67,
    0x2a,
    0x00,
    0x49,
    /* NID_setct_BatchAdminReqTBE */
    0x67,
    0x2a,
    0x00,
    0x4a,
    /* NID_setct_BatchAdminResTBE */
    0x67,
    0x2a,
    0x00,
    0x4b,
    /* NID_setct_RegFormReqTBE */
    0x67,
    0x2a,
    0x00,
    0x4c,
    /* NID_setct_CertReqTBE */
    0x67,
    0x2a,
    0x00,
    0x4d,
    /* NID_setct_CertReqTBEX */
    0x67,
    0x2a,
    0x00,
    0x4e,
    /* NID_setct_CertResTBE */
    0x67,
    0x2a,
    0x00,
    0x4f,
    /* NID_setct_CRLNotificationTBS */
    0x67,
    0x2a,
    0x00,
    0x50,
    /* NID_setct_CRLNotificationResTBS */
    0x67,
    0x2a,
    0x00,
    0x51,
    /* NID_setct_BCIDistributionTBS */
    0x67,
    0x2a,
    0x00,
    0x52,
    /* NID_setext_genCrypt */
    0x67,
    0x2a,
    0x01,
    0x01,
    /* NID_setext_miAuth */
    0x67,
    0x2a,
    0x01,
    0x03,
    /* NID_setext_pinSecure */
    0x67,
    0x2a,
    0x01,
    0x04,
    /* NID_setext_pinAny */
    0x67,
    0x2a,
    0x01,
    0x05,
    /* NID_setext_track2 */
    0x67,
    0x2a,
    0x01,
    0x07,
    /* NID_setext_cv */
    0x67,
    0x2a,
    0x01,
    0x08,
    /* NID_set_policy_root */
    0x67,
    0x2a,
    0x05,
    0x00,
    /* NID_setCext_hashedRoot */
    0x67,
    0x2a,
    0x07,
    0x00,
    /* NID_setCext_certType */
    0x67,
    0x2a,
    0x07,
    0x01,
    /* NID_setCext_merchData */
    0x67,
    0x2a,
    0x07,
    0x02,
    /* NID_setCext_cCertRequired */
    0x67,
    0x2a,
    0x07,
    0x03,
    /* NID_setCext_tunneling */
    0x67,
    0x2a,
    0x07,
    0x04,
    /* NID_setCext_setExt */
    0x67,
    0x2a,
    0x07,
    0x05,
    /* NID_setCext_setQualf */
    0x67,
    0x2a,
    0x07,
    0x06,
    /* NID_setCext_PGWYcapabilities */
    0x67,
    0x2a,
    0x07,
    0x07,
    /* NID_setCext_TokenIdentifier */
    0x67,
    0x2a,
    0x07,
    0x08,
    /* NID_setCext_Track2Data */
    0x67,
    0x2a,
    0x07,
    0x09,
    /* NID_setCext_TokenType */
    0x67,
    0x2a,
    0x07,
    0x0a,
    /* NID_setCext_IssuerCapabilities */
    0x67,
    0x2a,
    0x07,
    0x0b,
    /* NID_setAttr_Cert */
    0x67,
    0x2a,
    0x03,
    0x00,
    /* NID_setAttr_PGWYcap */
    0x67,
    0x2a,
    0x03,
    0x01,
    /* NID_setAttr_TokenType */
    0x67,
    0x2a,
    0x03,
    0x02,
    /* NID_setAttr_IssCap */
    0x67,
    0x2a,
    0x03,
    0x03,
    /* NID_set_rootKeyThumb */
    0x67,
    0x2a,
    0x03,
    0x00,
    0x00,
    /* NID_set_addPolicy */
    0x67,
    0x2a,
    0x03,
    0x00,
    0x01,
    /* NID_setAttr_Token_EMV */
    0x67,
    0x2a,
    0x03,
    0x02,
    0x01,
    /* NID_setAttr_Token_B0Prime */
    0x67,
    0x2a,
    0x03,
    0x02,
    0x02,
    /* NID_setAttr_IssCap_CVM */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x03,
    /* NID_setAttr_IssCap_T2 */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x04,
    /* NID_setAttr_IssCap_Sig */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x05,
    /* NID_setAttr_GenCryptgrm */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x03,
    0x01,
    /* NID_setAttr_T2Enc */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x04,
    0x01,
    /* NID_setAttr_T2cleartxt */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x04,
    0x02,
    /* NID_setAttr_TokICCsig */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x05,
    0x01,
    /* NID_setAttr_SecDevSig */
    0x67,
    0x2a,
    0x03,
    0x03,
    0x05,
    0x02,
    /* NID_set_brand_IATA_ATA */
    0x67,
    0x2a,
    0x08,
    0x01,
    /* NID_set_brand_Diners */
    0x67,
    0x2a,
    0x08,
    0x1e,
    /* NID_set_brand_AmericanExpress */
    0x67,
    0x2a,
    0x08,
    0x22,
    /* NID_set_brand_JCB */
    0x67,
    0x2a,
    0x08,
    0x23,
    /* NID_set_brand_Visa */
    0x67,
    0x2a,
    0x08,
    0x04,
    /* NID_set_brand_MasterCard */
    0x67,
    0x2a,
    0x08,
    0x05,
    /* NID_set_brand_Novus */
    0x67,
    0x2a,
    0x08,
    0xae,
    0x7b,
    /* NID_des_cdmf */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x03,
    0x0a,
    /* NID_rsaOAEPEncryptionSET */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x06,
    /* NID_international_organizations */
    0x67,
    /* NID_ms_smartcard_login */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x14,
    0x02,
    0x02,
    /* NID_ms_upn */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x14,
    0x02,
    0x03,
    /* NID_streetAddress */
    0x55,
    0x04,
    0x09,
    /* NID_postalCode */
    0x55,
    0x04,
    0x11,
    /* NID_id_ppl */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x15,
    /* NID_proxyCertInfo */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x01,
    0x0e,
    /* NID_id_ppl_anyLanguage */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x15,
    0x00,
    /* NID_id_ppl_inheritAll */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x15,
    0x01,
    /* NID_name_constraints */
    0x55,
    0x1d,
    0x1e,
    /* NID_Independent */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x15,
    0x02,
    /* NID_sha256WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x0b,
    /* NID_sha384WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x0c,
    /* NID_sha512WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x0d,
    /* NID_sha224WithRSAEncryption */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x0e,
    /* NID_sha256 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x02,
    0x01,
    /* NID_sha384 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x02,
    0x02,
    /* NID_sha512 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x02,
    0x03,
    /* NID_sha224 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x02,
    0x04,
    /* NID_identified_organization */
    0x2b,
    /* NID_certicom_arc */
    0x2b,
    0x81,
    0x04,
    /* NID_wap */
    0x67,
    0x2b,
    /* NID_wap_wsg */
    0x67,
    0x2b,
    0x01,
    /* NID_X9_62_id_characteristic_two_basis */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x01,
    0x02,
    0x03,
    /* NID_X9_62_onBasis */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x01,
    0x02,
    0x03,
    0x01,
    /* NID_X9_62_tpBasis */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x01,
    0x02,
    0x03,
    0x02,
    /* NID_X9_62_ppBasis */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x01,
    0x02,
    0x03,
    0x03,
    /* NID_X9_62_c2pnb163v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x01,
    /* NID_X9_62_c2pnb163v2 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x02,
    /* NID_X9_62_c2pnb163v3 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x03,
    /* NID_X9_62_c2pnb176v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x04,
    /* NID_X9_62_c2tnb191v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x05,
    /* NID_X9_62_c2tnb191v2 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x06,
    /* NID_X9_62_c2tnb191v3 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x07,
    /* NID_X9_62_c2onb191v4 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x08,
    /* NID_X9_62_c2onb191v5 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x09,
    /* NID_X9_62_c2pnb208w1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x0a,
    /* NID_X9_62_c2tnb239v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x0b,
    /* NID_X9_62_c2tnb239v2 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x0c,
    /* NID_X9_62_c2tnb239v3 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x0d,
    /* NID_X9_62_c2onb239v4 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x0e,
    /* NID_X9_62_c2onb239v5 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x0f,
    /* NID_X9_62_c2pnb272w1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x10,
    /* NID_X9_62_c2pnb304w1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x11,
    /* NID_X9_62_c2tnb359v1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x12,
    /* NID_X9_62_c2pnb368w1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x13,
    /* NID_X9_62_c2tnb431r1 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x03,
    0x00,
    0x14,
    /* NID_secp112r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x06,
    /* NID_secp112r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x07,
    /* NID_secp128r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x1c,
    /* NID_secp128r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x1d,
    /* NID_secp160k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x09,
    /* NID_secp160r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x08,
    /* NID_secp160r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x1e,
    /* NID_secp192k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x1f,
    /* NID_secp224k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x20,
    /* NID_secp224r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x21,
    /* NID_secp256k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x0a,
    /* NID_secp384r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x22,
    /* NID_secp521r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x23,
    /* NID_sect113r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x04,
    /* NID_sect113r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x05,
    /* NID_sect131r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x16,
    /* NID_sect131r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x17,
    /* NID_sect163k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x01,
    /* NID_sect163r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x02,
    /* NID_sect163r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x0f,
    /* NID_sect193r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x18,
    /* NID_sect193r2 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x19,
    /* NID_sect233k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x1a,
    /* NID_sect233r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x1b,
    /* NID_sect239k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x03,
    /* NID_sect283k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x10,
    /* NID_sect283r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x11,
    /* NID_sect409k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x24,
    /* NID_sect409r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x25,
    /* NID_sect571k1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x26,
    /* NID_sect571r1 */
    0x2b,
    0x81,
    0x04,
    0x00,
    0x27,
    /* NID_wap_wsg_idm_ecid_wtls1 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x01,
    /* NID_wap_wsg_idm_ecid_wtls3 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x03,
    /* NID_wap_wsg_idm_ecid_wtls4 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x04,
    /* NID_wap_wsg_idm_ecid_wtls5 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x05,
    /* NID_wap_wsg_idm_ecid_wtls6 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x06,
    /* NID_wap_wsg_idm_ecid_wtls7 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x07,
    /* NID_wap_wsg_idm_ecid_wtls8 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x08,
    /* NID_wap_wsg_idm_ecid_wtls9 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x09,
    /* NID_wap_wsg_idm_ecid_wtls10 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x0a,
    /* NID_wap_wsg_idm_ecid_wtls11 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x0b,
    /* NID_wap_wsg_idm_ecid_wtls12 */
    0x67,
    0x2b,
    0x01,
    0x04,
    0x0c,
    /* NID_any_policy */
    0x55,
    0x1d,
    0x20,
    0x00,
    /* NID_policy_mappings */
    0x55,
    0x1d,
    0x21,
    /* NID_inhibit_any_policy */
    0x55,
    0x1d,
    0x36,
    /* NID_camellia_128_cbc */
    0x2a,
    0x83,
    0x08,
    0x8c,
    0x9a,
    0x4b,
    0x3d,
    0x01,
    0x01,
    0x01,
    0x02,
    /* NID_camellia_192_cbc */
    0x2a,
    0x83,
    0x08,
    0x8c,
    0x9a,
    0x4b,
    0x3d,
    0x01,
    0x01,
    0x01,
    0x03,
    /* NID_camellia_256_cbc */
    0x2a,
    0x83,
    0x08,
    0x8c,
    0x9a,
    0x4b,
    0x3d,
    0x01,
    0x01,
    0x01,
    0x04,
    /* NID_camellia_128_ecb */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x01,
    /* NID_camellia_192_ecb */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x15,
    /* NID_camellia_256_ecb */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x29,
    /* NID_camellia_128_cfb128 */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x04,
    /* NID_camellia_192_cfb128 */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x18,
    /* NID_camellia_256_cfb128 */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x2c,
    /* NID_camellia_128_ofb128 */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x03,
    /* NID_camellia_192_ofb128 */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x17,
    /* NID_camellia_256_ofb128 */
    0x03,
    0xa2,
    0x31,
    0x05,
    0x03,
    0x01,
    0x09,
    0x2b,
    /* NID_subject_directory_attributes */
    0x55,
    0x1d,
    0x09,
    /* NID_issuing_distribution_point */
    0x55,
    0x1d,
    0x1c,
    /* NID_certificate_issuer */
    0x55,
    0x1d,
    0x1d,
    /* NID_kisa */
    0x2a,
    0x83,
    0x1a,
    0x8c,
    0x9a,
    0x44,
    /* NID_seed_ecb */
    0x2a,
    0x83,
    0x1a,
    0x8c,
    0x9a,
    0x44,
    0x01,
    0x03,
    /* NID_seed_cbc */
    0x2a,
    0x83,
    0x1a,
    0x8c,
    0x9a,
    0x44,
    0x01,
    0x04,
    /* NID_seed_ofb128 */
    0x2a,
    0x83,
    0x1a,
    0x8c,
    0x9a,
    0x44,
    0x01,
    0x06,
    /* NID_seed_cfb128 */
    0x2a,
    0x83,
    0x1a,
    0x8c,
    0x9a,
    0x44,
    0x01,
    0x05,
    /* NID_hmac_md5 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x08,
    0x01,
    0x01,
    /* NID_hmac_sha1 */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x08,
    0x01,
    0x02,
    /* NID_id_PasswordBasedMAC */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf6,
    0x7d,
    0x07,
    0x42,
    0x0d,
    /* NID_id_DHBasedMac */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf6,
    0x7d,
    0x07,
    0x42,
    0x1e,
    /* NID_id_it_suppLangTags */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x04,
    0x10,
    /* NID_caRepository */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x30,
    0x05,
    /* NID_id_smime_ct_compressedData */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x09,
    /* NID_id_ct_asciiTextWithCRLF */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x01,
    0x1b,
    /* NID_id_aes128_wrap */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x05,
    /* NID_id_aes192_wrap */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x19,
    /* NID_id_aes256_wrap */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x2d,
    /* NID_ecdsa_with_Recommended */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x02,
    /* NID_ecdsa_with_Specified */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x03,
    /* NID_ecdsa_with_SHA224 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x03,
    0x01,
    /* NID_ecdsa_with_SHA256 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x03,
    0x02,
    /* NID_ecdsa_with_SHA384 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x03,
    0x03,
    /* NID_ecdsa_with_SHA512 */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3d,
    0x04,
    0x03,
    0x04,
    /* NID_hmacWithMD5 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x06,
    /* NID_hmacWithSHA224 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x08,
    /* NID_hmacWithSHA256 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x09,
    /* NID_hmacWithSHA384 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x0a,
    /* NID_hmacWithSHA512 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x02,
    0x0b,
    /* NID_dsa_with_SHA224 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x03,
    0x01,
    /* NID_dsa_with_SHA256 */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x03,
    0x02,
    /* NID_whirlpool */
    0x28,
    0xcf,
    0x06,
    0x03,
    0x00,
    0x37,
    /* NID_cryptopro */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    /* NID_cryptocom */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    /* NID_id_GostR3411_94_with_GostR3410_2001 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x03,
    /* NID_id_GostR3411_94_with_GostR3410_94 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x04,
    /* NID_id_GostR3411_94 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x09,
    /* NID_id_HMACGostR3411_94 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x0a,
    /* NID_id_GostR3410_2001 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x13,
    /* NID_id_GostR3410_94 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x14,
    /* NID_id_Gost28147_89 */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x15,
    /* NID_id_Gost28147_89_MAC */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x16,
    /* NID_id_GostR3411_94_prf */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x17,
    /* NID_id_GostR3410_2001DH */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x62,
    /* NID_id_GostR3410_94DH */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x63,
    /* NID_id_Gost28147_89_CryptoPro_KeyMeshing */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x0e,
    0x01,
    /* NID_id_Gost28147_89_None_KeyMeshing */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x0e,
    0x00,
    /* NID_id_GostR3411_94_TestParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1e,
    0x00,
    /* NID_id_GostR3411_94_CryptoProParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1e,
    0x01,
    /* NID_id_Gost28147_89_TestParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x00,
    /* NID_id_Gost28147_89_CryptoPro_A_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x01,
    /* NID_id_Gost28147_89_CryptoPro_B_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x02,
    /* NID_id_Gost28147_89_CryptoPro_C_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x03,
    /* NID_id_Gost28147_89_CryptoPro_D_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x04,
    /* NID_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x05,
    /* NID_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x06,
    /* NID_id_Gost28147_89_CryptoPro_RIC_1_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x1f,
    0x07,
    /* NID_id_GostR3410_94_TestParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x20,
    0x00,
    /* NID_id_GostR3410_94_CryptoPro_A_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x20,
    0x02,
    /* NID_id_GostR3410_94_CryptoPro_B_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x20,
    0x03,
    /* NID_id_GostR3410_94_CryptoPro_C_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x20,
    0x04,
    /* NID_id_GostR3410_94_CryptoPro_D_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x20,
    0x05,
    /* NID_id_GostR3410_94_CryptoPro_XchA_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x21,
    0x01,
    /* NID_id_GostR3410_94_CryptoPro_XchB_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x21,
    0x02,
    /* NID_id_GostR3410_94_CryptoPro_XchC_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x21,
    0x03,
    /* NID_id_GostR3410_2001_TestParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x23,
    0x00,
    /* NID_id_GostR3410_2001_CryptoPro_A_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x23,
    0x01,
    /* NID_id_GostR3410_2001_CryptoPro_B_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x23,
    0x02,
    /* NID_id_GostR3410_2001_CryptoPro_C_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x23,
    0x03,
    /* NID_id_GostR3410_2001_CryptoPro_XchA_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x24,
    0x00,
    /* NID_id_GostR3410_2001_CryptoPro_XchB_ParamSet */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x24,
    0x01,
    /* NID_id_GostR3410_94_a */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x14,
    0x01,
    /* NID_id_GostR3410_94_aBis */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x14,
    0x02,
    /* NID_id_GostR3410_94_b */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x14,
    0x03,
    /* NID_id_GostR3410_94_bBis */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x02,
    0x14,
    0x04,
    /* NID_id_Gost28147_89_cc */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    0x01,
    0x06,
    0x01,
    /* NID_id_GostR3410_94_cc */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    0x01,
    0x05,
    0x03,
    /* NID_id_GostR3410_2001_cc */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    0x01,
    0x05,
    0x04,
    /* NID_id_GostR3411_94_with_GostR3410_94_cc */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    0x01,
    0x03,
    0x03,
    /* NID_id_GostR3411_94_with_GostR3410_2001_cc */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    0x01,
    0x03,
    0x04,
    /* NID_id_GostR3410_2001_ParamSet_cc */
    0x2a,
    0x85,
    0x03,
    0x02,
    0x09,
    0x01,
    0x08,
    0x01,
    /* NID_LocalKeySet */
    0x2b,
    0x06,
    0x01,
    0x04,
    0x01,
    0x82,
    0x37,
    0x11,
    0x02,
    /* NID_freshest_crl */
    0x55,
    0x1d,
    0x2e,
    /* NID_id_on_permanentIdentifier */
    0x2b,
    0x06,
    0x01,
    0x05,
    0x05,
    0x07,
    0x08,
    0x03,
    /* NID_searchGuide */
    0x55,
    0x04,
    0x0e,
    /* NID_businessCategory */
    0x55,
    0x04,
    0x0f,
    /* NID_postalAddress */
    0x55,
    0x04,
    0x10,
    /* NID_postOfficeBox */
    0x55,
    0x04,
    0x12,
    /* NID_physicalDeliveryOfficeName */
    0x55,
    0x04,
    0x13,
    /* NID_telephoneNumber */
    0x55,
    0x04,
    0x14,
    /* NID_telexNumber */
    0x55,
    0x04,
    0x15,
    /* NID_teletexTerminalIdentifier */
    0x55,
    0x04,
    0x16,
    /* NID_facsimileTelephoneNumber */
    0x55,
    0x04,
    0x17,
    /* NID_x121Address */
    0x55,
    0x04,
    0x18,
    /* NID_internationaliSDNNumber */
    0x55,
    0x04,
    0x19,
    /* NID_registeredAddress */
    0x55,
    0x04,
    0x1a,
    /* NID_destinationIndicator */
    0x55,
    0x04,
    0x1b,
    /* NID_preferredDeliveryMethod */
    0x55,
    0x04,
    0x1c,
    /* NID_presentationAddress */
    0x55,
    0x04,
    0x1d,
    /* NID_supportedApplicationContext */
    0x55,
    0x04,
    0x1e,
    /* NID_member */
    0x55,
    0x04,
    0x1f,
    /* NID_owner */
    0x55,
    0x04,
    0x20,
    /* NID_roleOccupant */
    0x55,
    0x04,
    0x21,
    /* NID_seeAlso */
    0x55,
    0x04,
    0x22,
    /* NID_userPassword */
    0x55,
    0x04,
    0x23,
    /* NID_userCertificate */
    0x55,
    0x04,
    0x24,
    /* NID_cACertificate */
    0x55,
    0x04,
    0x25,
    /* NID_authorityRevocationList */
    0x55,
    0x04,
    0x26,
    /* NID_certificateRevocationList */
    0x55,
    0x04,
    0x27,
    /* NID_crossCertificatePair */
    0x55,
    0x04,
    0x28,
    /* NID_enhancedSearchGuide */
    0x55,
    0x04,
    0x2f,
    /* NID_protocolInformation */
    0x55,
    0x04,
    0x30,
    /* NID_distinguishedName */
    0x55,
    0x04,
    0x31,
    /* NID_uniqueMember */
    0x55,
    0x04,
    0x32,
    /* NID_houseIdentifier */
    0x55,
    0x04,
    0x33,
    /* NID_supportedAlgorithms */
    0x55,
    0x04,
    0x34,
    /* NID_deltaRevocationList */
    0x55,
    0x04,
    0x35,
    /* NID_dmdName */
    0x55,
    0x04,
    0x36,
    /* NID_id_alg_PWRI_KEK */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x09,
    0x10,
    0x03,
    0x09,
    /* NID_aes_128_gcm */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x06,
    /* NID_aes_128_ccm */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x07,
    /* NID_id_aes128_wrap_pad */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x08,
    /* NID_aes_192_gcm */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x1a,
    /* NID_aes_192_ccm */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x1b,
    /* NID_id_aes192_wrap_pad */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x1c,
    /* NID_aes_256_gcm */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x2e,
    /* NID_aes_256_ccm */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x2f,
    /* NID_id_aes256_wrap_pad */
    0x60,
    0x86,
    0x48,
    0x01,
    0x65,
    0x03,
    0x04,
    0x01,
    0x30,
    /* NID_id_camellia128_wrap */
    0x2a,
    0x83,
    0x08,
    0x8c,
    0x9a,
    0x4b,
    0x3d,
    0x01,
    0x01,
    0x03,
    0x02,
    /* NID_id_camellia192_wrap */
    0x2a,
    0x83,
    0x08,
    0x8c,
    0x9a,
    0x4b,
    0x3d,
    0x01,
    0x01,
    0x03,
    0x03,
    /* NID_id_camellia256_wrap */
    0x2a,
    0x83,
    0x08,
    0x8c,
    0x9a,
    0x4b,
    0x3d,
    0x01,
    0x01,
    0x03,
    0x04,
    /* NID_anyExtendedKeyUsage */
    0x55,
    0x1d,
    0x25,
    0x00,
    /* NID_mgf1 */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x08,
    /* NID_rsassaPss */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x0a,
    /* NID_rsaesOaep */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x07,
    /* NID_dhpublicnumber */
    0x2a,
    0x86,
    0x48,
    0xce,
    0x3e,
    0x02,
    0x01,
    /* NID_brainpoolP160r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x01,
    /* NID_brainpoolP160t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x02,
    /* NID_brainpoolP192r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x03,
    /* NID_brainpoolP192t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x04,
    /* NID_brainpoolP224r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x05,
    /* NID_brainpoolP224t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x06,
    /* NID_brainpoolP256r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x07,
    /* NID_brainpoolP256t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x08,
    /* NID_brainpoolP320r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x09,
    /* NID_brainpoolP320t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x0a,
    /* NID_brainpoolP384r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x0b,
    /* NID_brainpoolP384t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x0c,
    /* NID_brainpoolP512r1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x0d,
    /* NID_brainpoolP512t1 */
    0x2b,
    0x24,
    0x03,
    0x03,
    0x02,
    0x08,
    0x01,
    0x01,
    0x0e,
    /* NID_pSpecified */
    0x2a,
    0x86,
    0x48,
    0x86,
    0xf7,
    0x0d,
    0x01,
    0x01,
    0x09,
    /* NID_dhSinglePass_stdDH_sha1kdf_scheme */
    0x2b,
    0x81,
    0x05,
    0x10,
    0x86,
    0x48,
    0x3f,
    0x00,
    0x02,
    /* NID_dhSinglePass_stdDH_sha224kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0b,
    0x00,
    /* NID_dhSinglePass_stdDH_sha256kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0b,
    0x01,
    /* NID_dhSinglePass_stdDH_sha384kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0b,
    0x02,
    /* NID_dhSinglePass_stdDH_sha512kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0b,
    0x03,
    /* NID_dhSinglePass_cofactorDH_sha1kdf_scheme */
    0x2b,
    0x81,
    0x05,
    0x10,
    0x86,
    0x48,
    0x3f,
    0x00,
    0x03,
    /* NID_dhSinglePass_cofactorDH_sha224kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0e,
    0x00,
    /* NID_dhSinglePass_cofactorDH_sha256kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0e,
    0x01,
    /* NID_dhSinglePass_cofactorDH_sha384kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0e,
    0x02,
    /* NID_dhSinglePass_cofactorDH_sha512kdf_scheme */
    0x2b,
    0x81,
    0x04,
    0x01,
    0x0e,
    0x03,
    /* NID_ED25519 */
    0x2b,
    0x65,
    0x70,
    /* NID_ED448 */
    0x2b,
    0x65,
    0x71,
};

static const ASN1_OBJECT kObjects[NUM_NID] = {
    {"UNDEF", "undefined", NID_undef, 0, NULL, 0},
    {"rsadsi", "RSA Data Security, Inc.", NID_rsadsi, 6, &kObjectData[0], 0},
    {"pkcs", "RSA Data Security, Inc. PKCS", NID_pkcs, 7, &kObjectData[6], 0},
    {"MD2", "md2", NID_md2, 8, &kObjectData[13], 0},
    {"MD5", "md5", NID_md5, 8, &kObjectData[21], 0},
    {"RC4", "rc4", NID_rc4, 8, &kObjectData[29], 0},
    {"rsaEncryption", "rsaEncryption", NID_rsaEncryption, 9, &kObjectData[37],
     0},
    {"RSA-MD2", "md2WithRSAEncryption", NID_md2WithRSAEncryption, 9,
     &kObjectData[46], 0},
    {"RSA-MD5", "md5WithRSAEncryption", NID_md5WithRSAEncryption, 9,
     &kObjectData[55], 0},
    {"PBE-MD2-DES", "pbeWithMD2AndDES-CBC", NID_pbeWithMD2AndDES_CBC, 9,
     &kObjectData[64], 0},
    {"PBE-MD5-DES", "pbeWithMD5AndDES-CBC", NID_pbeWithMD5AndDES_CBC, 9,
     &kObjectData[73], 0},
    {"X500", "directory services (X.500)", NID_X500, 1, &kObjectData[82], 0},
    {"X509", "X509", NID_X509, 2, &kObjectData[83], 0},
    {"CN", "commonName", NID_commonName, 3, &kObjectData[85], 0},
    {"C", "countryName", NID_countryName, 3, &kObjectData[88], 0},
    {"L", "localityName", NID_localityName, 3, &kObjectData[91], 0},
    {"ST", "stateOrProvinceName", NID_stateOrProvinceName, 3, &kObjectData[94],
     0},
    {"O", "organizationName", NID_organizationName, 3, &kObjectData[97], 0},
    {"OU", "organizationalUnitName", NID_organizationalUnitName, 3,
     &kObjectData[100], 0},
    {"RSA", "rsa", NID_rsa, 4, &kObjectData[103], 0},
    {"pkcs7", "pkcs7", NID_pkcs7, 8, &kObjectData[107], 0},
    {"pkcs7-data", "pkcs7-data", NID_pkcs7_data, 9, &kObjectData[115], 0},
    {"pkcs7-signedData", "pkcs7-signedData", NID_pkcs7_signed, 9,
     &kObjectData[124], 0},
    {"pkcs7-envelopedData", "pkcs7-envelopedData", NID_pkcs7_enveloped, 9,
     &kObjectData[133], 0},
    {"pkcs7-signedAndEnvelopedData", "pkcs7-signedAndEnvelopedData",
     NID_pkcs7_signedAndEnveloped, 9, &kObjectData[142], 0},
    {"pkcs7-digestData", "pkcs7-digestData", NID_pkcs7_digest, 9,
     &kObjectData[151], 0},
    {"pkcs7-encryptedData", "pkcs7-encryptedData", NID_pkcs7_encrypted, 9,
     &kObjectData[160], 0},
    {"pkcs3", "pkcs3", NID_pkcs3, 8, &kObjectData[169], 0},
    {"dhKeyAgreement", "dhKeyAgreement", NID_dhKeyAgreement, 9,
     &kObjectData[177], 0},
    {"DES-ECB", "des-ecb", NID_des_ecb, 5, &kObjectData[186], 0},
    {"DES-CFB", "des-cfb", NID_des_cfb64, 5, &kObjectData[191], 0},
    {"DES-CBC", "des-cbc", NID_des_cbc, 5, &kObjectData[196], 0},
    {"DES-EDE", "des-ede", NID_des_ede_ecb, 5, &kObjectData[201], 0},
    {"DES-EDE3", "des-ede3", NID_des_ede3_ecb, 0, NULL, 0},
    {"IDEA-CBC", "idea-cbc", NID_idea_cbc, 11, &kObjectData[206], 0},
    {"IDEA-CFB", "idea-cfb", NID_idea_cfb64, 0, NULL, 0},
    {"IDEA-ECB", "idea-ecb", NID_idea_ecb, 0, NULL, 0},
    {"RC2-CBC", "rc2-cbc", NID_rc2_cbc, 8, &kObjectData[217], 0},
    {"RC2-ECB", "rc2-ecb", NID_rc2_ecb, 0, NULL, 0},
    {"RC2-CFB", "rc2-cfb", NID_rc2_cfb64, 0, NULL, 0},
    {"RC2-OFB", "rc2-ofb", NID_rc2_ofb64, 0, NULL, 0},
    {"SHA", "sha", NID_sha, 5, &kObjectData[225], 0},
    {"RSA-SHA", "shaWithRSAEncryption", NID_shaWithRSAEncryption, 5,
     &kObjectData[230], 0},
    {"DES-EDE-CBC", "des-ede-cbc", NID_des_ede_cbc, 0, NULL, 0},
    {"DES-EDE3-CBC", "des-ede3-cbc", NID_des_ede3_cbc, 8, &kObjectData[235], 0},
    {"DES-OFB", "des-ofb", NID_des_ofb64, 5, &kObjectData[243], 0},
    {"IDEA-OFB", "idea-ofb", NID_idea_ofb64, 0, NULL, 0},
    {"pkcs9", "pkcs9", NID_pkcs9, 8, &kObjectData[248], 0},
    {"emailAddress", "emailAddress", NID_pkcs9_emailAddress, 9,
     &kObjectData[256], 0},
    {"unstructuredName", "unstructuredName", NID_pkcs9_unstructuredName, 9,
     &kObjectData[265], 0},
    {"contentType", "contentType", NID_pkcs9_contentType, 9, &kObjectData[274],
     0},
    {"messageDigest", "messageDigest", NID_pkcs9_messageDigest, 9,
     &kObjectData[283], 0},
    {"signingTime", "signingTime", NID_pkcs9_signingTime, 9, &kObjectData[292],
     0},
    {"countersignature", "countersignature", NID_pkcs9_countersignature, 9,
     &kObjectData[301], 0},
    {"challengePassword", "challengePassword", NID_pkcs9_challengePassword, 9,
     &kObjectData[310], 0},
    {"unstructuredAddress", "unstructuredAddress",
     NID_pkcs9_unstructuredAddress, 9, &kObjectData[319], 0},
    {"extendedCertificateAttributes", "extendedCertificateAttributes",
     NID_pkcs9_extCertAttributes, 9, &kObjectData[328], 0},
    {"Netscape", "Netscape Communications Corp.", NID_netscape, 7,
     &kObjectData[337], 0},
    {"nsCertExt", "Netscape Certificate Extension", NID_netscape_cert_extension,
     8, &kObjectData[344], 0},
    {"nsDataType", "Netscape Data Type", NID_netscape_data_type, 8,
     &kObjectData[352], 0},
    {"DES-EDE-CFB", "des-ede-cfb", NID_des_ede_cfb64, 0, NULL, 0},
    {"DES-EDE3-CFB", "des-ede3-cfb", NID_des_ede3_cfb64, 0, NULL, 0},
    {"DES-EDE-OFB", "des-ede-ofb", NID_des_ede_ofb64, 0, NULL, 0},
    {"DES-EDE3-OFB", "des-ede3-ofb", NID_des_ede3_ofb64, 0, NULL, 0},
    {"SHA1", "sha1", NID_sha1, 5, &kObjectData[360], 0},
    {"RSA-SHA1", "sha1WithRSAEncryption", NID_sha1WithRSAEncryption, 9,
     &kObjectData[365], 0},
    {"DSA-SHA", "dsaWithSHA", NID_dsaWithSHA, 5, &kObjectData[374], 0},
    {"DSA-old", "dsaEncryption-old", NID_dsa_2, 5, &kObjectData[379], 0},
    {"PBE-SHA1-RC2-64", "pbeWithSHA1AndRC2-CBC", NID_pbeWithSHA1AndRC2_CBC, 9,
     &kObjectData[384], 0},
    {"PBKDF2", "PBKDF2", NID_id_pbkdf2, 9, &kObjectData[393], 0},
    {"DSA-SHA1-old", "dsaWithSHA1-old", NID_dsaWithSHA1_2, 5, &kObjectData[402],
     0},
    {"nsCertType", "Netscape Cert Type", NID_netscape_cert_type, 9,
     &kObjectData[407], 0},
    {"nsBaseUrl", "Netscape Base Url", NID_netscape_base_url, 9,
     &kObjectData[416], 0},
    {"nsRevocationUrl", "Netscape Revocation Url", NID_netscape_revocation_url,
     9, &kObjectData[425], 0},
    {"nsCaRevocationUrl", "Netscape CA Revocation Url",
     NID_netscape_ca_revocation_url, 9, &kObjectData[434], 0},
    {"nsRenewalUrl", "Netscape Renewal Url", NID_netscape_renewal_url, 9,
     &kObjectData[443], 0},
    {"nsCaPolicyUrl", "Netscape CA Policy Url", NID_netscape_ca_policy_url, 9,
     &kObjectData[452], 0},
    {"nsSslServerName", "Netscape SSL Server Name",
     NID_netscape_ssl_server_name, 9, &kObjectData[461], 0},
    {"nsComment", "Netscape Comment", NID_netscape_comment, 9,
     &kObjectData[470], 0},
    {"nsCertSequence", "Netscape Certificate Sequence",
     NID_netscape_cert_sequence, 9, &kObjectData[479], 0},
    {"DESX-CBC", "desx-cbc", NID_desx_cbc, 0, NULL, 0},
    {"id-ce", "id-ce", NID_id_ce, 2, &kObjectData[488], 0},
    {"subjectKeyIdentifier", "X509v3 Subject Key Identifier",
     NID_subject_key_identifier, 3, &kObjectData[490], 0},
    {"keyUsage", "X509v3 Key Usage", NID_key_usage, 3, &kObjectData[493], 0},
    {"privateKeyUsagePeriod", "X509v3 Private Key Usage Period",
     NID_private_key_usage_period, 3, &kObjectData[496], 0},
    {"subjectAltName", "X509v3 Subject Alternative Name", NID_subject_alt_name,
     3, &kObjectData[499], 0},
    {"issuerAltName", "X509v3 Issuer Alternative Name", NID_issuer_alt_name, 3,
     &kObjectData[502], 0},
    {"basicConstraints", "X509v3 Basic Constraints", NID_basic_constraints, 3,
     &kObjectData[505], 0},
    {"crlNumber", "X509v3 CRL Number", NID_crl_number, 3, &kObjectData[508], 0},
    {"certificatePolicies", "X509v3 Certificate Policies",
     NID_certificate_policies, 3, &kObjectData[511], 0},
    {"authorityKeyIdentifier", "X509v3 Authority Key Identifier",
     NID_authority_key_identifier, 3, &kObjectData[514], 0},
    {"BF-CBC", "bf-cbc", NID_bf_cbc, 9, &kObjectData[517], 0},
    {"BF-ECB", "bf-ecb", NID_bf_ecb, 0, NULL, 0},
    {"BF-CFB", "bf-cfb", NID_bf_cfb64, 0, NULL, 0},
    {"BF-OFB", "bf-ofb", NID_bf_ofb64, 0, NULL, 0},
    {"MDC2", "mdc2", NID_mdc2, 4, &kObjectData[526], 0},
    {"RSA-MDC2", "mdc2WithRSA", NID_mdc2WithRSA, 4, &kObjectData[530], 0},
    {"RC4-40", "rc4-40", NID_rc4_40, 0, NULL, 0},
    {"RC2-40-CBC", "rc2-40-cbc", NID_rc2_40_cbc, 0, NULL, 0},
    {"GN", "givenName", NID_givenName, 3, &kObjectData[534], 0},
    {"SN", "surname", NID_surname, 3, &kObjectData[537], 0},
    {"initials", "initials", NID_initials, 3, &kObjectData[540], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"crlDistributionPoints", "X509v3 CRL Distribution Points",
     NID_crl_distribution_points, 3, &kObjectData[543], 0},
    {"RSA-NP-MD5", "md5WithRSA", NID_md5WithRSA, 5, &kObjectData[546], 0},
    {"serialNumber", "serialNumber", NID_serialNumber, 3, &kObjectData[551], 0},
    {"title", "title", NID_title, 3, &kObjectData[554], 0},
    {"description", "description", NID_description, 3, &kObjectData[557], 0},
    {"CAST5-CBC", "cast5-cbc", NID_cast5_cbc, 9, &kObjectData[560], 0},
    {"CAST5-ECB", "cast5-ecb", NID_cast5_ecb, 0, NULL, 0},
    {"CAST5-CFB", "cast5-cfb", NID_cast5_cfb64, 0, NULL, 0},
    {"CAST5-OFB", "cast5-ofb", NID_cast5_ofb64, 0, NULL, 0},
    {"pbeWithMD5AndCast5CBC", "pbeWithMD5AndCast5CBC",
     NID_pbeWithMD5AndCast5_CBC, 9, &kObjectData[569], 0},
    {"DSA-SHA1", "dsaWithSHA1", NID_dsaWithSHA1, 7, &kObjectData[578], 0},
    {"MD5-SHA1", "md5-sha1", NID_md5_sha1, 0, NULL, 0},
    {"RSA-SHA1-2", "sha1WithRSA", NID_sha1WithRSA, 5, &kObjectData[585], 0},
    {"DSA", "dsaEncryption", NID_dsa, 7, &kObjectData[590], 0},
    {"RIPEMD160", "ripemd160", NID_ripemd160, 5, &kObjectData[597], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"RSA-RIPEMD160", "ripemd160WithRSA", NID_ripemd160WithRSA, 6,
     &kObjectData[602], 0},
    {"RC5-CBC", "rc5-cbc", NID_rc5_cbc, 8, &kObjectData[608], 0},
    {"RC5-ECB", "rc5-ecb", NID_rc5_ecb, 0, NULL, 0},
    {"RC5-CFB", "rc5-cfb", NID_rc5_cfb64, 0, NULL, 0},
    {"RC5-OFB", "rc5-ofb", NID_rc5_ofb64, 0, NULL, 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"ZLIB", "zlib compression", NID_zlib_compression, 11, &kObjectData[616],
     0},
    {"extendedKeyUsage", "X509v3 Extended Key Usage", NID_ext_key_usage, 3,
     &kObjectData[627], 0},
    {"PKIX", "PKIX", NID_id_pkix, 6, &kObjectData[630], 0},
    {"id-kp", "id-kp", NID_id_kp, 7, &kObjectData[636], 0},
    {"serverAuth", "TLS Web Server Authentication", NID_server_auth, 8,
     &kObjectData[643], 0},
    {"clientAuth", "TLS Web Client Authentication", NID_client_auth, 8,
     &kObjectData[651], 0},
    {"codeSigning", "Code Signing", NID_code_sign, 8, &kObjectData[659], 0},
    {"emailProtection", "E-mail Protection", NID_email_protect, 8,
     &kObjectData[667], 0},
    {"timeStamping", "Time Stamping", NID_time_stamp, 8, &kObjectData[675], 0},
    {"msCodeInd", "Microsoft Individual Code Signing", NID_ms_code_ind, 10,
     &kObjectData[683], 0},
    {"msCodeCom", "Microsoft Commercial Code Signing", NID_ms_code_com, 10,
     &kObjectData[693], 0},
    {"msCTLSign", "Microsoft Trust List Signing", NID_ms_ctl_sign, 10,
     &kObjectData[703], 0},
    {"msSGC", "Microsoft Server Gated Crypto", NID_ms_sgc, 10,
     &kObjectData[713], 0},
    {"msEFS", "Microsoft Encrypted File System", NID_ms_efs, 10,
     &kObjectData[723], 0},
    {"nsSGC", "Netscape Server Gated Crypto", NID_ns_sgc, 9, &kObjectData[733],
     0},
    {"deltaCRL", "X509v3 Delta CRL Indicator", NID_delta_crl, 3,
     &kObjectData[742], 0},
    {"CRLReason", "X509v3 CRL Reason Code", NID_crl_reason, 3,
     &kObjectData[745], 0},
    {"invalidityDate", "Invalidity Date", NID_invalidity_date, 3,
     &kObjectData[748], 0},
    {"SXNetID", "Strong Extranet ID", NID_sxnet, 5, &kObjectData[751], 0},
    {"PBE-SHA1-RC4-128", "pbeWithSHA1And128BitRC4",
     NID_pbe_WithSHA1And128BitRC4, 10, &kObjectData[756], 0},
    {"PBE-SHA1-RC4-40", "pbeWithSHA1And40BitRC4", NID_pbe_WithSHA1And40BitRC4,
     10, &kObjectData[766], 0},
    {"PBE-SHA1-3DES", "pbeWithSHA1And3-KeyTripleDES-CBC",
     NID_pbe_WithSHA1And3_Key_TripleDES_CBC, 10, &kObjectData[776], 0},
    {"PBE-SHA1-2DES", "pbeWithSHA1And2-KeyTripleDES-CBC",
     NID_pbe_WithSHA1And2_Key_TripleDES_CBC, 10, &kObjectData[786], 0},
    {"PBE-SHA1-RC2-128", "pbeWithSHA1And128BitRC2-CBC",
     NID_pbe_WithSHA1And128BitRC2_CBC, 10, &kObjectData[796], 0},
    {"PBE-SHA1-RC2-40", "pbeWithSHA1And40BitRC2-CBC",
     NID_pbe_WithSHA1And40BitRC2_CBC, 10, &kObjectData[806], 0},
    {"keyBag", "keyBag", NID_keyBag, 11, &kObjectData[816], 0},
    {"pkcs8ShroudedKeyBag", "pkcs8ShroudedKeyBag", NID_pkcs8ShroudedKeyBag, 11,
     &kObjectData[827], 0},
    {"certBag", "certBag", NID_certBag, 11, &kObjectData[838], 0},
    {"crlBag", "crlBag", NID_crlBag, 11, &kObjectData[849], 0},
    {"secretBag", "secretBag", NID_secretBag, 11, &kObjectData[860], 0},
    {"safeContentsBag", "safeContentsBag", NID_safeContentsBag, 11,
     &kObjectData[871], 0},
    {"friendlyName", "friendlyName", NID_friendlyName, 9, &kObjectData[882], 0},
    {"localKeyID", "localKeyID", NID_localKeyID, 9, &kObjectData[891], 0},
    {"x509Certificate", "x509Certificate", NID_x509Certificate, 10,
     &kObjectData[900], 0},
    {"sdsiCertificate", "sdsiCertificate", NID_sdsiCertificate, 10,
     &kObjectData[910], 0},
    {"x509Crl", "x509Crl", NID_x509Crl, 10, &kObjectData[920], 0},
    {"PBES2", "PBES2", NID_pbes2, 9, &kObjectData[930], 0},
    {"PBMAC1", "PBMAC1", NID_pbmac1, 9, &kObjectData[939], 0},
    {"hmacWithSHA1", "hmacWithSHA1", NID_hmacWithSHA1, 8, &kObjectData[948], 0},
    {"id-qt-cps", "Policy Qualifier CPS", NID_id_qt_cps, 8, &kObjectData[956],
     0},
    {"id-qt-unotice", "Policy Qualifier User Notice", NID_id_qt_unotice, 8,
     &kObjectData[964], 0},
    {"RC2-64-CBC", "rc2-64-cbc", NID_rc2_64_cbc, 0, NULL, 0},
    {"SMIME-CAPS", "S/MIME Capabilities", NID_SMIMECapabilities, 9,
     &kObjectData[972], 0},
    {"PBE-MD2-RC2-64", "pbeWithMD2AndRC2-CBC", NID_pbeWithMD2AndRC2_CBC, 9,
     &kObjectData[981], 0},
    {"PBE-MD5-RC2-64", "pbeWithMD5AndRC2-CBC", NID_pbeWithMD5AndRC2_CBC, 9,
     &kObjectData[990], 0},
    {"PBE-SHA1-DES", "pbeWithSHA1AndDES-CBC", NID_pbeWithSHA1AndDES_CBC, 9,
     &kObjectData[999], 0},
    {"msExtReq", "Microsoft Extension Request", NID_ms_ext_req, 10,
     &kObjectData[1008], 0},
    {"extReq", "Extension Request", NID_ext_req, 9, &kObjectData[1018], 0},
    {"name", "name", NID_name, 3, &kObjectData[1027], 0},
    {"dnQualifier", "dnQualifier", NID_dnQualifier, 3, &kObjectData[1030], 0},
    {"id-pe", "id-pe", NID_id_pe, 7, &kObjectData[1033], 0},
    {"id-ad", "id-ad", NID_id_ad, 7, &kObjectData[1040], 0},
    {"authorityInfoAccess", "Authority Information Access", NID_info_access, 8,
     &kObjectData[1047], 0},
    {"OCSP", "OCSP", NID_ad_OCSP, 8, &kObjectData[1055], 0},
    {"caIssuers", "CA Issuers", NID_ad_ca_issuers, 8, &kObjectData[1063], 0},
    {"OCSPSigning", "OCSP Signing", NID_OCSP_sign, 8, &kObjectData[1071], 0},
    {"ISO", "iso", NID_iso, 0, NULL, 0},
    {"member-body", "ISO Member Body", NID_member_body, 1, &kObjectData[1079],
     0},
    {"ISO-US", "ISO US Member Body", NID_ISO_US, 3, &kObjectData[1080], 0},
    {"X9-57", "X9.57", NID_X9_57, 5, &kObjectData[1083], 0},
    {"X9cm", "X9.57 CM ?", NID_X9cm, 6, &kObjectData[1088], 0},
    {"pkcs1", "pkcs1", NID_pkcs1, 8, &kObjectData[1094], 0},
    {"pkcs5", "pkcs5", NID_pkcs5, 8, &kObjectData[1102], 0},
    {"SMIME", "S/MIME", NID_SMIME, 9, &kObjectData[1110], 0},
    {"id-smime-mod", "id-smime-mod", NID_id_smime_mod, 10, &kObjectData[1119],
     0},
    {"id-smime-ct", "id-smime-ct", NID_id_smime_ct, 10, &kObjectData[1129], 0},
    {"id-smime-aa", "id-smime-aa", NID_id_smime_aa, 10, &kObjectData[1139], 0},
    {"id-smime-alg", "id-smime-alg", NID_id_smime_alg, 10, &kObjectData[1149],
     0},
    {"id-smime-cd", "id-smime-cd", NID_id_smime_cd, 10, &kObjectData[1159], 0},
    {"id-smime-spq", "id-smime-spq", NID_id_smime_spq, 10, &kObjectData[1169],
     0},
    {"id-smime-cti", "id-smime-cti", NID_id_smime_cti, 10, &kObjectData[1179],
     0},
    {"id-smime-mod-cms", "id-smime-mod-cms", NID_id_smime_mod_cms, 11,
     &kObjectData[1189], 0},
    {"id-smime-mod-ess", "id-smime-mod-ess", NID_id_smime_mod_ess, 11,
     &kObjectData[1200], 0},
    {"id-smime-mod-oid", "id-smime-mod-oid", NID_id_smime_mod_oid, 11,
     &kObjectData[1211], 0},
    {"id-smime-mod-msg-v3", "id-smime-mod-msg-v3", NID_id_smime_mod_msg_v3, 11,
     &kObjectData[1222], 0},
    {"id-smime-mod-ets-eSignature-88", "id-smime-mod-ets-eSignature-88",
     NID_id_smime_mod_ets_eSignature_88, 11, &kObjectData[1233], 0},
    {"id-smime-mod-ets-eSignature-97", "id-smime-mod-ets-eSignature-97",
     NID_id_smime_mod_ets_eSignature_97, 11, &kObjectData[1244], 0},
    {"id-smime-mod-ets-eSigPolicy-88", "id-smime-mod-ets-eSigPolicy-88",
     NID_id_smime_mod_ets_eSigPolicy_88, 11, &kObjectData[1255], 0},
    {"id-smime-mod-ets-eSigPolicy-97", "id-smime-mod-ets-eSigPolicy-97",
     NID_id_smime_mod_ets_eSigPolicy_97, 11, &kObjectData[1266], 0},
    {"id-smime-ct-receipt", "id-smime-ct-receipt", NID_id_smime_ct_receipt, 11,
     &kObjectData[1277], 0},
    {"id-smime-ct-authData", "id-smime-ct-authData", NID_id_smime_ct_authData,
     11, &kObjectData[1288], 0},
    {"id-smime-ct-publishCert", "id-smime-ct-publishCert",
     NID_id_smime_ct_publishCert, 11, &kObjectData[1299], 0},
    {"id-smime-ct-TSTInfo", "id-smime-ct-TSTInfo", NID_id_smime_ct_TSTInfo, 11,
     &kObjectData[1310], 0},
    {"id-smime-ct-TDTInfo", "id-smime-ct-TDTInfo", NID_id_smime_ct_TDTInfo, 11,
     &kObjectData[1321], 0},
    {"id-smime-ct-contentInfo", "id-smime-ct-contentInfo",
     NID_id_smime_ct_contentInfo, 11, &kObjectData[1332], 0},
    {"id-smime-ct-DVCSRequestData", "id-smime-ct-DVCSRequestData",
     NID_id_smime_ct_DVCSRequestData, 11, &kObjectData[1343], 0},
    {"id-smime-ct-DVCSResponseData", "id-smime-ct-DVCSResponseData",
     NID_id_smime_ct_DVCSResponseData, 11, &kObjectData[1354], 0},
    {"id-smime-aa-receiptRequest", "id-smime-aa-receiptRequest",
     NID_id_smime_aa_receiptRequest, 11, &kObjectData[1365], 0},
    {"id-smime-aa-securityLabel", "id-smime-aa-securityLabel",
     NID_id_smime_aa_securityLabel, 11, &kObjectData[1376], 0},
    {"id-smime-aa-mlExpandHistory", "id-smime-aa-mlExpandHistory",
     NID_id_smime_aa_mlExpandHistory, 11, &kObjectData[1387], 0},
    {"id-smime-aa-contentHint", "id-smime-aa-contentHint",
     NID_id_smime_aa_contentHint, 11, &kObjectData[1398], 0},
    {"id-smime-aa-msgSigDigest", "id-smime-aa-msgSigDigest",
     NID_id_smime_aa_msgSigDigest, 11, &kObjectData[1409], 0},
    {"id-smime-aa-encapContentType", "id-smime-aa-encapContentType",
     NID_id_smime_aa_encapContentType, 11, &kObjectData[1420], 0},
    {"id-smime-aa-contentIdentifier", "id-smime-aa-contentIdentifier",
     NID_id_smime_aa_contentIdentifier, 11, &kObjectData[1431], 0},
    {"id-smime-aa-macValue", "id-smime-aa-macValue", NID_id_smime_aa_macValue,
     11, &kObjectData[1442], 0},
    {"id-smime-aa-equivalentLabels", "id-smime-aa-equivalentLabels",
     NID_id_smime_aa_equivalentLabels, 11, &kObjectData[1453], 0},
    {"id-smime-aa-contentReference", "id-smime-aa-contentReference",
     NID_id_smime_aa_contentReference, 11, &kObjectData[1464], 0},
    {"id-smime-aa-encrypKeyPref", "id-smime-aa-encrypKeyPref",
     NID_id_smime_aa_encrypKeyPref, 11, &kObjectData[1475], 0},
    {"id-smime-aa-signingCertificate", "id-smime-aa-signingCertificate",
     NID_id_smime_aa_signingCertificate, 11, &kObjectData[1486], 0},
    {"id-smime-aa-smimeEncryptCerts", "id-smime-aa-smimeEncryptCerts",
     NID_id_smime_aa_smimeEncryptCerts, 11, &kObjectData[1497], 0},
    {"id-smime-aa-timeStampToken", "id-smime-aa-timeStampToken",
     NID_id_smime_aa_timeStampToken, 11, &kObjectData[1508], 0},
    {"id-smime-aa-ets-sigPolicyId", "id-smime-aa-ets-sigPolicyId",
     NID_id_smime_aa_ets_sigPolicyId, 11, &kObjectData[1519], 0},
    {"id-smime-aa-ets-commitmentType", "id-smime-aa-ets-commitmentType",
     NID_id_smime_aa_ets_commitmentType, 11, &kObjectData[1530], 0},
    {"id-smime-aa-ets-signerLocation", "id-smime-aa-ets-signerLocation",
     NID_id_smime_aa_ets_signerLocation, 11, &kObjectData[1541], 0},
    {"id-smime-aa-ets-signerAttr", "id-smime-aa-ets-signerAttr",
     NID_id_smime_aa_ets_signerAttr, 11, &kObjectData[1552], 0},
    {"id-smime-aa-ets-otherSigCert", "id-smime-aa-ets-otherSigCert",
     NID_id_smime_aa_ets_otherSigCert, 11, &kObjectData[1563], 0},
    {"id-smime-aa-ets-contentTimestamp", "id-smime-aa-ets-contentTimestamp",
     NID_id_smime_aa_ets_contentTimestamp, 11, &kObjectData[1574], 0},
    {"id-smime-aa-ets-CertificateRefs", "id-smime-aa-ets-CertificateRefs",
     NID_id_smime_aa_ets_CertificateRefs, 11, &kObjectData[1585], 0},
    {"id-smime-aa-ets-RevocationRefs", "id-smime-aa-ets-RevocationRefs",
     NID_id_smime_aa_ets_RevocationRefs, 11, &kObjectData[1596], 0},
    {"id-smime-aa-ets-certValues", "id-smime-aa-ets-certValues",
     NID_id_smime_aa_ets_certValues, 11, &kObjectData[1607], 0},
    {"id-smime-aa-ets-revocationValues", "id-smime-aa-ets-revocationValues",
     NID_id_smime_aa_ets_revocationValues, 11, &kObjectData[1618], 0},
    {"id-smime-aa-ets-escTimeStamp", "id-smime-aa-ets-escTimeStamp",
     NID_id_smime_aa_ets_escTimeStamp, 11, &kObjectData[1629], 0},
    {"id-smime-aa-ets-certCRLTimestamp", "id-smime-aa-ets-certCRLTimestamp",
     NID_id_smime_aa_ets_certCRLTimestamp, 11, &kObjectData[1640], 0},
    {"id-smime-aa-ets-archiveTimeStamp", "id-smime-aa-ets-archiveTimeStamp",
     NID_id_smime_aa_ets_archiveTimeStamp, 11, &kObjectData[1651], 0},
    {"id-smime-aa-signatureType", "id-smime-aa-signatureType",
     NID_id_smime_aa_signatureType, 11, &kObjectData[1662], 0},
    {"id-smime-aa-dvcs-dvc", "id-smime-aa-dvcs-dvc", NID_id_smime_aa_dvcs_dvc,
     11, &kObjectData[1673], 0},
    {"id-smime-alg-ESDHwith3DES", "id-smime-alg-ESDHwith3DES",
     NID_id_smime_alg_ESDHwith3DES, 11, &kObjectData[1684], 0},
    {"id-smime-alg-ESDHwithRC2", "id-smime-alg-ESDHwithRC2",
     NID_id_smime_alg_ESDHwithRC2, 11, &kObjectData[1695], 0},
    {"id-smime-alg-3DESwrap", "id-smime-alg-3DESwrap",
     NID_id_smime_alg_3DESwrap, 11, &kObjectData[1706], 0},
    {"id-smime-alg-RC2wrap", "id-smime-alg-RC2wrap", NID_id_smime_alg_RC2wrap,
     11, &kObjectData[1717], 0},
    {"id-smime-alg-ESDH", "id-smime-alg-ESDH", NID_id_smime_alg_ESDH, 11,
     &kObjectData[1728], 0},
    {"id-smime-alg-CMS3DESwrap", "id-smime-alg-CMS3DESwrap",
     NID_id_smime_alg_CMS3DESwrap, 11, &kObjectData[1739], 0},
    {"id-smime-alg-CMSRC2wrap", "id-smime-alg-CMSRC2wrap",
     NID_id_smime_alg_CMSRC2wrap, 11, &kObjectData[1750], 0},
    {"id-smime-cd-ldap", "id-smime-cd-ldap", NID_id_smime_cd_ldap, 11,
     &kObjectData[1761], 0},
    {"id-smime-spq-ets-sqt-uri", "id-smime-spq-ets-sqt-uri",
     NID_id_smime_spq_ets_sqt_uri, 11, &kObjectData[1772], 0},
    {"id-smime-spq-ets-sqt-unotice", "id-smime-spq-ets-sqt-unotice",
     NID_id_smime_spq_ets_sqt_unotice, 11, &kObjectData[1783], 0},
    {"id-smime-cti-ets-proofOfOrigin", "id-smime-cti-ets-proofOfOrigin",
     NID_id_smime_cti_ets_proofOfOrigin, 11, &kObjectData[1794], 0},
    {"id-smime-cti-ets-proofOfReceipt", "id-smime-cti-ets-proofOfReceipt",
     NID_id_smime_cti_ets_proofOfReceipt, 11, &kObjectData[1805], 0},
    {"id-smime-cti-ets-proofOfDelivery", "id-smime-cti-ets-proofOfDelivery",
     NID_id_smime_cti_ets_proofOfDelivery, 11, &kObjectData[1816], 0},
    {"id-smime-cti-ets-proofOfSender", "id-smime-cti-ets-proofOfSender",
     NID_id_smime_cti_ets_proofOfSender, 11, &kObjectData[1827], 0},
    {"id-smime-cti-ets-proofOfApproval", "id-smime-cti-ets-proofOfApproval",
     NID_id_smime_cti_ets_proofOfApproval, 11, &kObjectData[1838], 0},
    {"id-smime-cti-ets-proofOfCreation", "id-smime-cti-ets-proofOfCreation",
     NID_id_smime_cti_ets_proofOfCreation, 11, &kObjectData[1849], 0},
    {"MD4", "md4", NID_md4, 8, &kObjectData[1860], 0},
    {"id-pkix-mod", "id-pkix-mod", NID_id_pkix_mod, 7, &kObjectData[1868], 0},
    {"id-qt", "id-qt", NID_id_qt, 7, &kObjectData[1875], 0},
    {"id-it", "id-it", NID_id_it, 7, &kObjectData[1882], 0},
    {"id-pkip", "id-pkip", NID_id_pkip, 7, &kObjectData[1889], 0},
    {"id-alg", "id-alg", NID_id_alg, 7, &kObjectData[1896], 0},
    {"id-cmc", "id-cmc", NID_id_cmc, 7, &kObjectData[1903], 0},
    {"id-on", "id-on", NID_id_on, 7, &kObjectData[1910], 0},
    {"id-pda", "id-pda", NID_id_pda, 7, &kObjectData[1917], 0},
    {"id-aca", "id-aca", NID_id_aca, 7, &kObjectData[1924], 0},
    {"id-qcs", "id-qcs", NID_id_qcs, 7, &kObjectData[1931], 0},
    {"id-cct", "id-cct", NID_id_cct, 7, &kObjectData[1938], 0},
    {"id-pkix1-explicit-88", "id-pkix1-explicit-88", NID_id_pkix1_explicit_88,
     8, &kObjectData[1945], 0},
    {"id-pkix1-implicit-88", "id-pkix1-implicit-88", NID_id_pkix1_implicit_88,
     8, &kObjectData[1953], 0},
    {"id-pkix1-explicit-93", "id-pkix1-explicit-93", NID_id_pkix1_explicit_93,
     8, &kObjectData[1961], 0},
    {"id-pkix1-implicit-93", "id-pkix1-implicit-93", NID_id_pkix1_implicit_93,
     8, &kObjectData[1969], 0},
    {"id-mod-crmf", "id-mod-crmf", NID_id_mod_crmf, 8, &kObjectData[1977], 0},
    {"id-mod-cmc", "id-mod-cmc", NID_id_mod_cmc, 8, &kObjectData[1985], 0},
    {"id-mod-kea-profile-88", "id-mod-kea-profile-88",
     NID_id_mod_kea_profile_88, 8, &kObjectData[1993], 0},
    {"id-mod-kea-profile-93", "id-mod-kea-profile-93",
     NID_id_mod_kea_profile_93, 8, &kObjectData[2001], 0},
    {"id-mod-cmp", "id-mod-cmp", NID_id_mod_cmp, 8, &kObjectData[2009], 0},
    {"id-mod-qualified-cert-88", "id-mod-qualified-cert-88",
     NID_id_mod_qualified_cert_88, 8, &kObjectData[2017], 0},
    {"id-mod-qualified-cert-93", "id-mod-qualified-cert-93",
     NID_id_mod_qualified_cert_93, 8, &kObjectData[2025], 0},
    {"id-mod-attribute-cert", "id-mod-attribute-cert",
     NID_id_mod_attribute_cert, 8, &kObjectData[2033], 0},
    {"id-mod-timestamp-protocol", "id-mod-timestamp-protocol",
     NID_id_mod_timestamp_protocol, 8, &kObjectData[2041], 0},
    {"id-mod-ocsp", "id-mod-ocsp", NID_id_mod_ocsp, 8, &kObjectData[2049], 0},
    {"id-mod-dvcs", "id-mod-dvcs", NID_id_mod_dvcs, 8, &kObjectData[2057], 0},
    {"id-mod-cmp2000", "id-mod-cmp2000", NID_id_mod_cmp2000, 8,
     &kObjectData[2065], 0},
    {"biometricInfo", "Biometric Info", NID_biometricInfo, 8,
     &kObjectData[2073], 0},
    {"qcStatements", "qcStatements", NID_qcStatements, 8, &kObjectData[2081],
     0},
    {"ac-auditEntity", "ac-auditEntity", NID_ac_auditEntity, 8,
     &kObjectData[2089], 0},
    {"ac-targeting", "ac-targeting", NID_ac_targeting, 8, &kObjectData[2097],
     0},
    {"aaControls", "aaControls", NID_aaControls, 8, &kObjectData[2105], 0},
    {"sbgp-ipAddrBlock", "sbgp-ipAddrBlock", NID_sbgp_ipAddrBlock, 8,
     &kObjectData[2113], 0},
    {"sbgp-autonomousSysNum", "sbgp-autonomousSysNum",
     NID_sbgp_autonomousSysNum, 8, &kObjectData[2121], 0},
    {"sbgp-routerIdentifier", "sbgp-routerIdentifier",
     NID_sbgp_routerIdentifier, 8, &kObjectData[2129], 0},
    {"textNotice", "textNotice", NID_textNotice, 8, &kObjectData[2137], 0},
    {"ipsecEndSystem", "IPSec End System", NID_ipsecEndSystem, 8,
     &kObjectData[2145], 0},
    {"ipsecTunnel", "IPSec Tunnel", NID_ipsecTunnel, 8, &kObjectData[2153], 0},
    {"ipsecUser", "IPSec User", NID_ipsecUser, 8, &kObjectData[2161], 0},
    {"DVCS", "dvcs", NID_dvcs, 8, &kObjectData[2169], 0},
    {"id-it-caProtEncCert", "id-it-caProtEncCert", NID_id_it_caProtEncCert, 8,
     &kObjectData[2177], 0},
    {"id-it-signKeyPairTypes", "id-it-signKeyPairTypes",
     NID_id_it_signKeyPairTypes, 8, &kObjectData[2185], 0},
    {"id-it-encKeyPairTypes", "id-it-encKeyPairTypes",
     NID_id_it_encKeyPairTypes, 8, &kObjectData[2193], 0},
    {"id-it-preferredSymmAlg", "id-it-preferredSymmAlg",
     NID_id_it_preferredSymmAlg, 8, &kObjectData[2201], 0},
    {"id-it-caKeyUpdateInfo", "id-it-caKeyUpdateInfo",
     NID_id_it_caKeyUpdateInfo, 8, &kObjectData[2209], 0},
    {"id-it-currentCRL", "id-it-currentCRL", NID_id_it_currentCRL, 8,
     &kObjectData[2217], 0},
    {"id-it-unsupportedOIDs", "id-it-unsupportedOIDs",
     NID_id_it_unsupportedOIDs, 8, &kObjectData[2225], 0},
    {"id-it-subscriptionRequest", "id-it-subscriptionRequest",
     NID_id_it_subscriptionRequest, 8, &kObjectData[2233], 0},
    {"id-it-subscriptionResponse", "id-it-subscriptionResponse",
     NID_id_it_subscriptionResponse, 8, &kObjectData[2241], 0},
    {"id-it-keyPairParamReq", "id-it-keyPairParamReq",
     NID_id_it_keyPairParamReq, 8, &kObjectData[2249], 0},
    {"id-it-keyPairParamRep", "id-it-keyPairParamRep",
     NID_id_it_keyPairParamRep, 8, &kObjectData[2257], 0},
    {"id-it-revPassphrase", "id-it-revPassphrase", NID_id_it_revPassphrase, 8,
     &kObjectData[2265], 0},
    {"id-it-implicitConfirm", "id-it-implicitConfirm",
     NID_id_it_implicitConfirm, 8, &kObjectData[2273], 0},
    {"id-it-confirmWaitTime", "id-it-confirmWaitTime",
     NID_id_it_confirmWaitTime, 8, &kObjectData[2281], 0},
    {"id-it-origPKIMessage", "id-it-origPKIMessage", NID_id_it_origPKIMessage,
     8, &kObjectData[2289], 0},
    {"id-regCtrl", "id-regCtrl", NID_id_regCtrl, 8, &kObjectData[2297], 0},
    {"id-regInfo", "id-regInfo", NID_id_regInfo, 8, &kObjectData[2305], 0},
    {"id-regCtrl-regToken", "id-regCtrl-regToken", NID_id_regCtrl_regToken, 9,
     &kObjectData[2313], 0},
    {"id-regCtrl-authenticator", "id-regCtrl-authenticator",
     NID_id_regCtrl_authenticator, 9, &kObjectData[2322], 0},
    {"id-regCtrl-pkiPublicationInfo", "id-regCtrl-pkiPublicationInfo",
     NID_id_regCtrl_pkiPublicationInfo, 9, &kObjectData[2331], 0},
    {"id-regCtrl-pkiArchiveOptions", "id-regCtrl-pkiArchiveOptions",
     NID_id_regCtrl_pkiArchiveOptions, 9, &kObjectData[2340], 0},
    {"id-regCtrl-oldCertID", "id-regCtrl-oldCertID", NID_id_regCtrl_oldCertID,
     9, &kObjectData[2349], 0},
    {"id-regCtrl-protocolEncrKey", "id-regCtrl-protocolEncrKey",
     NID_id_regCtrl_protocolEncrKey, 9, &kObjectData[2358], 0},
    {"id-regInfo-utf8Pairs", "id-regInfo-utf8Pairs", NID_id_regInfo_utf8Pairs,
     9, &kObjectData[2367], 0},
    {"id-regInfo-certReq", "id-regInfo-certReq", NID_id_regInfo_certReq, 9,
     &kObjectData[2376], 0},
    {"id-alg-des40", "id-alg-des40", NID_id_alg_des40, 8, &kObjectData[2385],
     0},
    {"id-alg-noSignature", "id-alg-noSignature", NID_id_alg_noSignature, 8,
     &kObjectData[2393], 0},
    {"id-alg-dh-sig-hmac-sha1", "id-alg-dh-sig-hmac-sha1",
     NID_id_alg_dh_sig_hmac_sha1, 8, &kObjectData[2401], 0},
    {"id-alg-dh-pop", "id-alg-dh-pop", NID_id_alg_dh_pop, 8, &kObjectData[2409],
     0},
    {"id-cmc-statusInfo", "id-cmc-statusInfo", NID_id_cmc_statusInfo, 8,
     &kObjectData[2417], 0},
    {"id-cmc-identification", "id-cmc-identification",
     NID_id_cmc_identification, 8, &kObjectData[2425], 0},
    {"id-cmc-identityProof", "id-cmc-identityProof", NID_id_cmc_identityProof,
     8, &kObjectData[2433], 0},
    {"id-cmc-dataReturn", "id-cmc-dataReturn", NID_id_cmc_dataReturn, 8,
     &kObjectData[2441], 0},
    {"id-cmc-transactionId", "id-cmc-transactionId", NID_id_cmc_transactionId,
     8, &kObjectData[2449], 0},
    {"id-cmc-senderNonce", "id-cmc-senderNonce", NID_id_cmc_senderNonce, 8,
     &kObjectData[2457], 0},
    {"id-cmc-recipientNonce", "id-cmc-recipientNonce",
     NID_id_cmc_recipientNonce, 8, &kObjectData[2465], 0},
    {"id-cmc-addExtensions", "id-cmc-addExtensions", NID_id_cmc_addExtensions,
     8, &kObjectData[2473], 0},
    {"id-cmc-encryptedPOP", "id-cmc-encryptedPOP", NID_id_cmc_encryptedPOP, 8,
     &kObjectData[2481], 0},
    {"id-cmc-decryptedPOP", "id-cmc-decryptedPOP", NID_id_cmc_decryptedPOP, 8,
     &kObjectData[2489], 0},
    {"id-cmc-lraPOPWitness", "id-cmc-lraPOPWitness", NID_id_cmc_lraPOPWitness,
     8, &kObjectData[2497], 0},
    {"id-cmc-getCert", "id-cmc-getCert", NID_id_cmc_getCert, 8,
     &kObjectData[2505], 0},
    {"id-cmc-getCRL", "id-cmc-getCRL", NID_id_cmc_getCRL, 8, &kObjectData[2513],
     0},
    {"id-cmc-revokeRequest", "id-cmc-revokeRequest", NID_id_cmc_revokeRequest,
     8, &kObjectData[2521], 0},
    {"id-cmc-regInfo", "id-cmc-regInfo", NID_id_cmc_regInfo, 8,
     &kObjectData[2529], 0},
    {"id-cmc-responseInfo", "id-cmc-responseInfo", NID_id_cmc_responseInfo, 8,
     &kObjectData[2537], 0},
    {"id-cmc-queryPending", "id-cmc-queryPending", NID_id_cmc_queryPending, 8,
     &kObjectData[2545], 0},
    {"id-cmc-popLinkRandom", "id-cmc-popLinkRandom", NID_id_cmc_popLinkRandom,
     8, &kObjectData[2553], 0},
    {"id-cmc-popLinkWitness", "id-cmc-popLinkWitness",
     NID_id_cmc_popLinkWitness, 8, &kObjectData[2561], 0},
    {"id-cmc-confirmCertAcceptance", "id-cmc-confirmCertAcceptance",
     NID_id_cmc_confirmCertAcceptance, 8, &kObjectData[2569], 0},
    {"id-on-personalData", "id-on-personalData", NID_id_on_personalData, 8,
     &kObjectData[2577], 0},
    {"id-pda-dateOfBirth", "id-pda-dateOfBirth", NID_id_pda_dateOfBirth, 8,
     &kObjectData[2585], 0},
    {"id-pda-placeOfBirth", "id-pda-placeOfBirth", NID_id_pda_placeOfBirth, 8,
     &kObjectData[2593], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"id-pda-gender", "id-pda-gender", NID_id_pda_gender, 8, &kObjectData[2601],
     0},
    {"id-pda-countryOfCitizenship", "id-pda-countryOfCitizenship",
     NID_id_pda_countryOfCitizenship, 8, &kObjectData[2609], 0},
    {"id-pda-countryOfResidence", "id-pda-countryOfResidence",
     NID_id_pda_countryOfResidence, 8, &kObjectData[2617], 0},
    {"id-aca-authenticationInfo", "id-aca-authenticationInfo",
     NID_id_aca_authenticationInfo, 8, &kObjectData[2625], 0},
    {"id-aca-accessIdentity", "id-aca-accessIdentity",
     NID_id_aca_accessIdentity, 8, &kObjectData[2633], 0},
    {"id-aca-chargingIdentity", "id-aca-chargingIdentity",
     NID_id_aca_chargingIdentity, 8, &kObjectData[2641], 0},
    {"id-aca-group", "id-aca-group", NID_id_aca_group, 8, &kObjectData[2649],
     0},
    {"id-aca-role", "id-aca-role", NID_id_aca_role, 8, &kObjectData[2657], 0},
    {"id-qcs-pkixQCSyntax-v1", "id-qcs-pkixQCSyntax-v1",
     NID_id_qcs_pkixQCSyntax_v1, 8, &kObjectData[2665], 0},
    {"id-cct-crs", "id-cct-crs", NID_id_cct_crs, 8, &kObjectData[2673], 0},
    {"id-cct-PKIData", "id-cct-PKIData", NID_id_cct_PKIData, 8,
     &kObjectData[2681], 0},
    {"id-cct-PKIResponse", "id-cct-PKIResponse", NID_id_cct_PKIResponse, 8,
     &kObjectData[2689], 0},
    {"ad_timestamping", "AD Time Stamping", NID_ad_timeStamping, 8,
     &kObjectData[2697], 0},
    {"AD_DVCS", "ad dvcs", NID_ad_dvcs, 8, &kObjectData[2705], 0},
    {"basicOCSPResponse", "Basic OCSP Response", NID_id_pkix_OCSP_basic, 9,
     &kObjectData[2713], 0},
    {"Nonce", "OCSP Nonce", NID_id_pkix_OCSP_Nonce, 9, &kObjectData[2722], 0},
    {"CrlID", "OCSP CRL ID", NID_id_pkix_OCSP_CrlID, 9, &kObjectData[2731], 0},
    {"acceptableResponses", "Acceptable OCSP Responses",
     NID_id_pkix_OCSP_acceptableResponses, 9, &kObjectData[2740], 0},
    {"noCheck", "OCSP No Check", NID_id_pkix_OCSP_noCheck, 9,
     &kObjectData[2749], 0},
    {"archiveCutoff", "OCSP Archive Cutoff", NID_id_pkix_OCSP_archiveCutoff, 9,
     &kObjectData[2758], 0},
    {"serviceLocator", "OCSP Service Locator", NID_id_pkix_OCSP_serviceLocator,
     9, &kObjectData[2767], 0},
    {"extendedStatus", "Extended OCSP Status", NID_id_pkix_OCSP_extendedStatus,
     9, &kObjectData[2776], 0},
    {"valid", "valid", NID_id_pkix_OCSP_valid, 9, &kObjectData[2785], 0},
    {"path", "path", NID_id_pkix_OCSP_path, 9, &kObjectData[2794], 0},
    {"trustRoot", "Trust Root", NID_id_pkix_OCSP_trustRoot, 9,
     &kObjectData[2803], 0},
    {"algorithm", "algorithm", NID_algorithm, 4, &kObjectData[2812], 0},
    {"rsaSignature", "rsaSignature", NID_rsaSignature, 5, &kObjectData[2816],
     0},
    {"X500algorithms", "directory services - algorithms", NID_X500algorithms, 2,
     &kObjectData[2821], 0},
    {"ORG", "org", NID_org, 1, &kObjectData[2823], 0},
    {"DOD", "dod", NID_dod, 2, &kObjectData[2824], 0},
    {"IANA", "iana", NID_iana, 3, &kObjectData[2826], 0},
    {"directory", "Directory", NID_Directory, 4, &kObjectData[2829], 0},
    {"mgmt", "Management", NID_Management, 4, &kObjectData[2833], 0},
    {"experimental", "Experimental", NID_Experimental, 4, &kObjectData[2837],
     0},
    {"private", "Private", NID_Private, 4, &kObjectData[2841], 0},
    {"security", "Security", NID_Security, 4, &kObjectData[2845], 0},
    {"snmpv2", "SNMPv2", NID_SNMPv2, 4, &kObjectData[2849], 0},
    {"Mail", "Mail", NID_Mail, 4, &kObjectData[2853], 0},
    {"enterprises", "Enterprises", NID_Enterprises, 5, &kObjectData[2857], 0},
    {"dcobject", "dcObject", NID_dcObject, 9, &kObjectData[2862], 0},
    {"DC", "domainComponent", NID_domainComponent, 10, &kObjectData[2871], 0},
    {"domain", "Domain", NID_Domain, 10, &kObjectData[2881], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"selected-attribute-types", "Selected Attribute Types",
     NID_selected_attribute_types, 3, &kObjectData[2891], 0},
    {"clearance", "clearance", NID_clearance, 4, &kObjectData[2894], 0},
    {"RSA-MD4", "md4WithRSAEncryption", NID_md4WithRSAEncryption, 9,
     &kObjectData[2898], 0},
    {"ac-proxying", "ac-proxying", NID_ac_proxying, 8, &kObjectData[2907], 0},
    {"subjectInfoAccess", "Subject Information Access", NID_sinfo_access, 8,
     &kObjectData[2915], 0},
    {"id-aca-encAttrs", "id-aca-encAttrs", NID_id_aca_encAttrs, 8,
     &kObjectData[2923], 0},
    {"role", "role", NID_role, 3, &kObjectData[2931], 0},
    {"policyConstraints", "X509v3 Policy Constraints", NID_policy_constraints,
     3, &kObjectData[2934], 0},
    {"targetInformation", "X509v3 AC Targeting", NID_target_information, 3,
     &kObjectData[2937], 0},
    {"noRevAvail", "X509v3 No Revocation Available", NID_no_rev_avail, 3,
     &kObjectData[2940], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"ansi-X9-62", "ANSI X9.62", NID_ansi_X9_62, 5, &kObjectData[2943], 0},
    {"prime-field", "prime-field", NID_X9_62_prime_field, 7, &kObjectData[2948],
     0},
    {"characteristic-two-field", "characteristic-two-field",
     NID_X9_62_characteristic_two_field, 7, &kObjectData[2955], 0},
    {"id-ecPublicKey", "id-ecPublicKey", NID_X9_62_id_ecPublicKey, 7,
     &kObjectData[2962], 0},
    {"prime192v1", "prime192v1", NID_X9_62_prime192v1, 8, &kObjectData[2969],
     0},
    {"prime192v2", "prime192v2", NID_X9_62_prime192v2, 8, &kObjectData[2977],
     0},
    {"prime192v3", "prime192v3", NID_X9_62_prime192v3, 8, &kObjectData[2985],
     0},
    {"prime239v1", "prime239v1", NID_X9_62_prime239v1, 8, &kObjectData[2993],
     0},
    {"prime239v2", "prime239v2", NID_X9_62_prime239v2, 8, &kObjectData[3001],
     0},
    {"prime239v3", "prime239v3", NID_X9_62_prime239v3, 8, &kObjectData[3009],
     0},
    {"prime256v1", "prime256v1", NID_X9_62_prime256v1, 8, &kObjectData[3017],
     0},
    {"ecdsa-with-SHA1", "ecdsa-with-SHA1", NID_ecdsa_with_SHA1, 7,
     &kObjectData[3025], 0},
    {"CSPName", "Microsoft CSP Name", NID_ms_csp_name, 9, &kObjectData[3032],
     0},
    {"AES-128-ECB", "aes-128-ecb", NID_aes_128_ecb, 9, &kObjectData[3041], 0},
    {"AES-128-CBC", "aes-128-cbc", NID_aes_128_cbc, 9, &kObjectData[3050], 0},
    {"AES-128-OFB", "aes-128-ofb", NID_aes_128_ofb128, 9, &kObjectData[3059],
     0},
    {"AES-128-CFB", "aes-128-cfb", NID_aes_128_cfb128, 9, &kObjectData[3068],
     0},
    {"AES-192-ECB", "aes-192-ecb", NID_aes_192_ecb, 9, &kObjectData[3077], 0},
    {"AES-192-CBC", "aes-192-cbc", NID_aes_192_cbc, 9, &kObjectData[3086], 0},
    {"AES-192-OFB", "aes-192-ofb", NID_aes_192_ofb128, 9, &kObjectData[3095],
     0},
    {"AES-192-CFB", "aes-192-cfb", NID_aes_192_cfb128, 9, &kObjectData[3104],
     0},
    {"AES-256-ECB", "aes-256-ecb", NID_aes_256_ecb, 9, &kObjectData[3113], 0},
    {"AES-256-CBC", "aes-256-cbc", NID_aes_256_cbc, 9, &kObjectData[3122], 0},
    {"AES-256-OFB", "aes-256-ofb", NID_aes_256_ofb128, 9, &kObjectData[3131],
     0},
    {"AES-256-CFB", "aes-256-cfb", NID_aes_256_cfb128, 9, &kObjectData[3140],
     0},
    {"holdInstructionCode", "Hold Instruction Code", NID_hold_instruction_code,
     3, &kObjectData[3149], 0},
    {"holdInstructionNone", "Hold Instruction None", NID_hold_instruction_none,
     7, &kObjectData[3152], 0},
    {"holdInstructionCallIssuer", "Hold Instruction Call Issuer",
     NID_hold_instruction_call_issuer, 7, &kObjectData[3159], 0},
    {"holdInstructionReject", "Hold Instruction Reject",
     NID_hold_instruction_reject, 7, &kObjectData[3166], 0},
    {"data", "data", NID_data, 1, &kObjectData[3173], 0},
    {"pss", "pss", NID_pss, 3, &kObjectData[3174], 0},
    {"ucl", "ucl", NID_ucl, 7, &kObjectData[3177], 0},
    {"pilot", "pilot", NID_pilot, 8, &kObjectData[3184], 0},
    {"pilotAttributeType", "pilotAttributeType", NID_pilotAttributeType, 9,
     &kObjectData[3192], 0},
    {"pilotAttributeSyntax", "pilotAttributeSyntax", NID_pilotAttributeSyntax,
     9, &kObjectData[3201], 0},
    {"pilotObjectClass", "pilotObjectClass", NID_pilotObjectClass, 9,
     &kObjectData[3210], 0},
    {"pilotGroups", "pilotGroups", NID_pilotGroups, 9, &kObjectData[3219], 0},
    {"iA5StringSyntax", "iA5StringSyntax", NID_iA5StringSyntax, 10,
     &kObjectData[3228], 0},
    {"caseIgnoreIA5StringSyntax", "caseIgnoreIA5StringSyntax",
     NID_caseIgnoreIA5StringSyntax, 10, &kObjectData[3238], 0},
    {"pilotObject", "pilotObject", NID_pilotObject, 10, &kObjectData[3248], 0},
    {"pilotPerson", "pilotPerson", NID_pilotPerson, 10, &kObjectData[3258], 0},
    {"account", "account", NID_account, 10, &kObjectData[3268], 0},
    {"document", "document", NID_document, 10, &kObjectData[3278], 0},
    {"room", "room", NID_room, 10, &kObjectData[3288], 0},
    {"documentSeries", "documentSeries", NID_documentSeries, 10,
     &kObjectData[3298], 0},
    {"rFC822localPart", "rFC822localPart", NID_rFC822localPart, 10,
     &kObjectData[3308], 0},
    {"dNSDomain", "dNSDomain", NID_dNSDomain, 10, &kObjectData[3318], 0},
    {"domainRelatedObject", "domainRelatedObject", NID_domainRelatedObject, 10,
     &kObjectData[3328], 0},
    {"friendlyCountry", "friendlyCountry", NID_friendlyCountry, 10,
     &kObjectData[3338], 0},
    {"simpleSecurityObject", "simpleSecurityObject", NID_simpleSecurityObject,
     10, &kObjectData[3348], 0},
    {"pilotOrganization", "pilotOrganization", NID_pilotOrganization, 10,
     &kObjectData[3358], 0},
    {"pilotDSA", "pilotDSA", NID_pilotDSA, 10, &kObjectData[3368], 0},
    {"qualityLabelledData", "qualityLabelledData", NID_qualityLabelledData, 10,
     &kObjectData[3378], 0},
    {"UID", "userId", NID_userId, 10, &kObjectData[3388], 0},
    {"textEncodedORAddress", "textEncodedORAddress", NID_textEncodedORAddress,
     10, &kObjectData[3398], 0},
    {"mail", "rfc822Mailbox", NID_rfc822Mailbox, 10, &kObjectData[3408], 0},
    {"info", "info", NID_info, 10, &kObjectData[3418], 0},
    {"favouriteDrink", "favouriteDrink", NID_favouriteDrink, 10,
     &kObjectData[3428], 0},
    {"roomNumber", "roomNumber", NID_roomNumber, 10, &kObjectData[3438], 0},
    {"photo", "photo", NID_photo, 10, &kObjectData[3448], 0},
    {"userClass", "userClass", NID_userClass, 10, &kObjectData[3458], 0},
    {"host", "host", NID_host, 10, &kObjectData[3468], 0},
    {"manager", "manager", NID_manager, 10, &kObjectData[3478], 0},
    {"documentIdentifier", "documentIdentifier", NID_documentIdentifier, 10,
     &kObjectData[3488], 0},
    {"documentTitle", "documentTitle", NID_documentTitle, 10,
     &kObjectData[3498], 0},
    {"documentVersion", "documentVersion", NID_documentVersion, 10,
     &kObjectData[3508], 0},
    {"documentAuthor", "documentAuthor", NID_documentAuthor, 10,
     &kObjectData[3518], 0},
    {"documentLocation", "documentLocation", NID_documentLocation, 10,
     &kObjectData[3528], 0},
    {"homeTelephoneNumber", "homeTelephoneNumber", NID_homeTelephoneNumber, 10,
     &kObjectData[3538], 0},
    {"secretary", "secretary", NID_secretary, 10, &kObjectData[3548], 0},
    {"otherMailbox", "otherMailbox", NID_otherMailbox, 10, &kObjectData[3558],
     0},
    {"lastModifiedTime", "lastModifiedTime", NID_lastModifiedTime, 10,
     &kObjectData[3568], 0},
    {"lastModifiedBy", "lastModifiedBy", NID_lastModifiedBy, 10,
     &kObjectData[3578], 0},
    {"aRecord", "aRecord", NID_aRecord, 10, &kObjectData[3588], 0},
    {"pilotAttributeType27", "pilotAttributeType27", NID_pilotAttributeType27,
     10, &kObjectData[3598], 0},
    {"mXRecord", "mXRecord", NID_mXRecord, 10, &kObjectData[3608], 0},
    {"nSRecord", "nSRecord", NID_nSRecord, 10, &kObjectData[3618], 0},
    {"sOARecord", "sOARecord", NID_sOARecord, 10, &kObjectData[3628], 0},
    {"cNAMERecord", "cNAMERecord", NID_cNAMERecord, 10, &kObjectData[3638], 0},
    {"associatedDomain", "associatedDomain", NID_associatedDomain, 10,
     &kObjectData[3648], 0},
    {"associatedName", "associatedName", NID_associatedName, 10,
     &kObjectData[3658], 0},
    {"homePostalAddress", "homePostalAddress", NID_homePostalAddress, 10,
     &kObjectData[3668], 0},
    {"personalTitle", "personalTitle", NID_personalTitle, 10,
     &kObjectData[3678], 0},
    {"mobileTelephoneNumber", "mobileTelephoneNumber",
     NID_mobileTelephoneNumber, 10, &kObjectData[3688], 0},
    {"pagerTelephoneNumber", "pagerTelephoneNumber", NID_pagerTelephoneNumber,
     10, &kObjectData[3698], 0},
    {"friendlyCountryName", "friendlyCountryName", NID_friendlyCountryName, 10,
     &kObjectData[3708], 0},
    {"organizationalStatus", "organizationalStatus", NID_organizationalStatus,
     10, &kObjectData[3718], 0},
    {"janetMailbox", "janetMailbox", NID_janetMailbox, 10, &kObjectData[3728],
     0},
    {"mailPreferenceOption", "mailPreferenceOption", NID_mailPreferenceOption,
     10, &kObjectData[3738], 0},
    {"buildingName", "buildingName", NID_buildingName, 10, &kObjectData[3748],
     0},
    {"dSAQuality", "dSAQuality", NID_dSAQuality, 10, &kObjectData[3758], 0},
    {"singleLevelQuality", "singleLevelQuality", NID_singleLevelQuality, 10,
     &kObjectData[3768], 0},
    {"subtreeMinimumQuality", "subtreeMinimumQuality",
     NID_subtreeMinimumQuality, 10, &kObjectData[3778], 0},
    {"subtreeMaximumQuality", "subtreeMaximumQuality",
     NID_subtreeMaximumQuality, 10, &kObjectData[3788], 0},
    {"personalSignature", "personalSignature", NID_personalSignature, 10,
     &kObjectData[3798], 0},
    {"dITRedirect", "dITRedirect", NID_dITRedirect, 10, &kObjectData[3808], 0},
    {"audio", "audio", NID_audio, 10, &kObjectData[3818], 0},
    {"documentPublisher", "documentPublisher", NID_documentPublisher, 10,
     &kObjectData[3828], 0},
    {"x500UniqueIdentifier", "x500UniqueIdentifier", NID_x500UniqueIdentifier,
     3, &kObjectData[3838], 0},
    {"mime-mhs", "MIME MHS", NID_mime_mhs, 5, &kObjectData[3841], 0},
    {"mime-mhs-headings", "mime-mhs-headings", NID_mime_mhs_headings, 6,
     &kObjectData[3846], 0},
    {"mime-mhs-bodies", "mime-mhs-bodies", NID_mime_mhs_bodies, 6,
     &kObjectData[3852], 0},
    {"id-hex-partial-message", "id-hex-partial-message",
     NID_id_hex_partial_message, 7, &kObjectData[3858], 0},
    {"id-hex-multipart-message", "id-hex-multipart-message",
     NID_id_hex_multipart_message, 7, &kObjectData[3865], 0},
    {"generationQualifier", "generationQualifier", NID_generationQualifier, 3,
     &kObjectData[3872], 0},
    {"pseudonym", "pseudonym", NID_pseudonym, 3, &kObjectData[3875], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"id-set", "Secure Electronic Transactions", NID_id_set, 2,
     &kObjectData[3878], 0},
    {"set-ctype", "content types", NID_set_ctype, 3, &kObjectData[3880], 0},
    {"set-msgExt", "message extensions", NID_set_msgExt, 3, &kObjectData[3883],
     0},
    {"set-attr", "set-attr", NID_set_attr, 3, &kObjectData[3886], 0},
    {"set-policy", "set-policy", NID_set_policy, 3, &kObjectData[3889], 0},
    {"set-certExt", "certificate extensions", NID_set_certExt, 3,
     &kObjectData[3892], 0},
    {"set-brand", "set-brand", NID_set_brand, 3, &kObjectData[3895], 0},
    {"setct-PANData", "setct-PANData", NID_setct_PANData, 4, &kObjectData[3898],
     0},
    {"setct-PANToken", "setct-PANToken", NID_setct_PANToken, 4,
     &kObjectData[3902], 0},
    {"setct-PANOnly", "setct-PANOnly", NID_setct_PANOnly, 4, &kObjectData[3906],
     0},
    {"setct-OIData", "setct-OIData", NID_setct_OIData, 4, &kObjectData[3910],
     0},
    {"setct-PI", "setct-PI", NID_setct_PI, 4, &kObjectData[3914], 0},
    {"setct-PIData", "setct-PIData", NID_setct_PIData, 4, &kObjectData[3918],
     0},
    {"setct-PIDataUnsigned", "setct-PIDataUnsigned", NID_setct_PIDataUnsigned,
     4, &kObjectData[3922], 0},
    {"setct-HODInput", "setct-HODInput", NID_setct_HODInput, 4,
     &kObjectData[3926], 0},
    {"setct-AuthResBaggage", "setct-AuthResBaggage", NID_setct_AuthResBaggage,
     4, &kObjectData[3930], 0},
    {"setct-AuthRevReqBaggage", "setct-AuthRevReqBaggage",
     NID_setct_AuthRevReqBaggage, 4, &kObjectData[3934], 0},
    {"setct-AuthRevResBaggage", "setct-AuthRevResBaggage",
     NID_setct_AuthRevResBaggage, 4, &kObjectData[3938], 0},
    {"setct-CapTokenSeq", "setct-CapTokenSeq", NID_setct_CapTokenSeq, 4,
     &kObjectData[3942], 0},
    {"setct-PInitResData", "setct-PInitResData", NID_setct_PInitResData, 4,
     &kObjectData[3946], 0},
    {"setct-PI-TBS", "setct-PI-TBS", NID_setct_PI_TBS, 4, &kObjectData[3950],
     0},
    {"setct-PResData", "setct-PResData", NID_setct_PResData, 4,
     &kObjectData[3954], 0},
    {"setct-AuthReqTBS", "setct-AuthReqTBS", NID_setct_AuthReqTBS, 4,
     &kObjectData[3958], 0},
    {"setct-AuthResTBS", "setct-AuthResTBS", NID_setct_AuthResTBS, 4,
     &kObjectData[3962], 0},
    {"setct-AuthResTBSX", "setct-AuthResTBSX", NID_setct_AuthResTBSX, 4,
     &kObjectData[3966], 0},
    {"setct-AuthTokenTBS", "setct-AuthTokenTBS", NID_setct_AuthTokenTBS, 4,
     &kObjectData[3970], 0},
    {"setct-CapTokenData", "setct-CapTokenData", NID_setct_CapTokenData, 4,
     &kObjectData[3974], 0},
    {"setct-CapTokenTBS", "setct-CapTokenTBS", NID_setct_CapTokenTBS, 4,
     &kObjectData[3978], 0},
    {"setct-AcqCardCodeMsg", "setct-AcqCardCodeMsg", NID_setct_AcqCardCodeMsg,
     4, &kObjectData[3982], 0},
    {"setct-AuthRevReqTBS", "setct-AuthRevReqTBS", NID_setct_AuthRevReqTBS, 4,
     &kObjectData[3986], 0},
    {"setct-AuthRevResData", "setct-AuthRevResData", NID_setct_AuthRevResData,
     4, &kObjectData[3990], 0},
    {"setct-AuthRevResTBS", "setct-AuthRevResTBS", NID_setct_AuthRevResTBS, 4,
     &kObjectData[3994], 0},
    {"setct-CapReqTBS", "setct-CapReqTBS", NID_setct_CapReqTBS, 4,
     &kObjectData[3998], 0},
    {"setct-CapReqTBSX", "setct-CapReqTBSX", NID_setct_CapReqTBSX, 4,
     &kObjectData[4002], 0},
    {"setct-CapResData", "setct-CapResData", NID_setct_CapResData, 4,
     &kObjectData[4006], 0},
    {"setct-CapRevReqTBS", "setct-CapRevReqTBS", NID_setct_CapRevReqTBS, 4,
     &kObjectData[4010], 0},
    {"setct-CapRevReqTBSX", "setct-CapRevReqTBSX", NID_setct_CapRevReqTBSX, 4,
     &kObjectData[4014], 0},
    {"setct-CapRevResData", "setct-CapRevResData", NID_setct_CapRevResData, 4,
     &kObjectData[4018], 0},
    {"setct-CredReqTBS", "setct-CredReqTBS", NID_setct_CredReqTBS, 4,
     &kObjectData[4022], 0},
    {"setct-CredReqTBSX", "setct-CredReqTBSX", NID_setct_CredReqTBSX, 4,
     &kObjectData[4026], 0},
    {"setct-CredResData", "setct-CredResData", NID_setct_CredResData, 4,
     &kObjectData[4030], 0},
    {"setct-CredRevReqTBS", "setct-CredRevReqTBS", NID_setct_CredRevReqTBS, 4,
     &kObjectData[4034], 0},
    {"setct-CredRevReqTBSX", "setct-CredRevReqTBSX", NID_setct_CredRevReqTBSX,
     4, &kObjectData[4038], 0},
    {"setct-CredRevResData", "setct-CredRevResData", NID_setct_CredRevResData,
     4, &kObjectData[4042], 0},
    {"setct-PCertReqData", "setct-PCertReqData", NID_setct_PCertReqData, 4,
     &kObjectData[4046], 0},
    {"setct-PCertResTBS", "setct-PCertResTBS", NID_setct_PCertResTBS, 4,
     &kObjectData[4050], 0},
    {"setct-BatchAdminReqData", "setct-BatchAdminReqData",
     NID_setct_BatchAdminReqData, 4, &kObjectData[4054], 0},
    {"setct-BatchAdminResData", "setct-BatchAdminResData",
     NID_setct_BatchAdminResData, 4, &kObjectData[4058], 0},
    {"setct-CardCInitResTBS", "setct-CardCInitResTBS",
     NID_setct_CardCInitResTBS, 4, &kObjectData[4062], 0},
    {"setct-MeAqCInitResTBS", "setct-MeAqCInitResTBS",
     NID_setct_MeAqCInitResTBS, 4, &kObjectData[4066], 0},
    {"setct-RegFormResTBS", "setct-RegFormResTBS", NID_setct_RegFormResTBS, 4,
     &kObjectData[4070], 0},
    {"setct-CertReqData", "setct-CertReqData", NID_setct_CertReqData, 4,
     &kObjectData[4074], 0},
    {"setct-CertReqTBS", "setct-CertReqTBS", NID_setct_CertReqTBS, 4,
     &kObjectData[4078], 0},
    {"setct-CertResData", "setct-CertResData", NID_setct_CertResData, 4,
     &kObjectData[4082], 0},
    {"setct-CertInqReqTBS", "setct-CertInqReqTBS", NID_setct_CertInqReqTBS, 4,
     &kObjectData[4086], 0},
    {"setct-ErrorTBS", "setct-ErrorTBS", NID_setct_ErrorTBS, 4,
     &kObjectData[4090], 0},
    {"setct-PIDualSignedTBE", "setct-PIDualSignedTBE",
     NID_setct_PIDualSignedTBE, 4, &kObjectData[4094], 0},
    {"setct-PIUnsignedTBE", "setct-PIUnsignedTBE", NID_setct_PIUnsignedTBE, 4,
     &kObjectData[4098], 0},
    {"setct-AuthReqTBE", "setct-AuthReqTBE", NID_setct_AuthReqTBE, 4,
     &kObjectData[4102], 0},
    {"setct-AuthResTBE", "setct-AuthResTBE", NID_setct_AuthResTBE, 4,
     &kObjectData[4106], 0},
    {"setct-AuthResTBEX", "setct-AuthResTBEX", NID_setct_AuthResTBEX, 4,
     &kObjectData[4110], 0},
    {"setct-AuthTokenTBE", "setct-AuthTokenTBE", NID_setct_AuthTokenTBE, 4,
     &kObjectData[4114], 0},
    {"setct-CapTokenTBE", "setct-CapTokenTBE", NID_setct_CapTokenTBE, 4,
     &kObjectData[4118], 0},
    {"setct-CapTokenTBEX", "setct-CapTokenTBEX", NID_setct_CapTokenTBEX, 4,
     &kObjectData[4122], 0},
    {"setct-AcqCardCodeMsgTBE", "setct-AcqCardCodeMsgTBE",
     NID_setct_AcqCardCodeMsgTBE, 4, &kObjectData[4126], 0},
    {"setct-AuthRevReqTBE", "setct-AuthRevReqTBE", NID_setct_AuthRevReqTBE, 4,
     &kObjectData[4130], 0},
    {"setct-AuthRevResTBE", "setct-AuthRevResTBE", NID_setct_AuthRevResTBE, 4,
     &kObjectData[4134], 0},
    {"setct-AuthRevResTBEB", "setct-AuthRevResTBEB", NID_setct_AuthRevResTBEB,
     4, &kObjectData[4138], 0},
    {"setct-CapReqTBE", "setct-CapReqTBE", NID_setct_CapReqTBE, 4,
     &kObjectData[4142], 0},
    {"setct-CapReqTBEX", "setct-CapReqTBEX", NID_setct_CapReqTBEX, 4,
     &kObjectData[4146], 0},
    {"setct-CapResTBE", "setct-CapResTBE", NID_setct_CapResTBE, 4,
     &kObjectData[4150], 0},
    {"setct-CapRevReqTBE", "setct-CapRevReqTBE", NID_setct_CapRevReqTBE, 4,
     &kObjectData[4154], 0},
    {"setct-CapRevReqTBEX", "setct-CapRevReqTBEX", NID_setct_CapRevReqTBEX, 4,
     &kObjectData[4158], 0},
    {"setct-CapRevResTBE", "setct-CapRevResTBE", NID_setct_CapRevResTBE, 4,
     &kObjectData[4162], 0},
    {"setct-CredReqTBE", "setct-CredReqTBE", NID_setct_CredReqTBE, 4,
     &kObjectData[4166], 0},
    {"setct-CredReqTBEX", "setct-CredReqTBEX", NID_setct_CredReqTBEX, 4,
     &kObjectData[4170], 0},
    {"setct-CredResTBE", "setct-CredResTBE", NID_setct_CredResTBE, 4,
     &kObjectData[4174], 0},
    {"setct-CredRevReqTBE", "setct-CredRevReqTBE", NID_setct_CredRevReqTBE, 4,
     &kObjectData[4178], 0},
    {"setct-CredRevReqTBEX", "setct-CredRevReqTBEX", NID_setct_CredRevReqTBEX,
     4, &kObjectData[4182], 0},
    {"setct-CredRevResTBE", "setct-CredRevResTBE", NID_setct_CredRevResTBE, 4,
     &kObjectData[4186], 0},
    {"setct-BatchAdminReqTBE", "setct-BatchAdminReqTBE",
     NID_setct_BatchAdminReqTBE, 4, &kObjectData[4190], 0},
    {"setct-BatchAdminResTBE", "setct-BatchAdminResTBE",
     NID_setct_BatchAdminResTBE, 4, &kObjectData[4194], 0},
    {"setct-RegFormReqTBE", "setct-RegFormReqTBE", NID_setct_RegFormReqTBE, 4,
     &kObjectData[4198], 0},
    {"setct-CertReqTBE", "setct-CertReqTBE", NID_setct_CertReqTBE, 4,
     &kObjectData[4202], 0},
    {"setct-CertReqTBEX", "setct-CertReqTBEX", NID_setct_CertReqTBEX, 4,
     &kObjectData[4206], 0},
    {"setct-CertResTBE", "setct-CertResTBE", NID_setct_CertResTBE, 4,
     &kObjectData[4210], 0},
    {"setct-CRLNotificationTBS", "setct-CRLNotificationTBS",
     NID_setct_CRLNotificationTBS, 4, &kObjectData[4214], 0},
    {"setct-CRLNotificationResTBS", "setct-CRLNotificationResTBS",
     NID_setct_CRLNotificationResTBS, 4, &kObjectData[4218], 0},
    {"setct-BCIDistributionTBS", "setct-BCIDistributionTBS",
     NID_setct_BCIDistributionTBS, 4, &kObjectData[4222], 0},
    {"setext-genCrypt", "generic cryptogram", NID_setext_genCrypt, 4,
     &kObjectData[4226], 0},
    {"setext-miAuth", "merchant initiated auth", NID_setext_miAuth, 4,
     &kObjectData[4230], 0},
    {"setext-pinSecure", "setext-pinSecure", NID_setext_pinSecure, 4,
     &kObjectData[4234], 0},
    {"setext-pinAny", "setext-pinAny", NID_setext_pinAny, 4, &kObjectData[4238],
     0},
    {"setext-track2", "setext-track2", NID_setext_track2, 4, &kObjectData[4242],
     0},
    {"setext-cv", "additional verification", NID_setext_cv, 4,
     &kObjectData[4246], 0},
    {"set-policy-root", "set-policy-root", NID_set_policy_root, 4,
     &kObjectData[4250], 0},
    {"setCext-hashedRoot", "setCext-hashedRoot", NID_setCext_hashedRoot, 4,
     &kObjectData[4254], 0},
    {"setCext-certType", "setCext-certType", NID_setCext_certType, 4,
     &kObjectData[4258], 0},
    {"setCext-merchData", "setCext-merchData", NID_setCext_merchData, 4,
     &kObjectData[4262], 0},
    {"setCext-cCertRequired", "setCext-cCertRequired",
     NID_setCext_cCertRequired, 4, &kObjectData[4266], 0},
    {"setCext-tunneling", "setCext-tunneling", NID_setCext_tunneling, 4,
     &kObjectData[4270], 0},
    {"setCext-setExt", "setCext-setExt", NID_setCext_setExt, 4,
     &kObjectData[4274], 0},
    {"setCext-setQualf", "setCext-setQualf", NID_setCext_setQualf, 4,
     &kObjectData[4278], 0},
    {"setCext-PGWYcapabilities", "setCext-PGWYcapabilities",
     NID_setCext_PGWYcapabilities, 4, &kObjectData[4282], 0},
    {"setCext-TokenIdentifier", "setCext-TokenIdentifier",
     NID_setCext_TokenIdentifier, 4, &kObjectData[4286], 0},
    {"setCext-Track2Data", "setCext-Track2Data", NID_setCext_Track2Data, 4,
     &kObjectData[4290], 0},
    {"setCext-TokenType", "setCext-TokenType", NID_setCext_TokenType, 4,
     &kObjectData[4294], 0},
    {"setCext-IssuerCapabilities", "setCext-IssuerCapabilities",
     NID_setCext_IssuerCapabilities, 4, &kObjectData[4298], 0},
    {"setAttr-Cert", "setAttr-Cert", NID_setAttr_Cert, 4, &kObjectData[4302],
     0},
    {"setAttr-PGWYcap", "payment gateway capabilities", NID_setAttr_PGWYcap, 4,
     &kObjectData[4306], 0},
    {"setAttr-TokenType", "setAttr-TokenType", NID_setAttr_TokenType, 4,
     &kObjectData[4310], 0},
    {"setAttr-IssCap", "issuer capabilities", NID_setAttr_IssCap, 4,
     &kObjectData[4314], 0},
    {"set-rootKeyThumb", "set-rootKeyThumb", NID_set_rootKeyThumb, 5,
     &kObjectData[4318], 0},
    {"set-addPolicy", "set-addPolicy", NID_set_addPolicy, 5, &kObjectData[4323],
     0},
    {"setAttr-Token-EMV", "setAttr-Token-EMV", NID_setAttr_Token_EMV, 5,
     &kObjectData[4328], 0},
    {"setAttr-Token-B0Prime", "setAttr-Token-B0Prime",
     NID_setAttr_Token_B0Prime, 5, &kObjectData[4333], 0},
    {"setAttr-IssCap-CVM", "setAttr-IssCap-CVM", NID_setAttr_IssCap_CVM, 5,
     &kObjectData[4338], 0},
    {"setAttr-IssCap-T2", "setAttr-IssCap-T2", NID_setAttr_IssCap_T2, 5,
     &kObjectData[4343], 0},
    {"setAttr-IssCap-Sig", "setAttr-IssCap-Sig", NID_setAttr_IssCap_Sig, 5,
     &kObjectData[4348], 0},
    {"setAttr-GenCryptgrm", "generate cryptogram", NID_setAttr_GenCryptgrm, 6,
     &kObjectData[4353], 0},
    {"setAttr-T2Enc", "encrypted track 2", NID_setAttr_T2Enc, 6,
     &kObjectData[4359], 0},
    {"setAttr-T2cleartxt", "cleartext track 2", NID_setAttr_T2cleartxt, 6,
     &kObjectData[4365], 0},
    {"setAttr-TokICCsig", "ICC or token signature", NID_setAttr_TokICCsig, 6,
     &kObjectData[4371], 0},
    {"setAttr-SecDevSig", "secure device signature", NID_setAttr_SecDevSig, 6,
     &kObjectData[4377], 0},
    {"set-brand-IATA-ATA", "set-brand-IATA-ATA", NID_set_brand_IATA_ATA, 4,
     &kObjectData[4383], 0},
    {"set-brand-Diners", "set-brand-Diners", NID_set_brand_Diners, 4,
     &kObjectData[4387], 0},
    {"set-brand-AmericanExpress", "set-brand-AmericanExpress",
     NID_set_brand_AmericanExpress, 4, &kObjectData[4391], 0},
    {"set-brand-JCB", "set-brand-JCB", NID_set_brand_JCB, 4, &kObjectData[4395],
     0},
    {"set-brand-Visa", "set-brand-Visa", NID_set_brand_Visa, 4,
     &kObjectData[4399], 0},
    {"set-brand-MasterCard", "set-brand-MasterCard", NID_set_brand_MasterCard,
     4, &kObjectData[4403], 0},
    {"set-brand-Novus", "set-brand-Novus", NID_set_brand_Novus, 5,
     &kObjectData[4407], 0},
    {"DES-CDMF", "des-cdmf", NID_des_cdmf, 8, &kObjectData[4412], 0},
    {"rsaOAEPEncryptionSET", "rsaOAEPEncryptionSET", NID_rsaOAEPEncryptionSET,
     9, &kObjectData[4420], 0},
    {"ITU-T", "itu-t", NID_itu_t, 0, NULL, 0},
    {"JOINT-ISO-ITU-T", "joint-iso-itu-t", NID_joint_iso_itu_t, 0, NULL, 0},
    {"international-organizations", "International Organizations",
     NID_international_organizations, 1, &kObjectData[4429], 0},
    {"msSmartcardLogin", "Microsoft Smartcardlogin", NID_ms_smartcard_login, 10,
     &kObjectData[4430], 0},
    {"msUPN", "Microsoft Universal Principal Name", NID_ms_upn, 10,
     &kObjectData[4440], 0},
    {"AES-128-CFB1", "aes-128-cfb1", NID_aes_128_cfb1, 0, NULL, 0},
    {"AES-192-CFB1", "aes-192-cfb1", NID_aes_192_cfb1, 0, NULL, 0},
    {"AES-256-CFB1", "aes-256-cfb1", NID_aes_256_cfb1, 0, NULL, 0},
    {"AES-128-CFB8", "aes-128-cfb8", NID_aes_128_cfb8, 0, NULL, 0},
    {"AES-192-CFB8", "aes-192-cfb8", NID_aes_192_cfb8, 0, NULL, 0},
    {"AES-256-CFB8", "aes-256-cfb8", NID_aes_256_cfb8, 0, NULL, 0},
    {"DES-CFB1", "des-cfb1", NID_des_cfb1, 0, NULL, 0},
    {"DES-CFB8", "des-cfb8", NID_des_cfb8, 0, NULL, 0},
    {"DES-EDE3-CFB1", "des-ede3-cfb1", NID_des_ede3_cfb1, 0, NULL, 0},
    {"DES-EDE3-CFB8", "des-ede3-cfb8", NID_des_ede3_cfb8, 0, NULL, 0},
    {"street", "streetAddress", NID_streetAddress, 3, &kObjectData[4450], 0},
    {"postalCode", "postalCode", NID_postalCode, 3, &kObjectData[4453], 0},
    {"id-ppl", "id-ppl", NID_id_ppl, 7, &kObjectData[4456], 0},
    {"proxyCertInfo", "Proxy Certificate Information", NID_proxyCertInfo, 8,
     &kObjectData[4463], 0},
    {"id-ppl-anyLanguage", "Any language", NID_id_ppl_anyLanguage, 8,
     &kObjectData[4471], 0},
    {"id-ppl-inheritAll", "Inherit all", NID_id_ppl_inheritAll, 8,
     &kObjectData[4479], 0},
    {"nameConstraints", "X509v3 Name Constraints", NID_name_constraints, 3,
     &kObjectData[4487], 0},
    {"id-ppl-independent", "Independent", NID_Independent, 8,
     &kObjectData[4490], 0},
    {"RSA-SHA256", "sha256WithRSAEncryption", NID_sha256WithRSAEncryption, 9,
     &kObjectData[4498], 0},
    {"RSA-SHA384", "sha384WithRSAEncryption", NID_sha384WithRSAEncryption, 9,
     &kObjectData[4507], 0},
    {"RSA-SHA512", "sha512WithRSAEncryption", NID_sha512WithRSAEncryption, 9,
     &kObjectData[4516], 0},
    {"RSA-SHA224", "sha224WithRSAEncryption", NID_sha224WithRSAEncryption, 9,
     &kObjectData[4525], 0},
    {"SHA256", "sha256", NID_sha256, 9, &kObjectData[4534], 0},
    {"SHA384", "sha384", NID_sha384, 9, &kObjectData[4543], 0},
    {"SHA512", "sha512", NID_sha512, 9, &kObjectData[4552], 0},
    {"SHA224", "sha224", NID_sha224, 9, &kObjectData[4561], 0},
    {"identified-organization", "identified-organization",
     NID_identified_organization, 1, &kObjectData[4570], 0},
    {"certicom-arc", "certicom-arc", NID_certicom_arc, 3, &kObjectData[4571],
     0},
    {"wap", "wap", NID_wap, 2, &kObjectData[4574], 0},
    {"wap-wsg", "wap-wsg", NID_wap_wsg, 3, &kObjectData[4576], 0},
    {"id-characteristic-two-basis", "id-characteristic-two-basis",
     NID_X9_62_id_characteristic_two_basis, 8, &kObjectData[4579], 0},
    {"onBasis", "onBasis", NID_X9_62_onBasis, 9, &kObjectData[4587], 0},
    {"tpBasis", "tpBasis", NID_X9_62_tpBasis, 9, &kObjectData[4596], 0},
    {"ppBasis", "ppBasis", NID_X9_62_ppBasis, 9, &kObjectData[4605], 0},
    {"c2pnb163v1", "c2pnb163v1", NID_X9_62_c2pnb163v1, 8, &kObjectData[4614],
     0},
    {"c2pnb163v2", "c2pnb163v2", NID_X9_62_c2pnb163v2, 8, &kObjectData[4622],
     0},
    {"c2pnb163v3", "c2pnb163v3", NID_X9_62_c2pnb163v3, 8, &kObjectData[4630],
     0},
    {"c2pnb176v1", "c2pnb176v1", NID_X9_62_c2pnb176v1, 8, &kObjectData[4638],
     0},
    {"c2tnb191v1", "c2tnb191v1", NID_X9_62_c2tnb191v1, 8, &kObjectData[4646],
     0},
    {"c2tnb191v2", "c2tnb191v2", NID_X9_62_c2tnb191v2, 8, &kObjectData[4654],
     0},
    {"c2tnb191v3", "c2tnb191v3", NID_X9_62_c2tnb191v3, 8, &kObjectData[4662],
     0},
    {"c2onb191v4", "c2onb191v4", NID_X9_62_c2onb191v4, 8, &kObjectData[4670],
     0},
    {"c2onb191v5", "c2onb191v5", NID_X9_62_c2onb191v5, 8, &kObjectData[4678],
     0},
    {"c2pnb208w1", "c2pnb208w1", NID_X9_62_c2pnb208w1, 8, &kObjectData[4686],
     0},
    {"c2tnb239v1", "c2tnb239v1", NID_X9_62_c2tnb239v1, 8, &kObjectData[4694],
     0},
    {"c2tnb239v2", "c2tnb239v2", NID_X9_62_c2tnb239v2, 8, &kObjectData[4702],
     0},
    {"c2tnb239v3", "c2tnb239v3", NID_X9_62_c2tnb239v3, 8, &kObjectData[4710],
     0},
    {"c2onb239v4", "c2onb239v4", NID_X9_62_c2onb239v4, 8, &kObjectData[4718],
     0},
    {"c2onb239v5", "c2onb239v5", NID_X9_62_c2onb239v5, 8, &kObjectData[4726],
     0},
    {"c2pnb272w1", "c2pnb272w1", NID_X9_62_c2pnb272w1, 8, &kObjectData[4734],
     0},
    {"c2pnb304w1", "c2pnb304w1", NID_X9_62_c2pnb304w1, 8, &kObjectData[4742],
     0},
    {"c2tnb359v1", "c2tnb359v1", NID_X9_62_c2tnb359v1, 8, &kObjectData[4750],
     0},
    {"c2pnb368w1", "c2pnb368w1", NID_X9_62_c2pnb368w1, 8, &kObjectData[4758],
     0},
    {"c2tnb431r1", "c2tnb431r1", NID_X9_62_c2tnb431r1, 8, &kObjectData[4766],
     0},
    {"secp112r1", "secp112r1", NID_secp112r1, 5, &kObjectData[4774], 0},
    {"secp112r2", "secp112r2", NID_secp112r2, 5, &kObjectData[4779], 0},
    {"secp128r1", "secp128r1", NID_secp128r1, 5, &kObjectData[4784], 0},
    {"secp128r2", "secp128r2", NID_secp128r2, 5, &kObjectData[4789], 0},
    {"secp160k1", "secp160k1", NID_secp160k1, 5, &kObjectData[4794], 0},
    {"secp160r1", "secp160r1", NID_secp160r1, 5, &kObjectData[4799], 0},
    {"secp160r2", "secp160r2", NID_secp160r2, 5, &kObjectData[4804], 0},
    {"secp192k1", "secp192k1", NID_secp192k1, 5, &kObjectData[4809], 0},
    {"secp224k1", "secp224k1", NID_secp224k1, 5, &kObjectData[4814], 0},
    {"secp224r1", "secp224r1", NID_secp224r1, 5, &kObjectData[4819], 0},
    {"secp256k1", "secp256k1", NID_secp256k1, 5, &kObjectData[4824], 0},
    {"secp384r1", "secp384r1", NID_secp384r1, 5, &kObjectData[4829], 0},
    {"secp521r1", "secp521r1", NID_secp521r1, 5, &kObjectData[4834], 0},
    {"sect113r1", "sect113r1", NID_sect113r1, 5, &kObjectData[4839], 0},
    {"sect113r2", "sect113r2", NID_sect113r2, 5, &kObjectData[4844], 0},
    {"sect131r1", "sect131r1", NID_sect131r1, 5, &kObjectData[4849], 0},
    {"sect131r2", "sect131r2", NID_sect131r2, 5, &kObjectData[4854], 0},
    {"sect163k1", "sect163k1", NID_sect163k1, 5, &kObjectData[4859], 0},
    {"sect163r1", "sect163r1", NID_sect163r1, 5, &kObjectData[4864], 0},
    {"sect163r2", "sect163r2", NID_sect163r2, 5, &kObjectData[4869], 0},
    {"sect193r1", "sect193r1", NID_sect193r1, 5, &kObjectData[4874], 0},
    {"sect193r2", "sect193r2", NID_sect193r2, 5, &kObjectData[4879], 0},
    {"sect233k1", "sect233k1", NID_sect233k1, 5, &kObjectData[4884], 0},
    {"sect233r1", "sect233r1", NID_sect233r1, 5, &kObjectData[4889], 0},
    {"sect239k1", "sect239k1", NID_sect239k1, 5, &kObjectData[4894], 0},
    {"sect283k1", "sect283k1", NID_sect283k1, 5, &kObjectData[4899], 0},
    {"sect283r1", "sect283r1", NID_sect283r1, 5, &kObjectData[4904], 0},
    {"sect409k1", "sect409k1", NID_sect409k1, 5, &kObjectData[4909], 0},
    {"sect409r1", "sect409r1", NID_sect409r1, 5, &kObjectData[4914], 0},
    {"sect571k1", "sect571k1", NID_sect571k1, 5, &kObjectData[4919], 0},
    {"sect571r1", "sect571r1", NID_sect571r1, 5, &kObjectData[4924], 0},
    {"wap-wsg-idm-ecid-wtls1", "wap-wsg-idm-ecid-wtls1",
     NID_wap_wsg_idm_ecid_wtls1, 5, &kObjectData[4929], 0},
    {"wap-wsg-idm-ecid-wtls3", "wap-wsg-idm-ecid-wtls3",
     NID_wap_wsg_idm_ecid_wtls3, 5, &kObjectData[4934], 0},
    {"wap-wsg-idm-ecid-wtls4", "wap-wsg-idm-ecid-wtls4",
     NID_wap_wsg_idm_ecid_wtls4, 5, &kObjectData[4939], 0},
    {"wap-wsg-idm-ecid-wtls5", "wap-wsg-idm-ecid-wtls5",
     NID_wap_wsg_idm_ecid_wtls5, 5, &kObjectData[4944], 0},
    {"wap-wsg-idm-ecid-wtls6", "wap-wsg-idm-ecid-wtls6",
     NID_wap_wsg_idm_ecid_wtls6, 5, &kObjectData[4949], 0},
    {"wap-wsg-idm-ecid-wtls7", "wap-wsg-idm-ecid-wtls7",
     NID_wap_wsg_idm_ecid_wtls7, 5, &kObjectData[4954], 0},
    {"wap-wsg-idm-ecid-wtls8", "wap-wsg-idm-ecid-wtls8",
     NID_wap_wsg_idm_ecid_wtls8, 5, &kObjectData[4959], 0},
    {"wap-wsg-idm-ecid-wtls9", "wap-wsg-idm-ecid-wtls9",
     NID_wap_wsg_idm_ecid_wtls9, 5, &kObjectData[4964], 0},
    {"wap-wsg-idm-ecid-wtls10", "wap-wsg-idm-ecid-wtls10",
     NID_wap_wsg_idm_ecid_wtls10, 5, &kObjectData[4969], 0},
    {"wap-wsg-idm-ecid-wtls11", "wap-wsg-idm-ecid-wtls11",
     NID_wap_wsg_idm_ecid_wtls11, 5, &kObjectData[4974], 0},
    {"wap-wsg-idm-ecid-wtls12", "wap-wsg-idm-ecid-wtls12",
     NID_wap_wsg_idm_ecid_wtls12, 5, &kObjectData[4979], 0},
    {"anyPolicy", "X509v3 Any Policy", NID_any_policy, 4, &kObjectData[4984],
     0},
    {"policyMappings", "X509v3 Policy Mappings", NID_policy_mappings, 3,
     &kObjectData[4988], 0},
    {"inhibitAnyPolicy", "X509v3 Inhibit Any Policy", NID_inhibit_any_policy, 3,
     &kObjectData[4991], 0},
    {"Oakley-EC2N-3", "ipsec3", NID_ipsec3, 0, NULL, 0},
    {"Oakley-EC2N-4", "ipsec4", NID_ipsec4, 0, NULL, 0},
    {"CAMELLIA-128-CBC", "camellia-128-cbc", NID_camellia_128_cbc, 11,
     &kObjectData[4994], 0},
    {"CAMELLIA-192-CBC", "camellia-192-cbc", NID_camellia_192_cbc, 11,
     &kObjectData[5005], 0},
    {"CAMELLIA-256-CBC", "camellia-256-cbc", NID_camellia_256_cbc, 11,
     &kObjectData[5016], 0},
    {"CAMELLIA-128-ECB", "camellia-128-ecb", NID_camellia_128_ecb, 8,
     &kObjectData[5027], 0},
    {"CAMELLIA-192-ECB", "camellia-192-ecb", NID_camellia_192_ecb, 8,
     &kObjectData[5035], 0},
    {"CAMELLIA-256-ECB", "camellia-256-ecb", NID_camellia_256_ecb, 8,
     &kObjectData[5043], 0},
    {"CAMELLIA-128-CFB", "camellia-128-cfb", NID_camellia_128_cfb128, 8,
     &kObjectData[5051], 0},
    {"CAMELLIA-192-CFB", "camellia-192-cfb", NID_camellia_192_cfb128, 8,
     &kObjectData[5059], 0},
    {"CAMELLIA-256-CFB", "camellia-256-cfb", NID_camellia_256_cfb128, 8,
     &kObjectData[5067], 0},
    {"CAMELLIA-128-CFB1", "camellia-128-cfb1", NID_camellia_128_cfb1, 0, NULL,
     0},
    {"CAMELLIA-192-CFB1", "camellia-192-cfb1", NID_camellia_192_cfb1, 0, NULL,
     0},
    {"CAMELLIA-256-CFB1", "camellia-256-cfb1", NID_camellia_256_cfb1, 0, NULL,
     0},
    {"CAMELLIA-128-CFB8", "camellia-128-cfb8", NID_camellia_128_cfb8, 0, NULL,
     0},
    {"CAMELLIA-192-CFB8", "camellia-192-cfb8", NID_camellia_192_cfb8, 0, NULL,
     0},
    {"CAMELLIA-256-CFB8", "camellia-256-cfb8", NID_camellia_256_cfb8, 0, NULL,
     0},
    {"CAMELLIA-128-OFB", "camellia-128-ofb", NID_camellia_128_ofb128, 8,
     &kObjectData[5075], 0},
    {"CAMELLIA-192-OFB", "camellia-192-ofb", NID_camellia_192_ofb128, 8,
     &kObjectData[5083], 0},
    {"CAMELLIA-256-OFB", "camellia-256-ofb", NID_camellia_256_ofb128, 8,
     &kObjectData[5091], 0},
    {"subjectDirectoryAttributes", "X509v3 Subject Directory Attributes",
     NID_subject_directory_attributes, 3, &kObjectData[5099], 0},
    {"issuingDistributionPoint", "X509v3 Issuing Distribution Point",
     NID_issuing_distribution_point, 3, &kObjectData[5102], 0},
    {"certificateIssuer", "X509v3 Certificate Issuer", NID_certificate_issuer,
     3, &kObjectData[5105], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"KISA", "kisa", NID_kisa, 6, &kObjectData[5108], 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {NULL, NULL, NID_undef, 0, NULL, 0},
    {"SEED-ECB", "seed-ecb", NID_seed_ecb, 8, &kObjectData[5114], 0},
    {"SEED-CBC", "seed-cbc", NID_seed_cbc, 8, &kObjectData[5122], 0},
    {"SEED-OFB", "seed-ofb", NID_seed_ofb128, 8, &kObjectData[5130], 0},
    {"SEED-CFB", "seed-cfb", NID_seed_cfb128, 8, &kObjectData[5138], 0},
    {"HMAC-MD5", "hmac-md5", NID_hmac_md5, 8, &kObjectData[5146], 0},
    {"HMAC-SHA1", "hmac-sha1", NID_hmac_sha1, 8, &kObjectData[5154], 0},
    {"id-PasswordBasedMAC", "password based MAC", NID_id_PasswordBasedMAC, 9,
     &kObjectData[5162], 0},
    {"id-DHBasedMac", "Diffie-Hellman based MAC", NID_id_DHBasedMac, 9,
     &kObjectData[5171], 0},
    {"id-it-suppLangTags", "id-it-suppLangTags", NID_id_it_suppLangTags, 8,
     &kObjectData[5180], 0},
    {"caRepository", "CA Repository", NID_caRepository, 8, &kObjectData[5188],
     0},
    {"id-smime-ct-compressedData", "id-smime-ct-compressedData",
     NID_id_smime_ct_compressedData, 11, &kObjectData[5196], 0},
    {"id-ct-asciiTextWithCRLF", "id-ct-asciiTextWithCRLF",
     NID_id_ct_asciiTextWithCRLF, 11, &kObjectData[5207], 0},
    {"id-aes128-wrap", "id-aes128-wrap", NID_id_aes128_wrap, 9,
     &kObjectData[5218], 0},
    {"id-aes192-wrap", "id-aes192-wrap", NID_id_aes192_wrap, 9,
     &kObjectData[5227], 0},
    {"id-aes256-wrap", "id-aes256-wrap", NID_id_aes256_wrap, 9,
     &kObjectData[5236], 0},
    {"ecdsa-with-Recommended", "ecdsa-with-Recommended",
     NID_ecdsa_with_Recommended, 7, &kObjectData[5245], 0},
    {"ecdsa-with-Specified", "ecdsa-with-Specified", NID_ecdsa_with_Specified,
     7, &kObjectData[5252], 0},
    {"ecdsa-with-SHA224", "ecdsa-with-SHA224", NID_ecdsa_with_SHA224, 8,
     &kObjectData[5259], 0},
    {"ecdsa-with-SHA256", "ecdsa-with-SHA256", NID_ecdsa_with_SHA256, 8,
     &kObjectData[5267], 0},
    {"ecdsa-with-SHA384", "ecdsa-with-SHA384", NID_ecdsa_with_SHA384, 8,
     &kObjectData[5275], 0},
    {"ecdsa-with-SHA512", "ecdsa-with-SHA512", NID_ecdsa_with_SHA512, 8,
     &kObjectData[5283], 0},
    {"hmacWithMD5", "hmacWithMD5", NID_hmacWithMD5, 8, &kObjectData[5291], 0},
    {"hmacWithSHA224", "hmacWithSHA224", NID_hmacWithSHA224, 8,
     &kObjectData[5299], 0},
    {"hmacWithSHA256", "hmacWithSHA256", NID_hmacWithSHA256, 8,
     &kObjectData[5307], 0},
    {"hmacWithSHA384", "hmacWithSHA384", NID_hmacWithSHA384, 8,
     &kObjectData[5315], 0},
    {"hmacWithSHA512", "hmacWithSHA512", NID_hmacWithSHA512, 8,
     &kObjectData[5323], 0},
    {"dsa_with_SHA224", "dsa_with_SHA224", NID_dsa_with_SHA224, 9,
     &kObjectData[5331], 0},
    {"dsa_with_SHA256", "dsa_with_SHA256", NID_dsa_with_SHA256, 9,
     &kObjectData[5340], 0},
    {"whirlpool", "whirlpool", NID_whirlpool, 6, &kObjectData[5349], 0},
    {"cryptopro", "cryptopro", NID_cryptopro, 5, &kObjectData[5355], 0},
    {"cryptocom", "cryptocom", NID_cryptocom, 5, &kObjectData[5360], 0},
    {"id-GostR3411-94-with-GostR3410-2001",
     "GOST R 34.11-94 with GOST R 34.10-2001",
     NID_id_GostR3411_94_with_GostR3410_2001, 6, &kObjectData[5365], 0},
    {"id-GostR3411-94-with-GostR3410-94",
     "GOST R 34.11-94 with GOST R 34.10-94",
     NID_id_GostR3411_94_with_GostR3410_94, 6, &kObjectData[5371], 0},
    {"md_gost94", "GOST R 34.11-94", NID_id_GostR3411_94, 6, &kObjectData[5377],
     0},
    {"id-HMACGostR3411-94", "HMAC GOST 34.11-94", NID_id_HMACGostR3411_94, 6,
     &kObjectData[5383], 0},
    {"gost2001", "GOST R 34.10-2001", NID_id_GostR3410_2001, 6,
     &kObjectData[5389], 0},
    {"gost94", "GOST R 34.10-94", NID_id_GostR3410_94, 6, &kObjectData[5395],
     0},
    {"gost89", "GOST 28147-89", NID_id_Gost28147_89, 6, &kObjectData[5401], 0},
    {"gost89-cnt", "gost89-cnt", NID_gost89_cnt, 0, NULL, 0},
    {"gost-mac", "GOST 28147-89 MAC", NID_id_Gost28147_89_MAC, 6,
     &kObjectData[5407], 0},
    {"prf-gostr3411-94", "GOST R 34.11-94 PRF", NID_id_GostR3411_94_prf, 6,
     &kObjectData[5413], 0},
    {"id-GostR3410-2001DH", "GOST R 34.10-2001 DH", NID_id_GostR3410_2001DH, 6,
     &kObjectData[5419], 0},
    {"id-GostR3410-94DH", "GOST R 34.10-94 DH", NID_id_GostR3410_94DH, 6,
     &kObjectData[5425], 0},
    {"id-Gost28147-89-CryptoPro-KeyMeshing",
     "id-Gost28147-89-CryptoPro-KeyMeshing",
     NID_id_Gost28147_89_CryptoPro_KeyMeshing, 7, &kObjectData[5431], 0},
    {"id-Gost28147-89-None-KeyMeshing", "id-Gost28147-89-None-KeyMeshing",
     NID_id_Gost28147_89_None_KeyMeshing, 7, &kObjectData[5438], 0},
    {"id-GostR3411-94-TestParamSet", "id-GostR3411-94-TestParamSet",
     NID_id_GostR3411_94_TestParamSet, 7, &kObjectData[5445], 0},
    {"id-GostR3411-94-CryptoProParamSet", "id-GostR3411-94-CryptoProParamSet",
     NID_id_GostR3411_94_CryptoProParamSet, 7, &kObjectData[5452], 0},
    {"id-Gost28147-89-TestParamSet", "id-Gost28147-89-TestParamSet",
     NID_id_Gost28147_89_TestParamSet, 7, &kObjectData[5459], 0},
    {"id-Gost28147-89-CryptoPro-A-ParamSet",
     "id-Gost28147-89-CryptoPro-A-ParamSet",
     NID_id_Gost28147_89_CryptoPro_A_ParamSet, 7, &kObjectData[5466], 0},
    {"id-Gost28147-89-CryptoPro-B-ParamSet",
     "id-Gost28147-89-CryptoPro-B-ParamSet",
     NID_id_Gost28147_89_CryptoPro_B_ParamSet, 7, &kObjectData[5473], 0},
    {"id-Gost28147-89-CryptoPro-C-ParamSet",
     "id-Gost28147-89-CryptoPro-C-ParamSet",
     NID_id_Gost28147_89_CryptoPro_C_ParamSet, 7, &kObjectData[5480], 0},
    {"id-Gost28147-89-CryptoPro-D-ParamSet",
     "id-Gost28147-89-CryptoPro-D-ParamSet",
     NID_id_Gost28147_89_CryptoPro_D_ParamSet, 7, &kObjectData[5487], 0},
    {"id-Gost28147-89-CryptoPro-Oscar-1-1-ParamSet",
     "id-Gost28147-89-CryptoPro-Oscar-1-1-ParamSet",
     NID_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet, 7, &kObjectData[5494],
     0},
    {"id-Gost28147-89-CryptoPro-Oscar-1-0-ParamSet",
     "id-Gost28147-89-CryptoPro-Oscar-1-0-ParamSet",
     NID_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet, 7, &kObjectData[5501],
     0},
    {"id-Gost28147-89-CryptoPro-RIC-1-ParamSet",
     "id-Gost28147-89-CryptoPro-RIC-1-ParamSet",
     NID_id_Gost28147_89_CryptoPro_RIC_1_ParamSet, 7, &kObjectData[5508], 0},
    {"id-GostR3410-94-TestParamSet", "id-GostR3410-94-TestParamSet",
     NID_id_GostR3410_94_TestParamSet, 7, &kObjectData[5515], 0},
    {"id-GostR3410-94-CryptoPro-A-ParamSet",
     "id-GostR3410-94-CryptoPro-A-ParamSet",
     NID_id_GostR3410_94_CryptoPro_A_ParamSet, 7, &kObjectData[5522], 0},
    {"id-GostR3410-94-CryptoPro-B-ParamSet",
     "id-GostR3410-94-CryptoPro-B-ParamSet",
     NID_id_GostR3410_94_CryptoPro_B_ParamSet, 7, &kObjectData[5529], 0},
    {"id-GostR3410-94-CryptoPro-C-ParamSet",
     "id-GostR3410-94-CryptoPro-C-ParamSet",
     NID_id_GostR3410_94_CryptoPro_C_ParamSet, 7, &kObjectData[5536], 0},
    {"id-GostR3410-94-CryptoPro-D-ParamSet",
     "id-GostR3410-94-CryptoPro-D-ParamSet",
     NID_id_GostR3410_94_CryptoPro_D_ParamSet, 7, &kObjectData[5543], 0},
    {"id-GostR3410-94-CryptoPro-XchA-ParamSet",
     "id-GostR3410-94-CryptoPro-XchA-ParamSet",
     NID_id_GostR3410_94_CryptoPro_XchA_ParamSet, 7, &kObjectData[5550], 0},
    {"id-GostR3410-94-CryptoPro-XchB-ParamSet",
     "id-GostR3410-94-CryptoPro-XchB-ParamSet",
     NID_id_GostR3410_94_CryptoPro_XchB_ParamSet, 7, &kObjectData[5557], 0},
    {"id-GostR3410-94-CryptoPro-XchC-ParamSet",
     "id-GostR3410-94-CryptoPro-XchC-ParamSet",
     NID_id_GostR3410_94_CryptoPro_XchC_ParamSet, 7, &kObjectData[5564], 0},
    {"id-GostR3410-2001-TestParamSet", "id-GostR3410-2001-TestParamSet",
     NID_id_GostR3410_2001_TestParamSet, 7, &kObjectData[5571], 0},
    {"id-GostR3410-2001-CryptoPro-A-ParamSet",
     "id-GostR3410-2001-CryptoPro-A-ParamSet",
     NID_id_GostR3410_2001_CryptoPro_A_ParamSet, 7, &kObjectData[5578], 0},
    {"id-GostR3410-2001-CryptoPro-B-ParamSet",
     "id-GostR3410-2001-CryptoPro-B-ParamSet",
     NID_id_GostR3410_2001_CryptoPro_B_ParamSet, 7, &kObjectData[5585], 0},
    {"id-GostR3410-2001-CryptoPro-C-ParamSet",
     "id-GostR3410-2001-CryptoPro-C-ParamSet",
     NID_id_GostR3410_2001_CryptoPro_C_ParamSet, 7, &kObjectData[5592], 0},
    {"id-GostR3410-2001-CryptoPro-XchA-ParamSet",
     "id-GostR3410-2001-CryptoPro-XchA-ParamSet",
     NID_id_GostR3410_2001_CryptoPro_XchA_ParamSet, 7, &kObjectData[5599], 0},
    {"id-GostR3410-2001-CryptoPro-XchB-ParamSet",
     "id-GostR3410-2001-CryptoPro-XchB-ParamSet",
     NID_id_GostR3410_2001_CryptoPro_XchB_ParamSet, 7, &kObjectData[5606], 0},
    {"id-GostR3410-94-a", "id-GostR3410-94-a", NID_id_GostR3410_94_a, 7,
     &kObjectData[5613], 0},
    {"id-GostR3410-94-aBis", "id-GostR3410-94-aBis", NID_id_GostR3410_94_aBis,
     7, &kObjectData[5620], 0},
    {"id-GostR3410-94-b", "id-GostR3410-94-b", NID_id_GostR3410_94_b, 7,
     &kObjectData[5627], 0},
    {"id-GostR3410-94-bBis", "id-GostR3410-94-bBis", NID_id_GostR3410_94_bBis,
     7, &kObjectData[5634], 0},
    {"id-Gost28147-89-cc", "GOST 28147-89 Cryptocom ParamSet",
     NID_id_Gost28147_89_cc, 8, &kObjectData[5641], 0},
    {"gost94cc", "GOST 34.10-94 Cryptocom", NID_id_GostR3410_94_cc, 8,
     &kObjectData[5649], 0},
    {"gost2001cc", "GOST 34.10-2001 Cryptocom", NID_id_GostR3410_2001_cc, 8,
     &kObjectData[5657], 0},
    {"id-GostR3411-94-with-GostR3410-94-cc",
     "GOST R 34.11-94 with GOST R 34.10-94 Cryptocom",
     NID_id_GostR3411_94_with_GostR3410_94_cc, 8, &kObjectData[5665], 0},
    {"id-GostR3411-94-with-GostR3410-2001-cc",
     "GOST R 34.11-94 with GOST R 34.10-2001 Cryptocom",
     NID_id_GostR3411_94_with_GostR3410_2001_cc, 8, &kObjectData[5673], 0},
    {"id-GostR3410-2001-ParamSet-cc",
     "GOST R 3410-2001 Parameter Set Cryptocom",
     NID_id_GostR3410_2001_ParamSet_cc, 8, &kObjectData[5681], 0},
    {"HMAC", "hmac", NID_hmac, 0, NULL, 0},
    {"LocalKeySet", "Microsoft Local Key set", NID_LocalKeySet, 9,
     &kObjectData[5689], 0},
    {"freshestCRL", "X509v3 Freshest CRL", NID_freshest_crl, 3,
     &kObjectData[5698], 0},
    {"id-on-permanentIdentifier", "Permanent Identifier",
     NID_id_on_permanentIdentifier, 8, &kObjectData[5701], 0},
    {"searchGuide", "searchGuide", NID_searchGuide, 3, &kObjectData[5709], 0},
    {"businessCategory", "businessCategory", NID_businessCategory, 3,
     &kObjectData[5712], 0},
    {"postalAddress", "postalAddress", NID_postalAddress, 3, &kObjectData[5715],
     0},
    {"postOfficeBox", "postOfficeBox", NID_postOfficeBox, 3, &kObjectData[5718],
     0},
    {"physicalDeliveryOfficeName", "physicalDeliveryOfficeName",
     NID_physicalDeliveryOfficeName, 3, &kObjectData[5721], 0},
    {"telephoneNumber", "telephoneNumber", NID_telephoneNumber, 3,
     &kObjectData[5724], 0},
    {"telexNumber", "telexNumber", NID_telexNumber, 3, &kObjectData[5727], 0},
    {"teletexTerminalIdentifier", "teletexTerminalIdentifier",
     NID_teletexTerminalIdentifier, 3, &kObjectData[5730], 0},
    {"facsimileTelephoneNumber", "facsimileTelephoneNumber",
     NID_facsimileTelephoneNumber, 3, &kObjectData[5733], 0},
    {"x121Address", "x121Address", NID_x121Address, 3, &kObjectData[5736], 0},
    {"internationaliSDNNumber", "internationaliSDNNumber",
     NID_internationaliSDNNumber, 3, &kObjectData[5739], 0},
    {"registeredAddress", "registeredAddress", NID_registeredAddress, 3,
     &kObjectData[5742], 0},
    {"destinationIndicator", "destinationIndicator", NID_destinationIndicator,
     3, &kObjectData[5745], 0},
    {"preferredDeliveryMethod", "preferredDeliveryMethod",
     NID_preferredDeliveryMethod, 3, &kObjectData[5748], 0},
    {"presentationAddress", "presentationAddress", NID_presentationAddress, 3,
     &kObjectData[5751], 0},
    {"supportedApplicationContext", "supportedApplicationContext",
     NID_supportedApplicationContext, 3, &kObjectData[5754], 0},
    {"member", "member", NID_member, 3, &kObjectData[5757], 0},
    {"owner", "owner", NID_owner, 3, &kObjectData[5760], 0},
    {"roleOccupant", "roleOccupant", NID_roleOccupant, 3, &kObjectData[5763],
     0},
    {"seeAlso", "seeAlso", NID_seeAlso, 3, &kObjectData[5766], 0},
    {"userPassword", "userPassword", NID_userPassword, 3, &kObjectData[5769],
     0},
    {"userCertificate", "userCertificate", NID_userCertificate, 3,
     &kObjectData[5772], 0},
    {"cACertificate", "cACertificate", NID_cACertificate, 3, &kObjectData[5775],
     0},
    {"authorityRevocationList", "authorityRevocationList",
     NID_authorityRevocationList, 3, &kObjectData[5778], 0},
    {"certificateRevocationList", "certificateRevocationList",
     NID_certificateRevocationList, 3, &kObjectData[5781], 0},
    {"crossCertificatePair", "crossCertificatePair", NID_crossCertificatePair,
     3, &kObjectData[5784], 0},
    {"enhancedSearchGuide", "enhancedSearchGuide", NID_enhancedSearchGuide, 3,
     &kObjectData[5787], 0},
    {"protocolInformation", "protocolInformation", NID_protocolInformation, 3,
     &kObjectData[5790], 0},
    {"distinguishedName", "distinguishedName", NID_distinguishedName, 3,
     &kObjectData[5793], 0},
    {"uniqueMember", "uniqueMember", NID_uniqueMember, 3, &kObjectData[5796],
     0},
    {"houseIdentifier", "houseIdentifier", NID_houseIdentifier, 3,
     &kObjectData[5799], 0},
    {"supportedAlgorithms", "supportedAlgorithms", NID_supportedAlgorithms, 3,
     &kObjectData[5802], 0},
    {"deltaRevocationList", "deltaRevocationList", NID_deltaRevocationList, 3,
     &kObjectData[5805], 0},
    {"dmdName", "dmdName", NID_dmdName, 3, &kObjectData[5808], 0},
    {"id-alg-PWRI-KEK", "id-alg-PWRI-KEK", NID_id_alg_PWRI_KEK, 11,
     &kObjectData[5811], 0},
    {"CMAC", "cmac", NID_cmac, 0, NULL, 0},
    {"id-aes128-GCM", "aes-128-gcm", NID_aes_128_gcm, 9, &kObjectData[5822], 0},
    {"id-aes128-CCM", "aes-128-ccm", NID_aes_128_ccm, 9, &kObjectData[5831], 0},
    {"id-aes128-wrap-pad", "id-aes128-wrap-pad", NID_id_aes128_wrap_pad, 9,
     &kObjectData[5840], 0},
    {"id-aes192-GCM", "aes-192-gcm", NID_aes_192_gcm, 9, &kObjectData[5849], 0},
    {"id-aes192-CCM", "aes-192-ccm", NID_aes_192_ccm, 9, &kObjectData[5858], 0},
    {"id-aes192-wrap-pad", "id-aes192-wrap-pad", NID_id_aes192_wrap_pad, 9,
     &kObjectData[5867], 0},
    {"id-aes256-GCM", "aes-256-gcm", NID_aes_256_gcm, 9, &kObjectData[5876], 0},
    {"id-aes256-CCM", "aes-256-ccm", NID_aes_256_ccm, 9, &kObjectData[5885], 0},
    {"id-aes256-wrap-pad", "id-aes256-wrap-pad", NID_id_aes256_wrap_pad, 9,
     &kObjectData[5894], 0},
    {"AES-128-CTR", "aes-128-ctr", NID_aes_128_ctr, 0, NULL, 0},
    {"AES-192-CTR", "aes-192-ctr", NID_aes_192_ctr, 0, NULL, 0},
    {"AES-256-CTR", "aes-256-ctr", NID_aes_256_ctr, 0, NULL, 0},
    {"id-camellia128-wrap", "id-camellia128-wrap", NID_id_camellia128_wrap, 11,
     &kObjectData[5903], 0},
    {"id-camellia192-wrap", "id-camellia192-wrap", NID_id_camellia192_wrap, 11,
     &kObjectData[5914], 0},
    {"id-camellia256-wrap", "id-camellia256-wrap", NID_id_camellia256_wrap, 11,
     &kObjectData[5925], 0},
    {"anyExtendedKeyUsage", "Any Extended Key Usage", NID_anyExtendedKeyUsage,
     4, &kObjectData[5936], 0},
    {"MGF1", "mgf1", NID_mgf1, 9, &kObjectData[5940], 0},
    {"RSASSA-PSS", "rsassaPss", NID_rsassaPss, 9, &kObjectData[5949], 0},
    {"AES-128-XTS", "aes-128-xts", NID_aes_128_xts, 0, NULL, 0},
    {"AES-256-XTS", "aes-256-xts", NID_aes_256_xts, 0, NULL, 0},
    {"RC4-HMAC-MD5", "rc4-hmac-md5", NID_rc4_hmac_md5, 0, NULL, 0},
    {"AES-128-CBC-HMAC-SHA1", "aes-128-cbc-hmac-sha1",
     NID_aes_128_cbc_hmac_sha1, 0, NULL, 0},
    {"AES-192-CBC-HMAC-SHA1", "aes-192-cbc-hmac-sha1",
     NID_aes_192_cbc_hmac_sha1, 0, NULL, 0},
    {"AES-256-CBC-HMAC-SHA1", "aes-256-cbc-hmac-sha1",
     NID_aes_256_cbc_hmac_sha1, 0, NULL, 0},
    {"RSAES-OAEP", "rsaesOaep", NID_rsaesOaep, 9, &kObjectData[5958], 0},
    {"dhpublicnumber", "X9.42 DH", NID_dhpublicnumber, 7, &kObjectData[5967],
     0},
    {"brainpoolP160r1", "brainpoolP160r1", NID_brainpoolP160r1, 9,
     &kObjectData[5974], 0},
    {"brainpoolP160t1", "brainpoolP160t1", NID_brainpoolP160t1, 9,
     &kObjectData[5983], 0},
    {"brainpoolP192r1", "brainpoolP192r1", NID_brainpoolP192r1, 9,
     &kObjectData[5992], 0},
    {"brainpoolP192t1", "brainpoolP192t1", NID_brainpoolP192t1, 9,
     &kObjectData[6001], 0},
    {"brainpoolP224r1", "brainpoolP224r1", NID_brainpoolP224r1, 9,
     &kObjectData[6010], 0},
    {"brainpoolP224t1", "brainpoolP224t1", NID_brainpoolP224t1, 9,
     &kObjectData[6019], 0},
    {"brainpoolP256r1", "brainpoolP256r1", NID_brainpoolP256r1, 9,
     &kObjectData[6028], 0},
    {"brainpoolP256t1", "brainpoolP256t1", NID_brainpoolP256t1, 9,
     &kObjectData[6037], 0},
    {"brainpoolP320r1", "brainpoolP320r1", NID_brainpoolP320r1, 9,
     &kObjectData[6046], 0},
    {"brainpoolP320t1", "brainpoolP320t1", NID_brainpoolP320t1, 9,
     &kObjectData[6055], 0},
    {"brainpoolP384r1", "brainpoolP384r1", NID_brainpoolP384r1, 9,
     &kObjectData[6064], 0},
    {"brainpoolP384t1", "brainpoolP384t1", NID_brainpoolP384t1, 9,
     &kObjectData[6073], 0},
    {"brainpoolP512r1", "brainpoolP512r1", NID_brainpoolP512r1, 9,
     &kObjectData[6082], 0},
    {"brainpoolP512t1", "brainpoolP512t1", NID_brainpoolP512t1, 9,
     &kObjectData[6091], 0},
    {"PSPECIFIED", "pSpecified", NID_pSpecified, 9, &kObjectData[6100], 0},
    {"dhSinglePass-stdDH-sha1kdf-scheme", "dhSinglePass-stdDH-sha1kdf-scheme",
     NID_dhSinglePass_stdDH_sha1kdf_scheme, 9, &kObjectData[6109], 0},
    {"dhSinglePass-stdDH-sha224kdf-scheme",
     "dhSinglePass-stdDH-sha224kdf-scheme",
     NID_dhSinglePass_stdDH_sha224kdf_scheme, 6, &kObjectData[6118], 0},
    {"dhSinglePass-stdDH-sha256kdf-scheme",
     "dhSinglePass-stdDH-sha256kdf-scheme",
     NID_dhSinglePass_stdDH_sha256kdf_scheme, 6, &kObjectData[6124], 0},
    {"dhSinglePass-stdDH-sha384kdf-scheme",
     "dhSinglePass-stdDH-sha384kdf-scheme",
     NID_dhSinglePass_stdDH_sha384kdf_scheme, 6, &kObjectData[6130], 0},
    {"dhSinglePass-stdDH-sha512kdf-scheme",
     "dhSinglePass-stdDH-sha512kdf-scheme",
     NID_dhSinglePass_stdDH_sha512kdf_scheme, 6, &kObjectData[6136], 0},
    {"dhSinglePass-cofactorDH-sha1kdf-scheme",
     "dhSinglePass-cofactorDH-sha1kdf-scheme",
     NID_dhSinglePass_cofactorDH_sha1kdf_scheme, 9, &kObjectData[6142], 0},
    {"dhSinglePass-cofactorDH-sha224kdf-scheme",
     "dhSinglePass-cofactorDH-sha224kdf-scheme",
     NID_dhSinglePass_cofactorDH_sha224kdf_scheme, 6, &kObjectData[6151], 0},
    {"dhSinglePass-cofactorDH-sha256kdf-scheme",
     "dhSinglePass-cofactorDH-sha256kdf-scheme",
     NID_dhSinglePass_cofactorDH_sha256kdf_scheme, 6, &kObjectData[6157], 0},
    {"dhSinglePass-cofactorDH-sha384kdf-scheme",
     "dhSinglePass-cofactorDH-sha384kdf-scheme",
     NID_dhSinglePass_cofactorDH_sha384kdf_scheme, 6, &kObjectData[6163], 0},
    {"dhSinglePass-cofactorDH-sha512kdf-scheme",
     "dhSinglePass-cofactorDH-sha512kdf-scheme",
     NID_dhSinglePass_cofactorDH_sha512kdf_scheme, 6, &kObjectData[6169], 0},
    {"dh-std-kdf", "dh-std-kdf", NID_dh_std_kdf, 0, NULL, 0},
    {"dh-cofactor-kdf", "dh-cofactor-kdf", NID_dh_cofactor_kdf, 0, NULL, 0},
    {"X25519", "X25519", NID_X25519, 0, NULL, 0},
    {"ED25519", "ED25519", NID_ED25519, 3, &kObjectData[6175], 0},
    {"ChaCha20-Poly1305", "chacha20-poly1305", NID_chacha20_poly1305, 0, NULL,
     0},
    {"KxRSA", "kx-rsa", NID_kx_rsa, 0, NULL, 0},
    {"KxECDHE", "kx-ecdhe", NID_kx_ecdhe, 0, NULL, 0},
    {"KxPSK", "kx-psk", NID_kx_psk, 0, NULL, 0},
    {"AuthRSA", "auth-rsa", NID_auth_rsa, 0, NULL, 0},
    {"AuthECDSA", "auth-ecdsa", NID_auth_ecdsa, 0, NULL, 0},
    {"AuthPSK", "auth-psk", NID_auth_psk, 0, NULL, 0},
    {"KxANY", "kx-any", NID_kx_any, 0, NULL, 0},
    {"AuthANY", "auth-any", NID_auth_any, 0, NULL, 0},
    {"CECPQ2", "CECPQ2", NID_CECPQ2, 0, NULL, 0},
    {"ED448", "ED448", NID_ED448, 3, &kObjectData[6178], 0},
};

static const uint16_t kNIDsInShortNameOrder[] = {
    364 /* AD_DVCS */,
    419 /* AES-128-CBC */,
    916 /* AES-128-CBC-HMAC-SHA1 */,
    421 /* AES-128-CFB */,
    650 /* AES-128-CFB1 */,
    653 /* AES-128-CFB8 */,
    904 /* AES-128-CTR */,
    418 /* AES-128-ECB */,
    420 /* AES-128-OFB */,
    913 /* AES-128-XTS */,
    423 /* AES-192-CBC */,
    917 /* AES-192-CBC-HMAC-SHA1 */,
    425 /* AES-192-CFB */,
    651 /* AES-192-CFB1 */,
    654 /* AES-192-CFB8 */,
    905 /* AES-192-CTR */,
    422 /* AES-192-ECB */,
    424 /* AES-192-OFB */,
    427 /* AES-256-CBC */,
    918 /* AES-256-CBC-HMAC-SHA1 */,
    429 /* AES-256-CFB */,
    652 /* AES-256-CFB1 */,
    655 /* AES-256-CFB8 */,
    906 /* AES-256-CTR */,
    426 /* AES-256-ECB */,
    428 /* AES-256-OFB */,
    914 /* AES-256-XTS */,
    958 /* AuthANY */,
    955 /* AuthECDSA */,
    956 /* AuthPSK */,
    954 /* AuthRSA */,
    91 /* BF-CBC */,
    93 /* BF-CFB */,
    92 /* BF-ECB */,
    94 /* BF-OFB */,
    14 /* C */,
    751 /* CAMELLIA-128-CBC */,
    757 /* CAMELLIA-128-CFB */,
    760 /* CAMELLIA-128-CFB1 */,
    763 /* CAMELLIA-128-CFB8 */,
    754 /* CAMELLIA-128-ECB */,
    766 /* CAMELLIA-128-OFB */,
    752 /* CAMELLIA-192-CBC */,
    758 /* CAMELLIA-192-CFB */,
    761 /* CAMELLIA-192-CFB1 */,
    764 /* CAMELLIA-192-CFB8 */,
    755 /* CAMELLIA-192-ECB */,
    767 /* CAMELLIA-192-OFB */,
    753 /* CAMELLIA-256-CBC */,
    759 /* CAMELLIA-256-CFB */,
    762 /* CAMELLIA-256-CFB1 */,
    765 /* CAMELLIA-256-CFB8 */,
    756 /* CAMELLIA-256-ECB */,
    768 /* CAMELLIA-256-OFB */,
    108 /* CAST5-CBC */,
    110 /* CAST5-CFB */,
    109 /* CAST5-ECB */,
    111 /* CAST5-OFB */,
    959 /* CECPQ2 */,
    894 /* CMAC */,
    13 /* CN */,
    141 /* CRLReason */,
    417 /* CSPName */,
    950 /* ChaCha20-Poly1305 */,
    367 /* CrlID */,
    391 /* DC */,
    31 /* DES-CBC */,
    643 /* DES-CDMF */,
    30 /* DES-CFB */,
    656 /* DES-CFB1 */,
    657 /* DES-CFB8 */,
    29 /* DES-ECB */,
    32 /* DES-EDE */,
    43 /* DES-EDE-CBC */,
    60 /* DES-EDE-CFB */,
    62 /* DES-EDE-OFB */,
    33 /* DES-EDE3 */,
    44 /* DES-EDE3-CBC */,
    61 /* DES-EDE3-CFB */,
    658 /* DES-EDE3-CFB1 */,
    659 /* DES-EDE3-CFB8 */,
    63 /* DES-EDE3-OFB */,
    45 /* DES-OFB */,
    80 /* DESX-CBC */,
    380 /* DOD */,
    116 /* DSA */,
    66 /* DSA-SHA */,
    113 /* DSA-SHA1 */,
    70 /* DSA-SHA1-old */,
    67 /* DSA-old */,
    297 /* DVCS */,
    949 /* ED25519 */,
    960 /* ED448 */,
    99 /* GN */,
    855 /* HMAC */,
    780 /* HMAC-MD5 */,
    781 /* HMAC-SHA1 */,
    381 /* IANA */,
    34 /* IDEA-CBC */,
    35 /* IDEA-CFB */,
    36 /* IDEA-ECB */,
    46 /* IDEA-OFB */,
    181 /* ISO */,
    183 /* ISO-US */,
    645 /* ITU-T */,
    646 /* JOINT-ISO-ITU-T */,
    773 /* KISA */,
    957 /* KxANY */,
    952 /* KxECDHE */,
    953 /* KxPSK */,
    951 /* KxRSA */,
    15 /* L */,
    856 /* LocalKeySet */,
    3 /* MD2 */,
    257 /* MD4 */,
    4 /* MD5 */,
    114 /* MD5-SHA1 */,
    95 /* MDC2 */,
    911 /* MGF1 */,
    388 /* Mail */,
    57 /* Netscape */,
    366 /* Nonce */,
    17 /* O */,
    178 /* OCSP */,
    180 /* OCSPSigning */,
    379 /* ORG */,
    18 /* OU */,
    749 /* Oakley-EC2N-3 */,
    750 /* Oakley-EC2N-4 */,
    9 /* PBE-MD2-DES */,
    168 /* PBE-MD2-RC2-64 */,
    10 /* PBE-MD5-DES */,
    169 /* PBE-MD5-RC2-64 */,
    147 /* PBE-SHA1-2DES */,
    146 /* PBE-SHA1-3DES */,
    170 /* PBE-SHA1-DES */,
    148 /* PBE-SHA1-RC2-128 */,
    149 /* PBE-SHA1-RC2-40 */,
    68 /* PBE-SHA1-RC2-64 */,
    144 /* PBE-SHA1-RC4-128 */,
    145 /* PBE-SHA1-RC4-40 */,
    161 /* PBES2 */,
    69 /* PBKDF2 */,
    162 /* PBMAC1 */,
    127 /* PKIX */,
    935 /* PSPECIFIED */,
    98 /* RC2-40-CBC */,
    166 /* RC2-64-CBC */,
    37 /* RC2-CBC */,
    39 /* RC2-CFB */,
    38 /* RC2-ECB */,
    40 /* RC2-OFB */,
    5 /* RC4 */,
    97 /* RC4-40 */,
    915 /* RC4-HMAC-MD5 */,
    120 /* RC5-CBC */,
    122 /* RC5-CFB */,
    121 /* RC5-ECB */,
    123 /* RC5-OFB */,
    117 /* RIPEMD160 */,
    19 /* RSA */,
    7 /* RSA-MD2 */,
    396 /* RSA-MD4 */,
    8 /* RSA-MD5 */,
    96 /* RSA-MDC2 */,
    104 /* RSA-NP-MD5 */,
    119 /* RSA-RIPEMD160 */,
    42 /* RSA-SHA */,
    65 /* RSA-SHA1 */,
    115 /* RSA-SHA1-2 */,
    671 /* RSA-SHA224 */,
    668 /* RSA-SHA256 */,
    669 /* RSA-SHA384 */,
    670 /* RSA-SHA512 */,
    919 /* RSAES-OAEP */,
    912 /* RSASSA-PSS */,
    777 /* SEED-CBC */,
    779 /* SEED-CFB */,
    776 /* SEED-ECB */,
    778 /* SEED-OFB */,
    41 /* SHA */,
    64 /* SHA1 */,
    675 /* SHA224 */,
    672 /* SHA256 */,
    673 /* SHA384 */,
    674 /* SHA512 */,
    188 /* SMIME */,
    167 /* SMIME-CAPS */,
    100 /* SN */,
    16 /* ST */,
    143 /* SXNetID */,
    458 /* UID */,
    0 /* UNDEF */,
    948 /* X25519 */,
    11 /* X500 */,
    378 /* X500algorithms */,
    12 /* X509 */,
    184 /* X9-57 */,
    185 /* X9cm */,
    125 /* ZLIB */,
    478 /* aRecord */,
    289 /* aaControls */,
    287 /* ac-auditEntity */,
    397 /* ac-proxying */,
    288 /* ac-targeting */,
    368 /* acceptableResponses */,
    446 /* account */,
    363 /* ad_timestamping */,
    376 /* algorithm */,
    405 /* ansi-X9-62 */,
    910 /* anyExtendedKeyUsage */,
    746 /* anyPolicy */,
    370 /* archiveCutoff */,
    484 /* associatedDomain */,
    485 /* associatedName */,
    501 /* audio */,
    177 /* authorityInfoAccess */,
    90 /* authorityKeyIdentifier */,
    882 /* authorityRevocationList */,
    87 /* basicConstraints */,
    365 /* basicOCSPResponse */,
    285 /* biometricInfo */,
    921 /* brainpoolP160r1 */,
    922 /* brainpoolP160t1 */,
    923 /* brainpoolP192r1 */,
    924 /* brainpoolP192t1 */,
    925 /* brainpoolP224r1 */,
    926 /* brainpoolP224t1 */,
    927 /* brainpoolP256r1 */,
    928 /* brainpoolP256t1 */,
    929 /* brainpoolP320r1 */,
    930 /* brainpoolP320t1 */,
    931 /* brainpoolP384r1 */,
    932 /* brainpoolP384t1 */,
    933 /* brainpoolP512r1 */,
    934 /* brainpoolP512t1 */,
    494 /* buildingName */,
    860 /* businessCategory */,
    691 /* c2onb191v4 */,
    692 /* c2onb191v5 */,
    697 /* c2onb239v4 */,
    698 /* c2onb239v5 */,
    684 /* c2pnb163v1 */,
    685 /* c2pnb163v2 */,
    686 /* c2pnb163v3 */,
    687 /* c2pnb176v1 */,
    693 /* c2pnb208w1 */,
    699 /* c2pnb272w1 */,
    700 /* c2pnb304w1 */,
    702 /* c2pnb368w1 */,
    688 /* c2tnb191v1 */,
    689 /* c2tnb191v2 */,
    690 /* c2tnb191v3 */,
    694 /* c2tnb239v1 */,
    695 /* c2tnb239v2 */,
    696 /* c2tnb239v3 */,
    701 /* c2tnb359v1 */,
    703 /* c2tnb431r1 */,
    881 /* cACertificate */,
    483 /* cNAMERecord */,
    179 /* caIssuers */,
    785 /* caRepository */,
    443 /* caseIgnoreIA5StringSyntax */,
    152 /* certBag */,
    677 /* certicom-arc */,
    771 /* certificateIssuer */,
    89 /* certificatePolicies */,
    883 /* certificateRevocationList */,
    54 /* challengePassword */,
    407 /* characteristic-two-field */,
    395 /* clearance */,
    130 /* clientAuth */,
    131 /* codeSigning */,
    50 /* contentType */,
    53 /* countersignature */,
    153 /* crlBag */,
    103 /* crlDistributionPoints */,
    88 /* crlNumber */,
    884 /* crossCertificatePair */,
    806 /* cryptocom */,
    805 /* cryptopro */,
    500 /* dITRedirect */,
    451 /* dNSDomain */,
    495 /* dSAQuality */,
    434 /* data */,
    390 /* dcobject */,
    140 /* deltaCRL */,
    891 /* deltaRevocationList */,
    107 /* description */,
    871 /* destinationIndicator */,
    947 /* dh-cofactor-kdf */,
    946 /* dh-std-kdf */,
    28 /* dhKeyAgreement */,
    941 /* dhSinglePass-cofactorDH-sha1kdf-scheme */,
    942 /* dhSinglePass-cofactorDH-sha224kdf-scheme */,
    943 /* dhSinglePass-cofactorDH-sha256kdf-scheme */,
    944 /* dhSinglePass-cofactorDH-sha384kdf-scheme */,
    945 /* dhSinglePass-cofactorDH-sha512kdf-scheme */,
    936 /* dhSinglePass-stdDH-sha1kdf-scheme */,
    937 /* dhSinglePass-stdDH-sha224kdf-scheme */,
    938 /* dhSinglePass-stdDH-sha256kdf-scheme */,
    939 /* dhSinglePass-stdDH-sha384kdf-scheme */,
    940 /* dhSinglePass-stdDH-sha512kdf-scheme */,
    920 /* dhpublicnumber */,
    382 /* directory */,
    887 /* distinguishedName */,
    892 /* dmdName */,
    174 /* dnQualifier */,
    447 /* document */,
    471 /* documentAuthor */,
    468 /* documentIdentifier */,
    472 /* documentLocation */,
    502 /* documentPublisher */,
    449 /* documentSeries */,
    469 /* documentTitle */,
    470 /* documentVersion */,
    392 /* domain */,
    452 /* domainRelatedObject */,
    802 /* dsa_with_SHA224 */,
    803 /* dsa_with_SHA256 */,
    791 /* ecdsa-with-Recommended */,
    416 /* ecdsa-with-SHA1 */,
    793 /* ecdsa-with-SHA224 */,
    794 /* ecdsa-with-SHA256 */,
    795 /* ecdsa-with-SHA384 */,
    796 /* ecdsa-with-SHA512 */,
    792 /* ecdsa-with-Specified */,
    48 /* emailAddress */,
    132 /* emailProtection */,
    885 /* enhancedSearchGuide */,
    389 /* enterprises */,
    384 /* experimental */,
    172 /* extReq */,
    56 /* extendedCertificateAttributes */,
    126 /* extendedKeyUsage */,
    372 /* extendedStatus */,
    867 /* facsimileTelephoneNumber */,
    462 /* favouriteDrink */,
    857 /* freshestCRL */,
    453 /* friendlyCountry */,
    490 /* friendlyCountryName */,
    156 /* friendlyName */,
    509 /* generationQualifier */,
    815 /* gost-mac */,
    811 /* gost2001 */,
    851 /* gost2001cc */,
    813 /* gost89 */,
    814 /* gost89-cnt */,
    812 /* gost94 */,
    850 /* gost94cc */,
    797 /* hmacWithMD5 */,
    163 /* hmacWithSHA1 */,
    798 /* hmacWithSHA224 */,
    799 /* hmacWithSHA256 */,
    800 /* hmacWithSHA384 */,
    801 /* hmacWithSHA512 */,
    432 /* holdInstructionCallIssuer */,
    430 /* holdInstructionCode */,
    431 /* holdInstructionNone */,
    433 /* holdInstructionReject */,
    486 /* homePostalAddress */,
    473 /* homeTelephoneNumber */,
    466 /* host */,
    889 /* houseIdentifier */,
    442 /* iA5StringSyntax */,
    783 /* id-DHBasedMac */,
    824 /* id-Gost28147-89-CryptoPro-A-ParamSet */,
    825 /* id-Gost28147-89-CryptoPro-B-ParamSet */,
    826 /* id-Gost28147-89-CryptoPro-C-ParamSet */,
    827 /* id-Gost28147-89-CryptoPro-D-ParamSet */,
    819 /* id-Gost28147-89-CryptoPro-KeyMeshing */,
    829 /* id-Gost28147-89-CryptoPro-Oscar-1-0-ParamSet */,
    828 /* id-Gost28147-89-CryptoPro-Oscar-1-1-ParamSet */,
    830 /* id-Gost28147-89-CryptoPro-RIC-1-ParamSet */,
    820 /* id-Gost28147-89-None-KeyMeshing */,
    823 /* id-Gost28147-89-TestParamSet */,
    849 /* id-Gost28147-89-cc */,
    840 /* id-GostR3410-2001-CryptoPro-A-ParamSet */,
    841 /* id-GostR3410-2001-CryptoPro-B-ParamSet */,
    842 /* id-GostR3410-2001-CryptoPro-C-ParamSet */,
    843 /* id-GostR3410-2001-CryptoPro-XchA-ParamSet */,
    844 /* id-GostR3410-2001-CryptoPro-XchB-ParamSet */,
    854 /* id-GostR3410-2001-ParamSet-cc */,
    839 /* id-GostR3410-2001-TestParamSet */,
    817 /* id-GostR3410-2001DH */,
    832 /* id-GostR3410-94-CryptoPro-A-ParamSet */,
    833 /* id-GostR3410-94-CryptoPro-B-ParamSet */,
    834 /* id-GostR3410-94-CryptoPro-C-ParamSet */,
    835 /* id-GostR3410-94-CryptoPro-D-ParamSet */,
    836 /* id-GostR3410-94-CryptoPro-XchA-ParamSet */,
    837 /* id-GostR3410-94-CryptoPro-XchB-ParamSet */,
    838 /* id-GostR3410-94-CryptoPro-XchC-ParamSet */,
    831 /* id-GostR3410-94-TestParamSet */,
    845 /* id-GostR3410-94-a */,
    846 /* id-GostR3410-94-aBis */,
    847 /* id-GostR3410-94-b */,
    848 /* id-GostR3410-94-bBis */,
    818 /* id-GostR3410-94DH */,
    822 /* id-GostR3411-94-CryptoProParamSet */,
    821 /* id-GostR3411-94-TestParamSet */,
    807 /* id-GostR3411-94-with-GostR3410-2001 */,
    853 /* id-GostR3411-94-with-GostR3410-2001-cc */,
    808 /* id-GostR3411-94-with-GostR3410-94 */,
    852 /* id-GostR3411-94-with-GostR3410-94-cc */,
    810 /* id-HMACGostR3411-94 */,
    782 /* id-PasswordBasedMAC */,
    266 /* id-aca */,
    355 /* id-aca-accessIdentity */,
    354 /* id-aca-authenticationInfo */,
    356 /* id-aca-chargingIdentity */,
    399 /* id-aca-encAttrs */,
    357 /* id-aca-group */,
    358 /* id-aca-role */,
    176 /* id-ad */,
    896 /* id-aes128-CCM */,
    895 /* id-aes128-GCM */,
    788 /* id-aes128-wrap */,
    897 /* id-aes128-wrap-pad */,
    899 /* id-aes192-CCM */,
    898 /* id-aes192-GCM */,
    789 /* id-aes192-wrap */,
    900 /* id-aes192-wrap-pad */,
    902 /* id-aes256-CCM */,
    901 /* id-aes256-GCM */,
    790 /* id-aes256-wrap */,
    903 /* id-aes256-wrap-pad */,
    262 /* id-alg */,
    893 /* id-alg-PWRI-KEK */,
    323 /* id-alg-des40 */,
    326 /* id-alg-dh-pop */,
    325 /* id-alg-dh-sig-hmac-sha1 */,
    324 /* id-alg-noSignature */,
    907 /* id-camellia128-wrap */,
    908 /* id-camellia192-wrap */,
    909 /* id-camellia256-wrap */,
    268 /* id-cct */,
    361 /* id-cct-PKIData */,
    362 /* id-cct-PKIResponse */,
    360 /* id-cct-crs */,
    81 /* id-ce */,
    680 /* id-characteristic-two-basis */,
    263 /* id-cmc */,
    334 /* id-cmc-addExtensions */,
    346 /* id-cmc-confirmCertAcceptance */,
    330 /* id-cmc-dataReturn */,
    336 /* id-cmc-decryptedPOP */,
    335 /* id-cmc-encryptedPOP */,
    339 /* id-cmc-getCRL */,
    338 /* id-cmc-getCert */,
    328 /* id-cmc-identification */,
    329 /* id-cmc-identityProof */,
    337 /* id-cmc-lraPOPWitness */,
    344 /* id-cmc-popLinkRandom */,
    345 /* id-cmc-popLinkWitness */,
    343 /* id-cmc-queryPending */,
    333 /* id-cmc-recipientNonce */,
    341 /* id-cmc-regInfo */,
    342 /* id-cmc-responseInfo */,
    340 /* id-cmc-revokeRequest */,
    332 /* id-cmc-senderNonce */,
    327 /* id-cmc-statusInfo */,
    331 /* id-cmc-transactionId */,
    787 /* id-ct-asciiTextWithCRLF */,
    408 /* id-ecPublicKey */,
    508 /* id-hex-multipart-message */,
    507 /* id-hex-partial-message */,
    260 /* id-it */,
    302 /* id-it-caKeyUpdateInfo */,
    298 /* id-it-caProtEncCert */,
    311 /* id-it-confirmWaitTime */,
    303 /* id-it-currentCRL */,
    300 /* id-it-encKeyPairTypes */,
    310 /* id-it-implicitConfirm */,
    308 /* id-it-keyPairParamRep */,
    307 /* id-it-keyPairParamReq */,
    312 /* id-it-origPKIMessage */,
    301 /* id-it-preferredSymmAlg */,
    309 /* id-it-revPassphrase */,
    299 /* id-it-signKeyPairTypes */,
    305 /* id-it-subscriptionRequest */,
    306 /* id-it-subscriptionResponse */,
    784 /* id-it-suppLangTags */,
    304 /* id-it-unsupportedOIDs */,
    128 /* id-kp */,
    280 /* id-mod-attribute-cert */,
    274 /* id-mod-cmc */,
    277 /* id-mod-cmp */,
    284 /* id-mod-cmp2000 */,
    273 /* id-mod-crmf */,
    283 /* id-mod-dvcs */,
    275 /* id-mod-kea-profile-88 */,
    276 /* id-mod-kea-profile-93 */,
    282 /* id-mod-ocsp */,
    278 /* id-mod-qualified-cert-88 */,
    279 /* id-mod-qualified-cert-93 */,
    281 /* id-mod-timestamp-protocol */,
    264 /* id-on */,
    858 /* id-on-permanentIdentifier */,
    347 /* id-on-personalData */,
    265 /* id-pda */,
    352 /* id-pda-countryOfCitizenship */,
    353 /* id-pda-countryOfResidence */,
    348 /* id-pda-dateOfBirth */,
    351 /* id-pda-gender */,
    349 /* id-pda-placeOfBirth */,
    175 /* id-pe */,
    261 /* id-pkip */,
    258 /* id-pkix-mod */,
    269 /* id-pkix1-explicit-88 */,
    271 /* id-pkix1-explicit-93 */,
    270 /* id-pkix1-implicit-88 */,
    272 /* id-pkix1-implicit-93 */,
    662 /* id-ppl */,
    664 /* id-ppl-anyLanguage */,
    667 /* id-ppl-independent */,
    665 /* id-ppl-inheritAll */,
    267 /* id-qcs */,
    359 /* id-qcs-pkixQCSyntax-v1 */,
    259 /* id-qt */,
    164 /* id-qt-cps */,
    165 /* id-qt-unotice */,
    313 /* id-regCtrl */,
    316 /* id-regCtrl-authenticator */,
    319 /* id-regCtrl-oldCertID */,
    318 /* id-regCtrl-pkiArchiveOptions */,
    317 /* id-regCtrl-pkiPublicationInfo */,
    320 /* id-regCtrl-protocolEncrKey */,
    315 /* id-regCtrl-regToken */,
    314 /* id-regInfo */,
    322 /* id-regInfo-certReq */,
    321 /* id-regInfo-utf8Pairs */,
    512 /* id-set */,
    191 /* id-smime-aa */,
    215 /* id-smime-aa-contentHint */,
    218 /* id-smime-aa-contentIdentifier */,
    221 /* id-smime-aa-contentReference */,
    240 /* id-smime-aa-dvcs-dvc */,
    217 /* id-smime-aa-encapContentType */,
    222 /* id-smime-aa-encrypKeyPref */,
    220 /* id-smime-aa-equivalentLabels */,
    232 /* id-smime-aa-ets-CertificateRefs */,
    233 /* id-smime-aa-ets-RevocationRefs */,
    238 /* id-smime-aa-ets-archiveTimeStamp */,
    237 /* id-smime-aa-ets-certCRLTimestamp */,
    234 /* id-smime-aa-ets-certValues */,
    227 /* id-smime-aa-ets-commitmentType */,
    231 /* id-smime-aa-ets-contentTimestamp */,
    236 /* id-smime-aa-ets-escTimeStamp */,
    230 /* id-smime-aa-ets-otherSigCert */,
    235 /* id-smime-aa-ets-revocationValues */,
    226 /* id-smime-aa-ets-sigPolicyId */,
    229 /* id-smime-aa-ets-signerAttr */,
    228 /* id-smime-aa-ets-signerLocation */,
    219 /* id-smime-aa-macValue */,
    214 /* id-smime-aa-mlExpandHistory */,
    216 /* id-smime-aa-msgSigDigest */,
    212 /* id-smime-aa-receiptRequest */,
    213 /* id-smime-aa-securityLabel */,
    239 /* id-smime-aa-signatureType */,
    223 /* id-smime-aa-signingCertificate */,
    224 /* id-smime-aa-smimeEncryptCerts */,
    225 /* id-smime-aa-timeStampToken */,
    192 /* id-smime-alg */,
    243 /* id-smime-alg-3DESwrap */,
    246 /* id-smime-alg-CMS3DESwrap */,
    247 /* id-smime-alg-CMSRC2wrap */,
    245 /* id-smime-alg-ESDH */,
    241 /* id-smime-alg-ESDHwith3DES */,
    242 /* id-smime-alg-ESDHwithRC2 */,
    244 /* id-smime-alg-RC2wrap */,
    193 /* id-smime-cd */,
    248 /* id-smime-cd-ldap */,
    190 /* id-smime-ct */,
    210 /* id-smime-ct-DVCSRequestData */,
    211 /* id-smime-ct-DVCSResponseData */,
    208 /* id-smime-ct-TDTInfo */,
    207 /* id-smime-ct-TSTInfo */,
    205 /* id-smime-ct-authData */,
    786 /* id-smime-ct-compressedData */,
    209 /* id-smime-ct-contentInfo */,
    206 /* id-smime-ct-publishCert */,
    204 /* id-smime-ct-receipt */,
    195 /* id-smime-cti */,
    255 /* id-smime-cti-ets-proofOfApproval */,
    256 /* id-smime-cti-ets-proofOfCreation */,
    253 /* id-smime-cti-ets-proofOfDelivery */,
    251 /* id-smime-cti-ets-proofOfOrigin */,
    252 /* id-smime-cti-ets-proofOfReceipt */,
    254 /* id-smime-cti-ets-proofOfSender */,
    189 /* id-smime-mod */,
    196 /* id-smime-mod-cms */,
    197 /* id-smime-mod-ess */,
    202 /* id-smime-mod-ets-eSigPolicy-88 */,
    203 /* id-smime-mod-ets-eSigPolicy-97 */,
    200 /* id-smime-mod-ets-eSignature-88 */,
    201 /* id-smime-mod-ets-eSignature-97 */,
    199 /* id-smime-mod-msg-v3 */,
    198 /* id-smime-mod-oid */,
    194 /* id-smime-spq */,
    250 /* id-smime-spq-ets-sqt-unotice */,
    249 /* id-smime-spq-ets-sqt-uri */,
    676 /* identified-organization */,
    461 /* info */,
    748 /* inhibitAnyPolicy */,
    101 /* initials */,
    647 /* international-organizations */,
    869 /* internationaliSDNNumber */,
    142 /* invalidityDate */,
    294 /* ipsecEndSystem */,
    295 /* ipsecTunnel */,
    296 /* ipsecUser */,
    86 /* issuerAltName */,
    770 /* issuingDistributionPoint */,
    492 /* janetMailbox */,
    150 /* keyBag */,
    83 /* keyUsage */,
    477 /* lastModifiedBy */,
    476 /* lastModifiedTime */,
    157 /* localKeyID */,
    480 /* mXRecord */,
    460 /* mail */,
    493 /* mailPreferenceOption */,
    467 /* manager */,
    809 /* md_gost94 */,
    875 /* member */,
    182 /* member-body */,
    51 /* messageDigest */,
    383 /* mgmt */,
    504 /* mime-mhs */,
    506 /* mime-mhs-bodies */,
    505 /* mime-mhs-headings */,
    488 /* mobileTelephoneNumber */,
    136 /* msCTLSign */,
    135 /* msCodeCom */,
    134 /* msCodeInd */,
    138 /* msEFS */,
    171 /* msExtReq */,
    137 /* msSGC */,
    648 /* msSmartcardLogin */,
    649 /* msUPN */,
    481 /* nSRecord */,
    173 /* name */,
    666 /* nameConstraints */,
    369 /* noCheck */,
    403 /* noRevAvail */,
    72 /* nsBaseUrl */,
    76 /* nsCaPolicyUrl */,
    74 /* nsCaRevocationUrl */,
    58 /* nsCertExt */,
    79 /* nsCertSequence */,
    71 /* nsCertType */,
    78 /* nsComment */,
    59 /* nsDataType */,
    75 /* nsRenewalUrl */,
    73 /* nsRevocationUrl */,
    139 /* nsSGC */,
    77 /* nsSslServerName */,
    681 /* onBasis */,
    491 /* organizationalStatus */,
    475 /* otherMailbox */,
    876 /* owner */,
    489 /* pagerTelephoneNumber */,
    374 /* path */,
    112 /* pbeWithMD5AndCast5CBC */,
    499 /* personalSignature */,
    487 /* personalTitle */,
    464 /* photo */,
    863 /* physicalDeliveryOfficeName */,
    437 /* pilot */,
    439 /* pilotAttributeSyntax */,
    438 /* pilotAttributeType */,
    479 /* pilotAttributeType27 */,
    456 /* pilotDSA */,
    441 /* pilotGroups */,
    444 /* pilotObject */,
    440 /* pilotObjectClass */,
    455 /* pilotOrganization */,
    445 /* pilotPerson */,
    2 /* pkcs */,
    186 /* pkcs1 */,
    27 /* pkcs3 */,
    187 /* pkcs5 */,
    20 /* pkcs7 */,
    21 /* pkcs7-data */,
    25 /* pkcs7-digestData */,
    26 /* pkcs7-encryptedData */,
    23 /* pkcs7-envelopedData */,
    24 /* pkcs7-signedAndEnvelopedData */,
    22 /* pkcs7-signedData */,
    151 /* pkcs8ShroudedKeyBag */,
    47 /* pkcs9 */,
    401 /* policyConstraints */,
    747 /* policyMappings */,
    862 /* postOfficeBox */,
    861 /* postalAddress */,
    661 /* postalCode */,
    683 /* ppBasis */,
    872 /* preferredDeliveryMethod */,
    873 /* presentationAddress */,
    816 /* prf-gostr3411-94 */,
    406 /* prime-field */,
    409 /* prime192v1 */,
    410 /* prime192v2 */,
    411 /* prime192v3 */,
    412 /* prime239v1 */,
    413 /* prime239v2 */,
    414 /* prime239v3 */,
    415 /* prime256v1 */,
    385 /* private */,
    84 /* privateKeyUsagePeriod */,
    886 /* protocolInformation */,
    663 /* proxyCertInfo */,
    510 /* pseudonym */,
    435 /* pss */,
    286 /* qcStatements */,
    457 /* qualityLabelledData */,
    450 /* rFC822localPart */,
    870 /* registeredAddress */,
    400 /* role */,
    877 /* roleOccupant */,
    448 /* room */,
    463 /* roomNumber */,
    6 /* rsaEncryption */,
    644 /* rsaOAEPEncryptionSET */,
    377 /* rsaSignature */,
    1 /* rsadsi */,
    482 /* sOARecord */,
    155 /* safeContentsBag */,
    291 /* sbgp-autonomousSysNum */,
    290 /* sbgp-ipAddrBlock */,
    292 /* sbgp-routerIdentifier */,
    159 /* sdsiCertificate */,
    859 /* searchGuide */,
    704 /* secp112r1 */,
    705 /* secp112r2 */,
    706 /* secp128r1 */,
    707 /* secp128r2 */,
    708 /* secp160k1 */,
    709 /* secp160r1 */,
    710 /* secp160r2 */,
    711 /* secp192k1 */,
    712 /* secp224k1 */,
    713 /* secp224r1 */,
    714 /* secp256k1 */,
    715 /* secp384r1 */,
    716 /* secp521r1 */,
    154 /* secretBag */,
    474 /* secretary */,
    717 /* sect113r1 */,
    718 /* sect113r2 */,
    719 /* sect131r1 */,
    720 /* sect131r2 */,
    721 /* sect163k1 */,
    722 /* sect163r1 */,
    723 /* sect163r2 */,
    724 /* sect193r1 */,
    725 /* sect193r2 */,
    726 /* sect233k1 */,
    727 /* sect233r1 */,
    728 /* sect239k1 */,
    729 /* sect283k1 */,
    730 /* sect283r1 */,
    731 /* sect409k1 */,
    732 /* sect409r1 */,
    733 /* sect571k1 */,
    734 /* sect571r1 */,
    386 /* security */,
    878 /* seeAlso */,
    394 /* selected-attribute-types */,
    105 /* serialNumber */,
    129 /* serverAuth */,
    371 /* serviceLocator */,
    625 /* set-addPolicy */,
    515 /* set-attr */,
    518 /* set-brand */,
    638 /* set-brand-AmericanExpress */,
    637 /* set-brand-Diners */,
    636 /* set-brand-IATA-ATA */,
    639 /* set-brand-JCB */,
    641 /* set-brand-MasterCard */,
    642 /* set-brand-Novus */,
    640 /* set-brand-Visa */,
    517 /* set-certExt */,
    513 /* set-ctype */,
    514 /* set-msgExt */,
    516 /* set-policy */,
    607 /* set-policy-root */,
    624 /* set-rootKeyThumb */,
    620 /* setAttr-Cert */,
    631 /* setAttr-GenCryptgrm */,
    623 /* setAttr-IssCap */,
    628 /* setAttr-IssCap-CVM */,
    630 /* setAttr-IssCap-Sig */,
    629 /* setAttr-IssCap-T2 */,
    621 /* setAttr-PGWYcap */,
    635 /* setAttr-SecDevSig */,
    632 /* setAttr-T2Enc */,
    633 /* setAttr-T2cleartxt */,
    634 /* setAttr-TokICCsig */,
    627 /* setAttr-Token-B0Prime */,
    626 /* setAttr-Token-EMV */,
    622 /* setAttr-TokenType */,
    619 /* setCext-IssuerCapabilities */,
    615 /* setCext-PGWYcapabilities */,
    616 /* setCext-TokenIdentifier */,
    618 /* setCext-TokenType */,
    617 /* setCext-Track2Data */,
    611 /* setCext-cCertRequired */,
    609 /* setCext-certType */,
    608 /* setCext-hashedRoot */,
    610 /* setCext-merchData */,
    613 /* setCext-setExt */,
    614 /* setCext-setQualf */,
    612 /* setCext-tunneling */,
    540 /* setct-AcqCardCodeMsg */,
    576 /* setct-AcqCardCodeMsgTBE */,
    570 /* setct-AuthReqTBE */,
    534 /* setct-AuthReqTBS */,
    527 /* setct-AuthResBaggage */,
    571 /* setct-AuthResTBE */,
    572 /* setct-AuthResTBEX */,
    535 /* setct-AuthResTBS */,
    536 /* setct-AuthResTBSX */,
    528 /* setct-AuthRevReqBaggage */,
    577 /* setct-AuthRevReqTBE */,
    541 /* setct-AuthRevReqTBS */,
    529 /* setct-AuthRevResBaggage */,
    542 /* setct-AuthRevResData */,
    578 /* setct-AuthRevResTBE */,
    579 /* setct-AuthRevResTBEB */,
    543 /* setct-AuthRevResTBS */,
    573 /* setct-AuthTokenTBE */,
    537 /* setct-AuthTokenTBS */,
    600 /* setct-BCIDistributionTBS */,
    558 /* setct-BatchAdminReqData */,
    592 /* setct-BatchAdminReqTBE */,
    559 /* setct-BatchAdminResData */,
    593 /* setct-BatchAdminResTBE */,
    599 /* setct-CRLNotificationResTBS */,
    598 /* setct-CRLNotificationTBS */,
    580 /* setct-CapReqTBE */,
    581 /* setct-CapReqTBEX */,
    544 /* setct-CapReqTBS */,
    545 /* setct-CapReqTBSX */,
    546 /* setct-CapResData */,
    582 /* setct-CapResTBE */,
    583 /* setct-CapRevReqTBE */,
    584 /* setct-CapRevReqTBEX */,
    547 /* setct-CapRevReqTBS */,
    548 /* setct-CapRevReqTBSX */,
    549 /* setct-CapRevResData */,
    585 /* setct-CapRevResTBE */,
    538 /* setct-CapTokenData */,
    530 /* setct-CapTokenSeq */,
    574 /* setct-CapTokenTBE */,
    575 /* setct-CapTokenTBEX */,
    539 /* setct-CapTokenTBS */,
    560 /* setct-CardCInitResTBS */,
    566 /* setct-CertInqReqTBS */,
    563 /* setct-CertReqData */,
    595 /* setct-CertReqTBE */,
    596 /* setct-CertReqTBEX */,
    564 /* setct-CertReqTBS */,
    565 /* setct-CertResData */,
    597 /* setct-CertResTBE */,
    586 /* setct-CredReqTBE */,
    587 /* setct-CredReqTBEX */,
    550 /* setct-CredReqTBS */,
    551 /* setct-CredReqTBSX */,
    552 /* setct-CredResData */,
    588 /* setct-CredResTBE */,
    589 /* setct-CredRevReqTBE */,
    590 /* setct-CredRevReqTBEX */,
    553 /* setct-CredRevReqTBS */,
    554 /* setct-CredRevReqTBSX */,
    555 /* setct-CredRevResData */,
    591 /* setct-CredRevResTBE */,
    567 /* setct-ErrorTBS */,
    526 /* setct-HODInput */,
    561 /* setct-MeAqCInitResTBS */,
    522 /* setct-OIData */,
    519 /* setct-PANData */,
    521 /* setct-PANOnly */,
    520 /* setct-PANToken */,
    556 /* setct-PCertReqData */,
    557 /* setct-PCertResTBS */,
    523 /* setct-PI */,
    532 /* setct-PI-TBS */,
    524 /* setct-PIData */,
    525 /* setct-PIDataUnsigned */,
    568 /* setct-PIDualSignedTBE */,
    569 /* setct-PIUnsignedTBE */,
    531 /* setct-PInitResData */,
    533 /* setct-PResData */,
    594 /* setct-RegFormReqTBE */,
    562 /* setct-RegFormResTBS */,
    606 /* setext-cv */,
    601 /* setext-genCrypt */,
    602 /* setext-miAuth */,
    604 /* setext-pinAny */,
    603 /* setext-pinSecure */,
    605 /* setext-track2 */,
    52 /* signingTime */,
    454 /* simpleSecurityObject */,
    496 /* singleLevelQuality */,
    387 /* snmpv2 */,
    660 /* street */,
    85 /* subjectAltName */,
    769 /* subjectDirectoryAttributes */,
    398 /* subjectInfoAccess */,
    82 /* subjectKeyIdentifier */,
    498 /* subtreeMaximumQuality */,
    497 /* subtreeMinimumQuality */,
    890 /* supportedAlgorithms */,
    874 /* supportedApplicationContext */,
    402 /* targetInformation */,
    864 /* telephoneNumber */,
    866 /* teletexTerminalIdentifier */,
    865 /* telexNumber */,
    459 /* textEncodedORAddress */,
    293 /* textNotice */,
    133 /* timeStamping */,
    106 /* title */,
    682 /* tpBasis */,
    375 /* trustRoot */,
    436 /* ucl */,
    888 /* uniqueMember */,
    55 /* unstructuredAddress */,
    49 /* unstructuredName */,
    880 /* userCertificate */,
    465 /* userClass */,
    879 /* userPassword */,
    373 /* valid */,
    678 /* wap */,
    679 /* wap-wsg */,
    735 /* wap-wsg-idm-ecid-wtls1 */,
    743 /* wap-wsg-idm-ecid-wtls10 */,
    744 /* wap-wsg-idm-ecid-wtls11 */,
    745 /* wap-wsg-idm-ecid-wtls12 */,
    736 /* wap-wsg-idm-ecid-wtls3 */,
    737 /* wap-wsg-idm-ecid-wtls4 */,
    738 /* wap-wsg-idm-ecid-wtls5 */,
    739 /* wap-wsg-idm-ecid-wtls6 */,
    740 /* wap-wsg-idm-ecid-wtls7 */,
    741 /* wap-wsg-idm-ecid-wtls8 */,
    742 /* wap-wsg-idm-ecid-wtls9 */,
    804 /* whirlpool */,
    868 /* x121Address */,
    503 /* x500UniqueIdentifier */,
    158 /* x509Certificate */,
    160 /* x509Crl */,
};

static const uint16_t kNIDsInLongNameOrder[] = {
    363 /* AD Time Stamping */,
    405 /* ANSI X9.62 */,
    368 /* Acceptable OCSP Responses */,
    910 /* Any Extended Key Usage */,
    664 /* Any language */,
    177 /* Authority Information Access */,
    365 /* Basic OCSP Response */,
    285 /* Biometric Info */,
    179 /* CA Issuers */,
    785 /* CA Repository */,
    959 /* CECPQ2 */,
    131 /* Code Signing */,
    783 /* Diffie-Hellman based MAC */,
    382 /* Directory */,
    392 /* Domain */,
    132 /* E-mail Protection */,
    949 /* ED25519 */,
    960 /* ED448 */,
    389 /* Enterprises */,
    384 /* Experimental */,
    372 /* Extended OCSP Status */,
    172 /* Extension Request */,
    813 /* GOST 28147-89 */,
    849 /* GOST 28147-89 Cryptocom ParamSet */,
    815 /* GOST 28147-89 MAC */,
    851 /* GOST 34.10-2001 Cryptocom */,
    850 /* GOST 34.10-94 Cryptocom */,
    811 /* GOST R 34.10-2001 */,
    817 /* GOST R 34.10-2001 DH */,
    812 /* GOST R 34.10-94 */,
    818 /* GOST R 34.10-94 DH */,
    809 /* GOST R 34.11-94 */,
    816 /* GOST R 34.11-94 PRF */,
    807 /* GOST R 34.11-94 with GOST R 34.10-2001 */,
    853 /* GOST R 34.11-94 with GOST R 34.10-2001 Cryptocom */,
    808 /* GOST R 34.11-94 with GOST R 34.10-94 */,
    852 /* GOST R 34.11-94 with GOST R 34.10-94 Cryptocom */,
    854 /* GOST R 3410-2001 Parameter Set Cryptocom */,
    810 /* HMAC GOST 34.11-94 */,
    432 /* Hold Instruction Call Issuer */,
    430 /* Hold Instruction Code */,
    431 /* Hold Instruction None */,
    433 /* Hold Instruction Reject */,
    634 /* ICC or token signature */,
    294 /* IPSec End System */,
    295 /* IPSec Tunnel */,
    296 /* IPSec User */,
    182 /* ISO Member Body */,
    183 /* ISO US Member Body */,
    667 /* Independent */,
    665 /* Inherit all */,
    647 /* International Organizations */,
    142 /* Invalidity Date */,
    504 /* MIME MHS */,
    388 /* Mail */,
    383 /* Management */,
    417 /* Microsoft CSP Name */,
    135 /* Microsoft Commercial Code Signing */,
    138 /* Microsoft Encrypted File System */,
    171 /* Microsoft Extension Request */,
    134 /* Microsoft Individual Code Signing */,
    856 /* Microsoft Local Key set */,
    137 /* Microsoft Server Gated Crypto */,
    648 /* Microsoft Smartcardlogin */,
    136 /* Microsoft Trust List Signing */,
    649 /* Microsoft Universal Principal Name */,
    72 /* Netscape Base Url */,
    76 /* Netscape CA Policy Url */,
    74 /* Netscape CA Revocation Url */,
    71 /* Netscape Cert Type */,
    58 /* Netscape Certificate Extension */,
    79 /* Netscape Certificate Sequence */,
    78 /* Netscape Comment */,
    57 /* Netscape Communications Corp. */,
    59 /* Netscape Data Type */,
    75 /* Netscape Renewal Url */,
    73 /* Netscape Revocation Url */,
    77 /* Netscape SSL Server Name */,
    139 /* Netscape Server Gated Crypto */,
    178 /* OCSP */,
    370 /* OCSP Archive Cutoff */,
    367 /* OCSP CRL ID */,
    369 /* OCSP No Check */,
    366 /* OCSP Nonce */,
    371 /* OCSP Service Locator */,
    180 /* OCSP Signing */,
    161 /* PBES2 */,
    69 /* PBKDF2 */,
    162 /* PBMAC1 */,
    127 /* PKIX */,
    858 /* Permanent Identifier */,
    164 /* Policy Qualifier CPS */,
    165 /* Policy Qualifier User Notice */,
    385 /* Private */,
    663 /* Proxy Certificate Information */,
    1 /* RSA Data Security, Inc. */,
    2 /* RSA Data Security, Inc. PKCS */,
    188 /* S/MIME */,
    167 /* S/MIME Capabilities */,
    387 /* SNMPv2 */,
    512 /* Secure Electronic Transactions */,
    386 /* Security */,
    394 /* Selected Attribute Types */,
    143 /* Strong Extranet ID */,
    398 /* Subject Information Access */,
    130 /* TLS Web Client Authentication */,
    129 /* TLS Web Server Authentication */,
    133 /* Time Stamping */,
    375 /* Trust Root */,
    948 /* X25519 */,
    12 /* X509 */,
    402 /* X509v3 AC Targeting */,
    746 /* X509v3 Any Policy */,
    90 /* X509v3 Authority Key Identifier */,
    87 /* X509v3 Basic Constraints */,
    103 /* X509v3 CRL Distribution Points */,
    88 /* X509v3 CRL Number */,
    141 /* X509v3 CRL Reason Code */,
    771 /* X509v3 Certificate Issuer */,
    89 /* X509v3 Certificate Policies */,
    140 /* X509v3 Delta CRL Indicator */,
    126 /* X509v3 Extended Key Usage */,
    857 /* X509v3 Freshest CRL */,
    748 /* X509v3 Inhibit Any Policy */,
    86 /* X509v3 Issuer Alternative Name */,
    770 /* X509v3 Issuing Distribution Point */,
    83 /* X509v3 Key Usage */,
    666 /* X509v3 Name Constraints */,
    403 /* X509v3 No Revocation Available */,
    401 /* X509v3 Policy Constraints */,
    747 /* X509v3 Policy Mappings */,
    84 /* X509v3 Private Key Usage Period */,
    85 /* X509v3 Subject Alternative Name */,
    769 /* X509v3 Subject Directory Attributes */,
    82 /* X509v3 Subject Key Identifier */,
    920 /* X9.42 DH */,
    184 /* X9.57 */,
    185 /* X9.57 CM ? */,
    478 /* aRecord */,
    289 /* aaControls */,
    287 /* ac-auditEntity */,
    397 /* ac-proxying */,
    288 /* ac-targeting */,
    446 /* account */,
    364 /* ad dvcs */,
    606 /* additional verification */,
    419 /* aes-128-cbc */,
    916 /* aes-128-cbc-hmac-sha1 */,
    896 /* aes-128-ccm */,
    421 /* aes-128-cfb */,
    650 /* aes-128-cfb1 */,
    653 /* aes-128-cfb8 */,
    904 /* aes-128-ctr */,
    418 /* aes-128-ecb */,
    895 /* aes-128-gcm */,
    420 /* aes-128-ofb */,
    913 /* aes-128-xts */,
    423 /* aes-192-cbc */,
    917 /* aes-192-cbc-hmac-sha1 */,
    899 /* aes-192-ccm */,
    425 /* aes-192-cfb */,
    651 /* aes-192-cfb1 */,
    654 /* aes-192-cfb8 */,
    905 /* aes-192-ctr */,
    422 /* aes-192-ecb */,
    898 /* aes-192-gcm */,
    424 /* aes-192-ofb */,
    427 /* aes-256-cbc */,
    918 /* aes-256-cbc-hmac-sha1 */,
    902 /* aes-256-ccm */,
    429 /* aes-256-cfb */,
    652 /* aes-256-cfb1 */,
    655 /* aes-256-cfb8 */,
    906 /* aes-256-ctr */,
    426 /* aes-256-ecb */,
    901 /* aes-256-gcm */,
    428 /* aes-256-ofb */,
    914 /* aes-256-xts */,
    376 /* algorithm */,
    484 /* associatedDomain */,
    485 /* associatedName */,
    501 /* audio */,
    958 /* auth-any */,
    955 /* auth-ecdsa */,
    956 /* auth-psk */,
    954 /* auth-rsa */,
    882 /* authorityRevocationList */,
    91 /* bf-cbc */,
    93 /* bf-cfb */,
    92 /* bf-ecb */,
    94 /* bf-ofb */,
    921 /* brainpoolP160r1 */,
    922 /* brainpoolP160t1 */,
    923 /* brainpoolP192r1 */,
    924 /* brainpoolP192t1 */,
    925 /* brainpoolP224r1 */,
    926 /* brainpoolP224t1 */,
    927 /* brainpoolP256r1 */,
    928 /* brainpoolP256t1 */,
    929 /* brainpoolP320r1 */,
    930 /* brainpoolP320t1 */,
    931 /* brainpoolP384r1 */,
    932 /* brainpoolP384t1 */,
    933 /* brainpoolP512r1 */,
    934 /* brainpoolP512t1 */,
    494 /* buildingName */,
    860 /* businessCategory */,
    691 /* c2onb191v4 */,
    692 /* c2onb191v5 */,
    697 /* c2onb239v4 */,
    698 /* c2onb239v5 */,
    684 /* c2pnb163v1 */,
    685 /* c2pnb163v2 */,
    686 /* c2pnb163v3 */,
    687 /* c2pnb176v1 */,
    693 /* c2pnb208w1 */,
    699 /* c2pnb272w1 */,
    700 /* c2pnb304w1 */,
    702 /* c2pnb368w1 */,
    688 /* c2tnb191v1 */,
    689 /* c2tnb191v2 */,
    690 /* c2tnb191v3 */,
    694 /* c2tnb239v1 */,
    695 /* c2tnb239v2 */,
    696 /* c2tnb239v3 */,
    701 /* c2tnb359v1 */,
    703 /* c2tnb431r1 */,
    881 /* cACertificate */,
    483 /* cNAMERecord */,
    751 /* camellia-128-cbc */,
    757 /* camellia-128-cfb */,
    760 /* camellia-128-cfb1 */,
    763 /* camellia-128-cfb8 */,
    754 /* camellia-128-ecb */,
    766 /* camellia-128-ofb */,
    752 /* camellia-192-cbc */,
    758 /* camellia-192-cfb */,
    761 /* camellia-192-cfb1 */,
    764 /* camellia-192-cfb8 */,
    755 /* camellia-192-ecb */,
    767 /* camellia-192-ofb */,
    753 /* camellia-256-cbc */,
    759 /* camellia-256-cfb */,
    762 /* camellia-256-cfb1 */,
    765 /* camellia-256-cfb8 */,
    756 /* camellia-256-ecb */,
    768 /* camellia-256-ofb */,
    443 /* caseIgnoreIA5StringSyntax */,
    108 /* cast5-cbc */,
    110 /* cast5-cfb */,
    109 /* cast5-ecb */,
    111 /* cast5-ofb */,
    152 /* certBag */,
    677 /* certicom-arc */,
    517 /* certificate extensions */,
    883 /* certificateRevocationList */,
    950 /* chacha20-poly1305 */,
    54 /* challengePassword */,
    407 /* characteristic-two-field */,
    395 /* clearance */,
    633 /* cleartext track 2 */,
    894 /* cmac */,
    13 /* commonName */,
    513 /* content types */,
    50 /* contentType */,
    53 /* countersignature */,
    14 /* countryName */,
    153 /* crlBag */,
    884 /* crossCertificatePair */,
    806 /* cryptocom */,
    805 /* cryptopro */,
    500 /* dITRedirect */,
    451 /* dNSDomain */,
    495 /* dSAQuality */,
    434 /* data */,
    390 /* dcObject */,
    891 /* deltaRevocationList */,
    31 /* des-cbc */,
    643 /* des-cdmf */,
    30 /* des-cfb */,
    656 /* des-cfb1 */,
    657 /* des-cfb8 */,
    29 /* des-ecb */,
    32 /* des-ede */,
    43 /* des-ede-cbc */,
    60 /* des-ede-cfb */,
    62 /* des-ede-ofb */,
    33 /* des-ede3 */,
    44 /* des-ede3-cbc */,
    61 /* des-ede3-cfb */,
    658 /* des-ede3-cfb1 */,
    659 /* des-ede3-cfb8 */,
    63 /* des-ede3-ofb */,
    45 /* des-ofb */,
    107 /* description */,
    871 /* destinationIndicator */,
    80 /* desx-cbc */,
    947 /* dh-cofactor-kdf */,
    946 /* dh-std-kdf */,
    28 /* dhKeyAgreement */,
    941 /* dhSinglePass-cofactorDH-sha1kdf-scheme */,
    942 /* dhSinglePass-cofactorDH-sha224kdf-scheme */,
    943 /* dhSinglePass-cofactorDH-sha256kdf-scheme */,
    944 /* dhSinglePass-cofactorDH-sha384kdf-scheme */,
    945 /* dhSinglePass-cofactorDH-sha512kdf-scheme */,
    936 /* dhSinglePass-stdDH-sha1kdf-scheme */,
    937 /* dhSinglePass-stdDH-sha224kdf-scheme */,
    938 /* dhSinglePass-stdDH-sha256kdf-scheme */,
    939 /* dhSinglePass-stdDH-sha384kdf-scheme */,
    940 /* dhSinglePass-stdDH-sha512kdf-scheme */,
    11 /* directory services (X.500) */,
    378 /* directory services - algorithms */,
    887 /* distinguishedName */,
    892 /* dmdName */,
    174 /* dnQualifier */,
    447 /* document */,
    471 /* documentAuthor */,
    468 /* documentIdentifier */,
    472 /* documentLocation */,
    502 /* documentPublisher */,
    449 /* documentSeries */,
    469 /* documentTitle */,
    470 /* documentVersion */,
    380 /* dod */,
    391 /* domainComponent */,
    452 /* domainRelatedObject */,
    116 /* dsaEncryption */,
    67 /* dsaEncryption-old */,
    66 /* dsaWithSHA */,
    113 /* dsaWithSHA1 */,
    70 /* dsaWithSHA1-old */,
    802 /* dsa_with_SHA224 */,
    803 /* dsa_with_SHA256 */,
    297 /* dvcs */,
    791 /* ecdsa-with-Recommended */,
    416 /* ecdsa-with-SHA1 */,
    793 /* ecdsa-with-SHA224 */,
    794 /* ecdsa-with-SHA256 */,
    795 /* ecdsa-with-SHA384 */,
    796 /* ecdsa-with-SHA512 */,
    792 /* ecdsa-with-Specified */,
    48 /* emailAddress */,
    632 /* encrypted track 2 */,
    885 /* enhancedSearchGuide */,
    56 /* extendedCertificateAttributes */,
    867 /* facsimileTelephoneNumber */,
    462 /* favouriteDrink */,
    453 /* friendlyCountry */,
    490 /* friendlyCountryName */,
    156 /* friendlyName */,
    631 /* generate cryptogram */,
    509 /* generationQualifier */,
    601 /* generic cryptogram */,
    99 /* givenName */,
    814 /* gost89-cnt */,
    855 /* hmac */,
    780 /* hmac-md5 */,
    781 /* hmac-sha1 */,
    797 /* hmacWithMD5 */,
    163 /* hmacWithSHA1 */,
    798 /* hmacWithSHA224 */,
    799 /* hmacWithSHA256 */,
    800 /* hmacWithSHA384 */,
    801 /* hmacWithSHA512 */,
    486 /* homePostalAddress */,
    473 /* homeTelephoneNumber */,
    466 /* host */,
    889 /* houseIdentifier */,
    442 /* iA5StringSyntax */,
    381 /* iana */,
    824 /* id-Gost28147-89-CryptoPro-A-ParamSet */,
    825 /* id-Gost28147-89-CryptoPro-B-ParamSet */,
    826 /* id-Gost28147-89-CryptoPro-C-ParamSet */,
    827 /* id-Gost28147-89-CryptoPro-D-ParamSet */,
    819 /* id-Gost28147-89-CryptoPro-KeyMeshing */,
    829 /* id-Gost28147-89-CryptoPro-Oscar-1-0-ParamSet */,
    828 /* id-Gost28147-89-CryptoPro-Oscar-1-1-ParamSet */,
    830 /* id-Gost28147-89-CryptoPro-RIC-1-ParamSet */,
    820 /* id-Gost28147-89-None-KeyMeshing */,
    823 /* id-Gost28147-89-TestParamSet */,
    840 /* id-GostR3410-2001-CryptoPro-A-ParamSet */,
    841 /* id-GostR3410-2001-CryptoPro-B-ParamSet */,
    842 /* id-GostR3410-2001-CryptoPro-C-ParamSet */,
    843 /* id-GostR3410-2001-CryptoPro-XchA-ParamSet */,
    844 /* id-GostR3410-2001-CryptoPro-XchB-ParamSet */,
    839 /* id-GostR3410-2001-TestParamSet */,
    832 /* id-GostR3410-94-CryptoPro-A-ParamSet */,
    833 /* id-GostR3410-94-CryptoPro-B-ParamSet */,
    834 /* id-GostR3410-94-CryptoPro-C-ParamSet */,
    835 /* id-GostR3410-94-CryptoPro-D-ParamSet */,
    836 /* id-GostR3410-94-CryptoPro-XchA-ParamSet */,
    837 /* id-GostR3410-94-CryptoPro-XchB-ParamSet */,
    838 /* id-GostR3410-94-CryptoPro-XchC-ParamSet */,
    831 /* id-GostR3410-94-TestParamSet */,
    845 /* id-GostR3410-94-a */,
    846 /* id-GostR3410-94-aBis */,
    847 /* id-GostR3410-94-b */,
    848 /* id-GostR3410-94-bBis */,
    822 /* id-GostR3411-94-CryptoProParamSet */,
    821 /* id-GostR3411-94-TestParamSet */,
    266 /* id-aca */,
    355 /* id-aca-accessIdentity */,
    354 /* id-aca-authenticationInfo */,
    356 /* id-aca-chargingIdentity */,
    399 /* id-aca-encAttrs */,
    357 /* id-aca-group */,
    358 /* id-aca-role */,
    176 /* id-ad */,
    788 /* id-aes128-wrap */,
    897 /* id-aes128-wrap-pad */,
    789 /* id-aes192-wrap */,
    900 /* id-aes192-wrap-pad */,
    790 /* id-aes256-wrap */,
    903 /* id-aes256-wrap-pad */,
    262 /* id-alg */,
    893 /* id-alg-PWRI-KEK */,
    323 /* id-alg-des40 */,
    326 /* id-alg-dh-pop */,
    325 /* id-alg-dh-sig-hmac-sha1 */,
    324 /* id-alg-noSignature */,
    907 /* id-camellia128-wrap */,
    908 /* id-camellia192-wrap */,
    909 /* id-camellia256-wrap */,
    268 /* id-cct */,
    361 /* id-cct-PKIData */,
    362 /* id-cct-PKIResponse */,
    360 /* id-cct-crs */,
    81 /* id-ce */,
    680 /* id-characteristic-two-basis */,
    263 /* id-cmc */,
    334 /* id-cmc-addExtensions */,
    346 /* id-cmc-confirmCertAcceptance */,
    330 /* id-cmc-dataReturn */,
    336 /* id-cmc-decryptedPOP */,
    335 /* id-cmc-encryptedPOP */,
    339 /* id-cmc-getCRL */,
    338 /* id-cmc-getCert */,
    328 /* id-cmc-identification */,
    329 /* id-cmc-identityProof */,
    337 /* id-cmc-lraPOPWitness */,
    344 /* id-cmc-popLinkRandom */,
    345 /* id-cmc-popLinkWitness */,
    343 /* id-cmc-queryPending */,
    333 /* id-cmc-recipientNonce */,
    341 /* id-cmc-regInfo */,
    342 /* id-cmc-responseInfo */,
    340 /* id-cmc-revokeRequest */,
    332 /* id-cmc-senderNonce */,
    327 /* id-cmc-statusInfo */,
    331 /* id-cmc-transactionId */,
    787 /* id-ct-asciiTextWithCRLF */,
    408 /* id-ecPublicKey */,
    508 /* id-hex-multipart-message */,
    507 /* id-hex-partial-message */,
    260 /* id-it */,
    302 /* id-it-caKeyUpdateInfo */,
    298 /* id-it-caProtEncCert */,
    311 /* id-it-confirmWaitTime */,
    303 /* id-it-currentCRL */,
    300 /* id-it-encKeyPairTypes */,
    310 /* id-it-implicitConfirm */,
    308 /* id-it-keyPairParamRep */,
    307 /* id-it-keyPairParamReq */,
    312 /* id-it-origPKIMessage */,
    301 /* id-it-preferredSymmAlg */,
    309 /* id-it-revPassphrase */,
    299 /* id-it-signKeyPairTypes */,
    305 /* id-it-subscriptionRequest */,
    306 /* id-it-subscriptionResponse */,
    784 /* id-it-suppLangTags */,
    304 /* id-it-unsupportedOIDs */,
    128 /* id-kp */,
    280 /* id-mod-attribute-cert */,
    274 /* id-mod-cmc */,
    277 /* id-mod-cmp */,
    284 /* id-mod-cmp2000 */,
    273 /* id-mod-crmf */,
    283 /* id-mod-dvcs */,
    275 /* id-mod-kea-profile-88 */,
    276 /* id-mod-kea-profile-93 */,
    282 /* id-mod-ocsp */,
    278 /* id-mod-qualified-cert-88 */,
    279 /* id-mod-qualified-cert-93 */,
    281 /* id-mod-timestamp-protocol */,
    264 /* id-on */,
    347 /* id-on-personalData */,
    265 /* id-pda */,
    352 /* id-pda-countryOfCitizenship */,
    353 /* id-pda-countryOfResidence */,
    348 /* id-pda-dateOfBirth */,
    351 /* id-pda-gender */,
    349 /* id-pda-placeOfBirth */,
    175 /* id-pe */,
    261 /* id-pkip */,
    258 /* id-pkix-mod */,
    269 /* id-pkix1-explicit-88 */,
    271 /* id-pkix1-explicit-93 */,
    270 /* id-pkix1-implicit-88 */,
    272 /* id-pkix1-implicit-93 */,
    662 /* id-ppl */,
    267 /* id-qcs */,
    359 /* id-qcs-pkixQCSyntax-v1 */,
    259 /* id-qt */,
    313 /* id-regCtrl */,
    316 /* id-regCtrl-authenticator */,
    319 /* id-regCtrl-oldCertID */,
    318 /* id-regCtrl-pkiArchiveOptions */,
    317 /* id-regCtrl-pkiPublicationInfo */,
    320 /* id-regCtrl-protocolEncrKey */,
    315 /* id-regCtrl-regToken */,
    314 /* id-regInfo */,
    322 /* id-regInfo-certReq */,
    321 /* id-regInfo-utf8Pairs */,
    191 /* id-smime-aa */,
    215 /* id-smime-aa-contentHint */,
    218 /* id-smime-aa-contentIdentifier */,
    221 /* id-smime-aa-contentReference */,
    240 /* id-smime-aa-dvcs-dvc */,
    217 /* id-smime-aa-encapContentType */,
    222 /* id-smime-aa-encrypKeyPref */,
    220 /* id-smime-aa-equivalentLabels */,
    232 /* id-smime-aa-ets-CertificateRefs */,
    233 /* id-smime-aa-ets-RevocationRefs */,
    238 /* id-smime-aa-ets-archiveTimeStamp */,
    237 /* id-smime-aa-ets-certCRLTimestamp */,
    234 /* id-smime-aa-ets-certValues */,
    227 /* id-smime-aa-ets-commitmentType */,
    231 /* id-smime-aa-ets-contentTimestamp */,
    236 /* id-smime-aa-ets-escTimeStamp */,
    230 /* id-smime-aa-ets-otherSigCert */,
    235 /* id-smime-aa-ets-revocationValues */,
    226 /* id-smime-aa-ets-sigPolicyId */,
    229 /* id-smime-aa-ets-signerAttr */,
    228 /* id-smime-aa-ets-signerLocation */,
    219 /* id-smime-aa-macValue */,
    214 /* id-smime-aa-mlExpandHistory */,
    216 /* id-smime-aa-msgSigDigest */,
    212 /* id-smime-aa-receiptRequest */,
    213 /* id-smime-aa-securityLabel */,
    239 /* id-smime-aa-signatureType */,
    223 /* id-smime-aa-signingCertificate */,
    224 /* id-smime-aa-smimeEncryptCerts */,
    225 /* id-smime-aa-timeStampToken */,
    192 /* id-smime-alg */,
    243 /* id-smime-alg-3DESwrap */,
    246 /* id-smime-alg-CMS3DESwrap */,
    247 /* id-smime-alg-CMSRC2wrap */,
    245 /* id-smime-alg-ESDH */,
    241 /* id-smime-alg-ESDHwith3DES */,
    242 /* id-smime-alg-ESDHwithRC2 */,
    244 /* id-smime-alg-RC2wrap */,
    193 /* id-smime-cd */,
    248 /* id-smime-cd-ldap */,
    190 /* id-smime-ct */,
    210 /* id-smime-ct-DVCSRequestData */,
    211 /* id-smime-ct-DVCSResponseData */,
    208 /* id-smime-ct-TDTInfo */,
    207 /* id-smime-ct-TSTInfo */,
    205 /* id-smime-ct-authData */,
    786 /* id-smime-ct-compressedData */,
    209 /* id-smime-ct-contentInfo */,
    206 /* id-smime-ct-publishCert */,
    204 /* id-smime-ct-receipt */,
    195 /* id-smime-cti */,
    255 /* id-smime-cti-ets-proofOfApproval */,
    256 /* id-smime-cti-ets-proofOfCreation */,
    253 /* id-smime-cti-ets-proofOfDelivery */,
    251 /* id-smime-cti-ets-proofOfOrigin */,
    252 /* id-smime-cti-ets-proofOfReceipt */,
    254 /* id-smime-cti-ets-proofOfSender */,
    189 /* id-smime-mod */,
    196 /* id-smime-mod-cms */,
    197 /* id-smime-mod-ess */,
    202 /* id-smime-mod-ets-eSigPolicy-88 */,
    203 /* id-smime-mod-ets-eSigPolicy-97 */,
    200 /* id-smime-mod-ets-eSignature-88 */,
    201 /* id-smime-mod-ets-eSignature-97 */,
    199 /* id-smime-mod-msg-v3 */,
    198 /* id-smime-mod-oid */,
    194 /* id-smime-spq */,
    250 /* id-smime-spq-ets-sqt-unotice */,
    249 /* id-smime-spq-ets-sqt-uri */,
    34 /* idea-cbc */,
    35 /* idea-cfb */,
    36 /* idea-ecb */,
    46 /* idea-ofb */,
    676 /* identified-organization */,
    461 /* info */,
    101 /* initials */,
    869 /* internationaliSDNNumber */,
    749 /* ipsec3 */,
    750 /* ipsec4 */,
    181 /* iso */,
    623 /* issuer capabilities */,
    645 /* itu-t */,
    492 /* janetMailbox */,
    646 /* joint-iso-itu-t */,
    150 /* keyBag */,
    773 /* kisa */,
    957 /* kx-any */,
    952 /* kx-ecdhe */,
    953 /* kx-psk */,
    951 /* kx-rsa */,
    477 /* lastModifiedBy */,
    476 /* lastModifiedTime */,
    157 /* localKeyID */,
    15 /* localityName */,
    480 /* mXRecord */,
    493 /* mailPreferenceOption */,
    467 /* manager */,
    3 /* md2 */,
    7 /* md2WithRSAEncryption */,
    257 /* md4 */,
    396 /* md4WithRSAEncryption */,
    4 /* md5 */,
    114 /* md5-sha1 */,
    104 /* md5WithRSA */,
    8 /* md5WithRSAEncryption */,
    95 /* mdc2 */,
    96 /* mdc2WithRSA */,
    875 /* member */,
    602 /* merchant initiated auth */,
    514 /* message extensions */,
    51 /* messageDigest */,
    911 /* mgf1 */,
    506 /* mime-mhs-bodies */,
    505 /* mime-mhs-headings */,
    488 /* mobileTelephoneNumber */,
    481 /* nSRecord */,
    173 /* name */,
    681 /* onBasis */,
    379 /* org */,
    17 /* organizationName */,
    491 /* organizationalStatus */,
    18 /* organizationalUnitName */,
    475 /* otherMailbox */,
    876 /* owner */,
    935 /* pSpecified */,
    489 /* pagerTelephoneNumber */,
    782 /* password based MAC */,
    374 /* path */,
    621 /* payment gateway capabilities */,
    9 /* pbeWithMD2AndDES-CBC */,
    168 /* pbeWithMD2AndRC2-CBC */,
    112 /* pbeWithMD5AndCast5CBC */,
    10 /* pbeWithMD5AndDES-CBC */,
    169 /* pbeWithMD5AndRC2-CBC */,
    148 /* pbeWithSHA1And128BitRC2-CBC */,
    144 /* pbeWithSHA1And128BitRC4 */,
    147 /* pbeWithSHA1And2-KeyTripleDES-CBC */,
    146 /* pbeWithSHA1And3-KeyTripleDES-CBC */,
    149 /* pbeWithSHA1And40BitRC2-CBC */,
    145 /* pbeWithSHA1And40BitRC4 */,
    170 /* pbeWithSHA1AndDES-CBC */,
    68 /* pbeWithSHA1AndRC2-CBC */,
    499 /* personalSignature */,
    487 /* personalTitle */,
    464 /* photo */,
    863 /* physicalDeliveryOfficeName */,
    437 /* pilot */,
    439 /* pilotAttributeSyntax */,
    438 /* pilotAttributeType */,
    479 /* pilotAttributeType27 */,
    456 /* pilotDSA */,
    441 /* pilotGroups */,
    444 /* pilotObject */,
    440 /* pilotObjectClass */,
    455 /* pilotOrganization */,
    445 /* pilotPerson */,
    186 /* pkcs1 */,
    27 /* pkcs3 */,
    187 /* pkcs5 */,
    20 /* pkcs7 */,
    21 /* pkcs7-data */,
    25 /* pkcs7-digestData */,
    26 /* pkcs7-encryptedData */,
    23 /* pkcs7-envelopedData */,
    24 /* pkcs7-signedAndEnvelopedData */,
    22 /* pkcs7-signedData */,
    151 /* pkcs8ShroudedKeyBag */,
    47 /* pkcs9 */,
    862 /* postOfficeBox */,
    861 /* postalAddress */,
    661 /* postalCode */,
    683 /* ppBasis */,
    872 /* preferredDeliveryMethod */,
    873 /* presentationAddress */,
    406 /* prime-field */,
    409 /* prime192v1 */,
    410 /* prime192v2 */,
    411 /* prime192v3 */,
    412 /* prime239v1 */,
    413 /* prime239v2 */,
    414 /* prime239v3 */,
    415 /* prime256v1 */,
    886 /* protocolInformation */,
    510 /* pseudonym */,
    435 /* pss */,
    286 /* qcStatements */,
    457 /* qualityLabelledData */,
    450 /* rFC822localPart */,
    98 /* rc2-40-cbc */,
    166 /* rc2-64-cbc */,
    37 /* rc2-cbc */,
    39 /* rc2-cfb */,
    38 /* rc2-ecb */,
    40 /* rc2-ofb */,
    5 /* rc4 */,
    97 /* rc4-40 */,
    915 /* rc4-hmac-md5 */,
    120 /* rc5-cbc */,
    122 /* rc5-cfb */,
    121 /* rc5-ecb */,
    123 /* rc5-ofb */,
    870 /* registeredAddress */,
    460 /* rfc822Mailbox */,
    117 /* ripemd160 */,
    119 /* ripemd160WithRSA */,
    400 /* role */,
    877 /* roleOccupant */,
    448 /* room */,
    463 /* roomNumber */,
    19 /* rsa */,
    6 /* rsaEncryption */,
    644 /* rsaOAEPEncryptionSET */,
    377 /* rsaSignature */,
    919 /* rsaesOaep */,
    912 /* rsassaPss */,
    482 /* sOARecord */,
    155 /* safeContentsBag */,
    291 /* sbgp-autonomousSysNum */,
    290 /* sbgp-ipAddrBlock */,
    292 /* sbgp-routerIdentifier */,
    159 /* sdsiCertificate */,
    859 /* searchGuide */,
    704 /* secp112r1 */,
    705 /* secp112r2 */,
    706 /* secp128r1 */,
    707 /* secp128r2 */,
    708 /* secp160k1 */,
    709 /* secp160r1 */,
    710 /* secp160r2 */,
    711 /* secp192k1 */,
    712 /* secp224k1 */,
    713 /* secp224r1 */,
    714 /* secp256k1 */,
    715 /* secp384r1 */,
    716 /* secp521r1 */,
    154 /* secretBag */,
    474 /* secretary */,
    717 /* sect113r1 */,
    718 /* sect113r2 */,
    719 /* sect131r1 */,
    720 /* sect131r2 */,
    721 /* sect163k1 */,
    722 /* sect163r1 */,
    723 /* sect163r2 */,
    724 /* sect193r1 */,
    725 /* sect193r2 */,
    726 /* sect233k1 */,
    727 /* sect233r1 */,
    728 /* sect239k1 */,
    729 /* sect283k1 */,
    730 /* sect283r1 */,
    731 /* sect409k1 */,
    732 /* sect409r1 */,
    733 /* sect571k1 */,
    734 /* sect571r1 */,
    635 /* secure device signature */,
    878 /* seeAlso */,
    777 /* seed-cbc */,
    779 /* seed-cfb */,
    776 /* seed-ecb */,
    778 /* seed-ofb */,
    105 /* serialNumber */,
    625 /* set-addPolicy */,
    515 /* set-attr */,
    518 /* set-brand */,
    638 /* set-brand-AmericanExpress */,
    637 /* set-brand-Diners */,
    636 /* set-brand-IATA-ATA */,
    639 /* set-brand-JCB */,
    641 /* set-brand-MasterCard */,
    642 /* set-brand-Novus */,
    640 /* set-brand-Visa */,
    516 /* set-policy */,
    607 /* set-policy-root */,
    624 /* set-rootKeyThumb */,
    620 /* setAttr-Cert */,
    628 /* setAttr-IssCap-CVM */,
    630 /* setAttr-IssCap-Sig */,
    629 /* setAttr-IssCap-T2 */,
    627 /* setAttr-Token-B0Prime */,
    626 /* setAttr-Token-EMV */,
    622 /* setAttr-TokenType */,
    619 /* setCext-IssuerCapabilities */,
    615 /* setCext-PGWYcapabilities */,
    616 /* setCext-TokenIdentifier */,
    618 /* setCext-TokenType */,
    617 /* setCext-Track2Data */,
    611 /* setCext-cCertRequired */,
    609 /* setCext-certType */,
    608 /* setCext-hashedRoot */,
    610 /* setCext-merchData */,
    613 /* setCext-setExt */,
    614 /* setCext-setQualf */,
    612 /* setCext-tunneling */,
    540 /* setct-AcqCardCodeMsg */,
    576 /* setct-AcqCardCodeMsgTBE */,
    570 /* setct-AuthReqTBE */,
    534 /* setct-AuthReqTBS */,
    527 /* setct-AuthResBaggage */,
    571 /* setct-AuthResTBE */,
    572 /* setct-AuthResTBEX */,
    535 /* setct-AuthResTBS */,
    536 /* setct-AuthResTBSX */,
    528 /* setct-AuthRevReqBaggage */,
    577 /* setct-AuthRevReqTBE */,
    541 /* setct-AuthRevReqTBS */,
    529 /* setct-AuthRevResBaggage */,
    542 /* setct-AuthRevResData */,
    578 /* setct-AuthRevResTBE */,
    579 /* setct-AuthRevResTBEB */,
    543 /* setct-AuthRevResTBS */,
    573 /* setct-AuthTokenTBE */,
    537 /* setct-AuthTokenTBS */,
    600 /* setct-BCIDistributionTBS */,
    558 /* setct-BatchAdminReqData */,
    592 /* setct-BatchAdminReqTBE */,
    559 /* setct-BatchAdminResData */,
    593 /* setct-BatchAdminResTBE */,
    599 /* setct-CRLNotificationResTBS */,
    598 /* setct-CRLNotificationTBS */,
    580 /* setct-CapReqTBE */,
    581 /* setct-CapReqTBEX */,
    544 /* setct-CapReqTBS */,
    545 /* setct-CapReqTBSX */,
    546 /* setct-CapResData */,
    582 /* setct-CapResTBE */,
    583 /* setct-CapRevReqTBE */,
    584 /* setct-CapRevReqTBEX */,
    547 /* setct-CapRevReqTBS */,
    548 /* setct-CapRevReqTBSX */,
    549 /* setct-CapRevResData */,
    585 /* setct-CapRevResTBE */,
    538 /* setct-CapTokenData */,
    530 /* setct-CapTokenSeq */,
    574 /* setct-CapTokenTBE */,
    575 /* setct-CapTokenTBEX */,
    539 /* setct-CapTokenTBS */,
    560 /* setct-CardCInitResTBS */,
    566 /* setct-CertInqReqTBS */,
    563 /* setct-CertReqData */,
    595 /* setct-CertReqTBE */,
    596 /* setct-CertReqTBEX */,
    564 /* setct-CertReqTBS */,
    565 /* setct-CertResData */,
    597 /* setct-CertResTBE */,
    586 /* setct-CredReqTBE */,
    587 /* setct-CredReqTBEX */,
    550 /* setct-CredReqTBS */,
    551 /* setct-CredReqTBSX */,
    552 /* setct-CredResData */,
    588 /* setct-CredResTBE */,
    589 /* setct-CredRevReqTBE */,
    590 /* setct-CredRevReqTBEX */,
    553 /* setct-CredRevReqTBS */,
    554 /* setct-CredRevReqTBSX */,
    555 /* setct-CredRevResData */,
    591 /* setct-CredRevResTBE */,
    567 /* setct-ErrorTBS */,
    526 /* setct-HODInput */,
    561 /* setct-MeAqCInitResTBS */,
    522 /* setct-OIData */,
    519 /* setct-PANData */,
    521 /* setct-PANOnly */,
    520 /* setct-PANToken */,
    556 /* setct-PCertReqData */,
    557 /* setct-PCertResTBS */,
    523 /* setct-PI */,
    532 /* setct-PI-TBS */,
    524 /* setct-PIData */,
    525 /* setct-PIDataUnsigned */,
    568 /* setct-PIDualSignedTBE */,
    569 /* setct-PIUnsignedTBE */,
    531 /* setct-PInitResData */,
    533 /* setct-PResData */,
    594 /* setct-RegFormReqTBE */,
    562 /* setct-RegFormResTBS */,
    604 /* setext-pinAny */,
    603 /* setext-pinSecure */,
    605 /* setext-track2 */,
    41 /* sha */,
    64 /* sha1 */,
    115 /* sha1WithRSA */,
    65 /* sha1WithRSAEncryption */,
    675 /* sha224 */,
    671 /* sha224WithRSAEncryption */,
    672 /* sha256 */,
    668 /* sha256WithRSAEncryption */,
    673 /* sha384 */,
    669 /* sha384WithRSAEncryption */,
    674 /* sha512 */,
    670 /* sha512WithRSAEncryption */,
    42 /* shaWithRSAEncryption */,
    52 /* signingTime */,
    454 /* simpleSecurityObject */,
    496 /* singleLevelQuality */,
    16 /* stateOrProvinceName */,
    660 /* streetAddress */,
    498 /* subtreeMaximumQuality */,
    497 /* subtreeMinimumQuality */,
    890 /* supportedAlgorithms */,
    874 /* supportedApplicationContext */,
    100 /* surname */,
    864 /* telephoneNumber */,
    866 /* teletexTerminalIdentifier */,
    865 /* telexNumber */,
    459 /* textEncodedORAddress */,
    293 /* textNotice */,
    106 /* title */,
    682 /* tpBasis */,
    436 /* ucl */,
    0 /* undefined */,
    888 /* uniqueMember */,
    55 /* unstructuredAddress */,
    49 /* unstructuredName */,
    880 /* userCertificate */,
    465 /* userClass */,
    458 /* userId */,
    879 /* userPassword */,
    373 /* valid */,
    678 /* wap */,
    679 /* wap-wsg */,
    735 /* wap-wsg-idm-ecid-wtls1 */,
    743 /* wap-wsg-idm-ecid-wtls10 */,
    744 /* wap-wsg-idm-ecid-wtls11 */,
    745 /* wap-wsg-idm-ecid-wtls12 */,
    736 /* wap-wsg-idm-ecid-wtls3 */,
    737 /* wap-wsg-idm-ecid-wtls4 */,
    738 /* wap-wsg-idm-ecid-wtls5 */,
    739 /* wap-wsg-idm-ecid-wtls6 */,
    740 /* wap-wsg-idm-ecid-wtls7 */,
    741 /* wap-wsg-idm-ecid-wtls8 */,
    742 /* wap-wsg-idm-ecid-wtls9 */,
    804 /* whirlpool */,
    868 /* x121Address */,
    503 /* x500UniqueIdentifier */,
    158 /* x509Certificate */,
    160 /* x509Crl */,
    125 /* zlib compression */,
};

static const uint16_t kNIDsInOIDOrder[] = {
    434 /* 0.9 (OBJ_data) */,
    182 /* 1.2 (OBJ_member_body) */,
    676 /* 1.3 (OBJ_identified_organization) */,
    379 /* 1.3 (OBJ_org) */,
    11 /* 2.5 (OBJ_X500) */,
    647 /* 2.23 (OBJ_international_organizations) */,
    380 /* 1.3.6 (OBJ_dod) */,
    12 /* 2.5.4 (OBJ_X509) */,
    378 /* 2.5.8 (OBJ_X500algorithms) */,
    81 /* 2.5.29 (OBJ_id_ce) */,
    512 /* 2.23.42 (OBJ_id_set) */,
    678 /* 2.23.43 (OBJ_wap) */,
    435 /* 0.9.2342 (OBJ_pss) */,
    183 /* 1.2.840 (OBJ_ISO_US) */,
    381 /* ******* (OBJ_iana) */,
    949 /* *********** (OBJ_ED25519) */,
    960 /* *********** (OBJ_ED448) */,
    677 /* 1.3.132 (OBJ_certicom_arc) */,
    394 /* ******* (OBJ_selected_attribute_types) */,
    13 /* ******* (OBJ_commonName) */,
    100 /* ******* (OBJ_surname) */,
    105 /* ******* (OBJ_serialNumber) */,
    14 /* ******* (OBJ_countryName) */,
    15 /* ******* (OBJ_localityName) */,
    16 /* ******* (OBJ_stateOrProvinceName) */,
    660 /* ******* (OBJ_streetAddress) */,
    17 /* ******** (OBJ_organizationName) */,
    18 /* ******** (OBJ_organizationalUnitName) */,
    106 /* ******** (OBJ_title) */,
    107 /* ******** (OBJ_description) */,
    859 /* ******** (OBJ_searchGuide) */,
    860 /* ******** (OBJ_businessCategory) */,
    861 /* ******** (OBJ_postalAddress) */,
    661 /* ******** (OBJ_postalCode) */,
    862 /* ******** (OBJ_postOfficeBox) */,
    863 /* ******** (OBJ_physicalDeliveryOfficeName) */,
    864 /* ******** (OBJ_telephoneNumber) */,
    865 /* ******** (OBJ_telexNumber) */,
    866 /* ******** (OBJ_teletexTerminalIdentifier) */,
    867 /* ******** (OBJ_facsimileTelephoneNumber) */,
    868 /* ******** (OBJ_x121Address) */,
    869 /* ******** (OBJ_internationaliSDNNumber) */,
    870 /* ******** (OBJ_registeredAddress) */,
    871 /* ******** (OBJ_destinationIndicator) */,
    872 /* ******** (OBJ_preferredDeliveryMethod) */,
    873 /* ******** (OBJ_presentationAddress) */,
    874 /* *******0 (OBJ_supportedApplicationContext) */,
    875 /* *******1 (OBJ_member) */,
    876 /* *******2 (OBJ_owner) */,
    877 /* *******3 (OBJ_roleOccupant) */,
    878 /* *******4 (OBJ_seeAlso) */,
    879 /* *******5 (OBJ_userPassword) */,
    880 /* *******6 (OBJ_userCertificate) */,
    881 /* *******7 (OBJ_cACertificate) */,
    882 /* *******8 (OBJ_authorityRevocationList) */,
    883 /* *******9 (OBJ_certificateRevocationList) */,
    884 /* *******0 (OBJ_crossCertificatePair) */,
    173 /* *******1 (OBJ_name) */,
    99 /* ******** (OBJ_givenName) */,
    101 /* ******** (OBJ_initials) */,
    509 /* ******** (OBJ_generationQualifier) */,
    503 /* ******** (OBJ_x500UniqueIdentifier) */,
    174 /* ******** (OBJ_dnQualifier) */,
    885 /* ******** (OBJ_enhancedSearchGuide) */,
    886 /* ******** (OBJ_protocolInformation) */,
    887 /* ******** (OBJ_distinguishedName) */,
    888 /* ******** (OBJ_uniqueMember) */,
    889 /* ******** (OBJ_houseIdentifier) */,
    890 /* *******2 (OBJ_supportedAlgorithms) */,
    891 /* *******3 (OBJ_deltaRevocationList) */,
    892 /* *******4 (OBJ_dmdName) */,
    510 /* *******5 (OBJ_pseudonym) */,
    400 /* *******2 (OBJ_role) */,
    769 /* 2.5.29.9 (OBJ_subject_directory_attributes) */,
    82 /* 2.5.29.14 (OBJ_subject_key_identifier) */,
    83 /* 2.5.29.15 (OBJ_key_usage) */,
    84 /* 2.5.29.16 (OBJ_private_key_usage_period) */,
    85 /* 2.5.29.17 (OBJ_subject_alt_name) */,
    86 /* 2.5.29.18 (OBJ_issuer_alt_name) */,
    87 /* 2.5.29.19 (OBJ_basic_constraints) */,
    88 /* 2.5.29.20 (OBJ_crl_number) */,
    141 /* 2.5.29.21 (OBJ_crl_reason) */,
    430 /* 2.5.29.23 (OBJ_hold_instruction_code) */,
    142 /* 2.5.29.24 (OBJ_invalidity_date) */,
    140 /* 2.5.29.27 (OBJ_delta_crl) */,
    770 /* 2.5.29.28 (OBJ_issuing_distribution_point) */,
    771 /* 2.5.29.29 (OBJ_certificate_issuer) */,
    666 /* 2.5.29.30 (OBJ_name_constraints) */,
    103 /* 2.5.29.31 (OBJ_crl_distribution_points) */,
    89 /* 2.5.29.32 (OBJ_certificate_policies) */,
    747 /* 2.5.29.33 (OBJ_policy_mappings) */,
    90 /* 2.5.29.35 (OBJ_authority_key_identifier) */,
    401 /* 2.5.29.36 (OBJ_policy_constraints) */,
    126 /* 2.5.29.37 (OBJ_ext_key_usage) */,
    857 /* 2.5.29.46 (OBJ_freshest_crl) */,
    748 /* 2.5.29.54 (OBJ_inhibit_any_policy) */,
    402 /* 2.5.29.55 (OBJ_target_information) */,
    403 /* 2.5.29.56 (OBJ_no_rev_avail) */,
    513 /* 2.23.42.0 (OBJ_set_ctype) */,
    514 /* 2.23.42.1 (OBJ_set_msgExt) */,
    515 /* ********* (OBJ_set_attr) */,
    516 /* 2.23.42.5 (OBJ_set_policy) */,
    517 /* 2.23.42.7 (OBJ_set_certExt) */,
    518 /* 2.23.42.8 (OBJ_set_brand) */,
    679 /* 2.23.43.1 (OBJ_wap_wsg) */,
    382 /* *******.1 (OBJ_Directory) */,
    383 /* *******.2 (OBJ_Management) */,
    384 /* *******.3 (OBJ_Experimental) */,
    385 /* *******.4 (OBJ_Private) */,
    386 /* *******.5 (OBJ_Security) */,
    387 /* *******.6 (OBJ_SNMPv2) */,
    388 /* *******.7 (OBJ_Mail) */,
    376 /* ********.2 (OBJ_algorithm) */,
    395 /* *******.55 (OBJ_clearance) */,
    19 /* 2.5.8.1.1 (OBJ_rsa) */,
    96 /* 2.5.8.3.100 (OBJ_mdc2WithRSA) */,
    95 /* 2.5.8.3.101 (OBJ_mdc2) */,
    746 /* 2.5.29.32.0 (OBJ_any_policy) */,
    910 /* 2.5.29.37.0 (OBJ_anyExtendedKeyUsage) */,
    519 /* 2.23.42.0.0 (OBJ_setct_PANData) */,
    520 /* 2.23.42.0.1 (OBJ_setct_PANToken) */,
    521 /* 2.23.42.0.2 (OBJ_setct_PANOnly) */,
    522 /* 2.23.42.0.3 (OBJ_setct_OIData) */,
    523 /* 2.23.42.0.4 (OBJ_setct_PI) */,
    524 /* 2.23.42.0.5 (OBJ_setct_PIData) */,
    525 /* 2.23.42.0.6 (OBJ_setct_PIDataUnsigned) */,
    526 /* 2.23.42.0.7 (OBJ_setct_HODInput) */,
    527 /* 2.23.42.0.8 (OBJ_setct_AuthResBaggage) */,
    528 /* 2.23.42.0.9 (OBJ_setct_AuthRevReqBaggage) */,
    529 /* 2.23.42.0.10 (OBJ_setct_AuthRevResBaggage) */,
    530 /* 2.23.42.0.11 (OBJ_setct_CapTokenSeq) */,
    531 /* 2.23.42.0.12 (OBJ_setct_PInitResData) */,
    532 /* 2.23.42.0.13 (OBJ_setct_PI_TBS) */,
    533 /* 2.23.42.0.14 (OBJ_setct_PResData) */,
    534 /* 2.23.42.0.16 (OBJ_setct_AuthReqTBS) */,
    535 /* 2.23.42.0.17 (OBJ_setct_AuthResTBS) */,
    536 /* 2.23.42.0.18 (OBJ_setct_AuthResTBSX) */,
    537 /* 2.23.42.0.19 (OBJ_setct_AuthTokenTBS) */,
    538 /* 2.23.42.0.20 (OBJ_setct_CapTokenData) */,
    539 /* 2.23.42.0.21 (OBJ_setct_CapTokenTBS) */,
    540 /* 2.23.42.0.22 (OBJ_setct_AcqCardCodeMsg) */,
    541 /* 2.23.42.0.23 (OBJ_setct_AuthRevReqTBS) */,
    542 /* 2.23.42.0.24 (OBJ_setct_AuthRevResData) */,
    543 /* 2.23.42.0.25 (OBJ_setct_AuthRevResTBS) */,
    544 /* 2.23.42.0.26 (OBJ_setct_CapReqTBS) */,
    545 /* 2.23.42.0.27 (OBJ_setct_CapReqTBSX) */,
    546 /* 2.23.42.0.28 (OBJ_setct_CapResData) */,
    547 /* 2.23.42.0.29 (OBJ_setct_CapRevReqTBS) */,
    548 /* 2.23.42.0.30 (OBJ_setct_CapRevReqTBSX) */,
    549 /* 2.23.42.0.31 (OBJ_setct_CapRevResData) */,
    550 /* 2.23.42.0.32 (OBJ_setct_CredReqTBS) */,
    551 /* 2.23.42.0.33 (OBJ_setct_CredReqTBSX) */,
    552 /* 2.23.42.0.34 (OBJ_setct_CredResData) */,
    553 /* 2.23.42.0.35 (OBJ_setct_CredRevReqTBS) */,
    554 /* 2.23.42.0.36 (OBJ_setct_CredRevReqTBSX) */,
    555 /* 2.23.42.0.37 (OBJ_setct_CredRevResData) */,
    556 /* 2.23.42.0.38 (OBJ_setct_PCertReqData) */,
    557 /* 2.23.42.0.39 (OBJ_setct_PCertResTBS) */,
    558 /* 2.23.42.0.40 (OBJ_setct_BatchAdminReqData) */,
    559 /* 2.23.42.0.41 (OBJ_setct_BatchAdminResData) */,
    560 /* 2.23.42.0.42 (OBJ_setct_CardCInitResTBS) */,
    561 /* 2.23.42.0.43 (OBJ_setct_MeAqCInitResTBS) */,
    562 /* 2.23.42.0.44 (OBJ_setct_RegFormResTBS) */,
    563 /* 2.23.42.0.45 (OBJ_setct_CertReqData) */,
    564 /* 2.23.42.0.46 (OBJ_setct_CertReqTBS) */,
    565 /* 2.23.42.0.47 (OBJ_setct_CertResData) */,
    566 /* 2.23.42.0.48 (OBJ_setct_CertInqReqTBS) */,
    567 /* 2.23.42.0.49 (OBJ_setct_ErrorTBS) */,
    568 /* 2.23.42.0.50 (OBJ_setct_PIDualSignedTBE) */,
    569 /* 2.23.42.0.51 (OBJ_setct_PIUnsignedTBE) */,
    570 /* 2.23.42.0.52 (OBJ_setct_AuthReqTBE) */,
    571 /* 2.23.42.0.53 (OBJ_setct_AuthResTBE) */,
    572 /* 2.23.42.0.54 (OBJ_setct_AuthResTBEX) */,
    573 /* 2.23.42.0.55 (OBJ_setct_AuthTokenTBE) */,
    574 /* 2.23.42.0.56 (OBJ_setct_CapTokenTBE) */,
    575 /* 2.23.42.0.57 (OBJ_setct_CapTokenTBEX) */,
    576 /* 2.23.42.0.58 (OBJ_setct_AcqCardCodeMsgTBE) */,
    577 /* 2.23.42.0.59 (OBJ_setct_AuthRevReqTBE) */,
    578 /* 2.23.42.0.60 (OBJ_setct_AuthRevResTBE) */,
    579 /* 2.23.42.0.61 (OBJ_setct_AuthRevResTBEB) */,
    580 /* 2.23.42.0.62 (OBJ_setct_CapReqTBE) */,
    581 /* 2.23.42.0.63 (OBJ_setct_CapReqTBEX) */,
    582 /* 2.23.42.0.64 (OBJ_setct_CapResTBE) */,
    583 /* 2.23.42.0.65 (OBJ_setct_CapRevReqTBE) */,
    584 /* 2.23.42.0.66 (OBJ_setct_CapRevReqTBEX) */,
    585 /* 2.23.42.0.67 (OBJ_setct_CapRevResTBE) */,
    586 /* 2.23.42.0.68 (OBJ_setct_CredReqTBE) */,
    587 /* 2.23.42.0.69 (OBJ_setct_CredReqTBEX) */,
    588 /* 2.23.42.0.70 (OBJ_setct_CredResTBE) */,
    589 /* 2.23.42.0.71 (OBJ_setct_CredRevReqTBE) */,
    590 /* 2.23.42.0.72 (OBJ_setct_CredRevReqTBEX) */,
    591 /* 2.23.42.0.73 (OBJ_setct_CredRevResTBE) */,
    592 /* 2.23.42.0.74 (OBJ_setct_BatchAdminReqTBE) */,
    593 /* 2.23.42.0.75 (OBJ_setct_BatchAdminResTBE) */,
    594 /* 2.23.42.0.76 (OBJ_setct_RegFormReqTBE) */,
    595 /* 2.23.42.0.77 (OBJ_setct_CertReqTBE) */,
    596 /* 2.23.42.0.78 (OBJ_setct_CertReqTBEX) */,
    597 /* 2.23.42.0.79 (OBJ_setct_CertResTBE) */,
    598 /* 2.23.42.0.80 (OBJ_setct_CRLNotificationTBS) */,
    599 /* 2.23.42.0.81 (OBJ_setct_CRLNotificationResTBS) */,
    600 /* 2.23.42.0.82 (OBJ_setct_BCIDistributionTBS) */,
    601 /* 2.23.42.1.1 (OBJ_setext_genCrypt) */,
    602 /* 2.23.42.1.3 (OBJ_setext_miAuth) */,
    603 /* 2.23.42.1.4 (OBJ_setext_pinSecure) */,
    604 /* 2.23.42.1.5 (OBJ_setext_pinAny) */,
    605 /* 2.23.42.1.7 (OBJ_setext_track2) */,
    606 /* 2.23.42.1.8 (OBJ_setext_cv) */,
    620 /* *********.0 (OBJ_setAttr_Cert) */,
    621 /* *********.1 (OBJ_setAttr_PGWYcap) */,
    622 /* *********.2 (OBJ_setAttr_TokenType) */,
    623 /* *********.3 (OBJ_setAttr_IssCap) */,
    607 /* 2.23.42.5.0 (OBJ_set_policy_root) */,
    608 /* 2.23.42.7.0 (OBJ_setCext_hashedRoot) */,
    609 /* 2.23.42.7.1 (OBJ_setCext_certType) */,
    610 /* 2.23.42.7.2 (OBJ_setCext_merchData) */,
    611 /* 2.23.42.7.3 (OBJ_setCext_cCertRequired) */,
    612 /* 2.23.42.7.4 (OBJ_setCext_tunneling) */,
    613 /* 2.23.42.7.5 (OBJ_setCext_setExt) */,
    614 /* 2.23.42.7.6 (OBJ_setCext_setQualf) */,
    615 /* 2.23.42.7.7 (OBJ_setCext_PGWYcapabilities) */,
    616 /* 2.23.42.7.8 (OBJ_setCext_TokenIdentifier) */,
    617 /* 2.23.42.7.9 (OBJ_setCext_Track2Data) */,
    618 /* 2.23.42.7.10 (OBJ_setCext_TokenType) */,
    619 /* 2.23.42.7.11 (OBJ_setCext_IssuerCapabilities) */,
    636 /* 2.23.42.8.1 (OBJ_set_brand_IATA_ATA) */,
    640 /* 2.23.42.8.4 (OBJ_set_brand_Visa) */,
    641 /* 2.23.42.8.5 (OBJ_set_brand_MasterCard) */,
    637 /* 2.23.42.8.30 (OBJ_set_brand_Diners) */,
    638 /* 2.23.42.8.34 (OBJ_set_brand_AmericanExpress) */,
    639 /* 2.23.42.8.35 (OBJ_set_brand_JCB) */,
    805 /* 1.2.643.2.2 (OBJ_cryptopro) */,
    806 /* 1.2.643.2.9 (OBJ_cryptocom) */,
    184 /* 1.2.840.10040 (OBJ_X9_57) */,
    405 /* 1.2.840.10045 (OBJ_ansi_X9_62) */,
    389 /* *******.4.1 (OBJ_Enterprises) */,
    504 /* *******.7.1 (OBJ_mime_mhs) */,
    104 /* ********.2.3 (OBJ_md5WithRSA) */,
    29 /* ********.2.6 (OBJ_des_ecb) */,
    31 /* ********.2.7 (OBJ_des_cbc) */,
    45 /* ********.2.8 (OBJ_des_ofb64) */,
    30 /* ********.2.9 (OBJ_des_cfb64) */,
    377 /* ********.2.11 (OBJ_rsaSignature) */,
    67 /* ********.2.12 (OBJ_dsa_2) */,
    66 /* ********.2.13 (OBJ_dsaWithSHA) */,
    42 /* ********.2.15 (OBJ_shaWithRSAEncryption) */,
    32 /* ********.2.17 (OBJ_des_ede_ecb) */,
    41 /* ********.2.18 (OBJ_sha) */,
    64 /* ********.2.26 (OBJ_sha1) */,
    70 /* ********.2.27 (OBJ_dsaWithSHA1_2) */,
    115 /* ********.2.29 (OBJ_sha1WithRSA) */,
    117 /* ********.2.1 (OBJ_ripemd160) */,
    143 /* *********.4.1 (OBJ_sxnet) */,
    721 /* *********.1 (OBJ_sect163k1) */,
    722 /* *********.2 (OBJ_sect163r1) */,
    728 /* *********.3 (OBJ_sect239k1) */,
    717 /* *********.4 (OBJ_sect113r1) */,
    718 /* *********.5 (OBJ_sect113r2) */,
    704 /* *********.6 (OBJ_secp112r1) */,
    705 /* *********.7 (OBJ_secp112r2) */,
    709 /* *********.8 (OBJ_secp160r1) */,
    708 /* *********.9 (OBJ_secp160k1) */,
    714 /* *********.10 (OBJ_secp256k1) */,
    723 /* *********.15 (OBJ_sect163r2) */,
    729 /* *********.16 (OBJ_sect283k1) */,
    730 /* *********.17 (OBJ_sect283r1) */,
    719 /* *********.22 (OBJ_sect131r1) */,
    720 /* *********.23 (OBJ_sect131r2) */,
    724 /* *********.24 (OBJ_sect193r1) */,
    725 /* *********.25 (OBJ_sect193r2) */,
    726 /* *********.26 (OBJ_sect233k1) */,
    727 /* *********.27 (OBJ_sect233r1) */,
    706 /* *********.28 (OBJ_secp128r1) */,
    707 /* *********.29 (OBJ_secp128r2) */,
    710 /* *********.30 (OBJ_secp160r2) */,
    711 /* *********.31 (OBJ_secp192k1) */,
    712 /* *********.32 (OBJ_secp224k1) */,
    713 /* *********.33 (OBJ_secp224r1) */,
    715 /* *********.34 (OBJ_secp384r1) */,
    716 /* *********.35 (OBJ_secp521r1) */,
    731 /* *********.36 (OBJ_sect409k1) */,
    732 /* *********.37 (OBJ_sect409r1) */,
    733 /* *********.38 (OBJ_sect571k1) */,
    734 /* *********.39 (OBJ_sect571r1) */,
    624 /* *********.0.0 (OBJ_set_rootKeyThumb) */,
    625 /* *********.0.1 (OBJ_set_addPolicy) */,
    626 /* *********.2.1 (OBJ_setAttr_Token_EMV) */,
    627 /* *********.2.2 (OBJ_setAttr_Token_B0Prime) */,
    628 /* *********.3.3 (OBJ_setAttr_IssCap_CVM) */,
    629 /* *********.3.4 (OBJ_setAttr_IssCap_T2) */,
    630 /* *********.3.5 (OBJ_setAttr_IssCap_Sig) */,
    642 /* 2.23.42.8.6011 (OBJ_set_brand_Novus) */,
    735 /* 2.23.43.1.4.1 (OBJ_wap_wsg_idm_ecid_wtls1) */,
    736 /* 2.23.43.1.4.3 (OBJ_wap_wsg_idm_ecid_wtls3) */,
    737 /* 2.23.43.1.4.4 (OBJ_wap_wsg_idm_ecid_wtls4) */,
    738 /* 2.23.43.1.4.5 (OBJ_wap_wsg_idm_ecid_wtls5) */,
    739 /* 2.23.43.1.4.6 (OBJ_wap_wsg_idm_ecid_wtls6) */,
    740 /* 2.23.43.1.4.7 (OBJ_wap_wsg_idm_ecid_wtls7) */,
    741 /* 2.23.43.1.4.8 (OBJ_wap_wsg_idm_ecid_wtls8) */,
    742 /* 2.23.43.1.4.9 (OBJ_wap_wsg_idm_ecid_wtls9) */,
    743 /* 2.23.43.1.4.10 (OBJ_wap_wsg_idm_ecid_wtls10) */,
    744 /* 2.23.43.1.4.11 (OBJ_wap_wsg_idm_ecid_wtls11) */,
    745 /* 2.23.43.1.4.12 (OBJ_wap_wsg_idm_ecid_wtls12) */,
    804 /* 1.0.10118.3.0.55 (OBJ_whirlpool) */,
    773 /* 1.2.410.200004 (OBJ_kisa) */,
    807 /* 1.2.643.2.2.3 (OBJ_id_GostR3411_94_with_GostR3410_2001) */,
    808 /* 1.2.643.2.2.4 (OBJ_id_GostR3411_94_with_GostR3410_94) */,
    809 /* 1.2.643.2.2.9 (OBJ_id_GostR3411_94) */,
    810 /* 1.2.643.2.2.10 (OBJ_id_HMACGostR3411_94) */,
    811 /* 1.2.643.2.2.19 (OBJ_id_GostR3410_2001) */,
    812 /* 1.2.643.2.2.20 (OBJ_id_GostR3410_94) */,
    813 /* 1.2.643.2.2.21 (OBJ_id_Gost28147_89) */,
    815 /* 1.2.643.2.2.22 (OBJ_id_Gost28147_89_MAC) */,
    816 /* 1.2.643.2.2.23 (OBJ_id_GostR3411_94_prf) */,
    817 /* 1.2.643.2.2.98 (OBJ_id_GostR3410_2001DH) */,
    818 /* 1.2.643.2.2.99 (OBJ_id_GostR3410_94DH) */,
    1 /* 1.2.840.113549 (OBJ_rsadsi) */,
    185 /* 1.2.840.10040.4 (OBJ_X9cm) */,
    127 /* *******.5.5.7 (OBJ_id_pkix) */,
    505 /* *******.7.1.1 (OBJ_mime_mhs_headings) */,
    506 /* *******.7.1.2 (OBJ_mime_mhs_bodies) */,
    119 /* ********.3.1.2 (OBJ_ripemd160WithRSA) */,
    937 /* *********.11.0 (OBJ_dhSinglePass_stdDH_sha224kdf_scheme) */,
    938 /* *********.11.1 (OBJ_dhSinglePass_stdDH_sha256kdf_scheme) */,
    939 /* *********.11.2 (OBJ_dhSinglePass_stdDH_sha384kdf_scheme) */,
    940 /* *********.11.3 (OBJ_dhSinglePass_stdDH_sha512kdf_scheme) */,
    942 /* *********.14.0 (OBJ_dhSinglePass_cofactorDH_sha224kdf_scheme) */,
    943 /* *********.14.1 (OBJ_dhSinglePass_cofactorDH_sha256kdf_scheme) */,
    944 /* *********.14.2 (OBJ_dhSinglePass_cofactorDH_sha384kdf_scheme) */,
    945 /* *********.14.3 (OBJ_dhSinglePass_cofactorDH_sha512kdf_scheme) */,
    631 /* *********.3.3.1 (OBJ_setAttr_GenCryptgrm) */,
    632 /* *********.3.4.1 (OBJ_setAttr_T2Enc) */,
    633 /* *********.3.4.2 (OBJ_setAttr_T2cleartxt) */,
    634 /* *********.3.5.1 (OBJ_setAttr_TokICCsig) */,
    635 /* *********.3.5.2 (OBJ_setAttr_SecDevSig) */,
    436 /* 0.9.2342.******** (OBJ_ucl) */,
    820 /* 1.2.643.******** (OBJ_id_Gost28147_89_None_KeyMeshing) */,
    819 /* 1.2.643.2.2.14.1 (OBJ_id_Gost28147_89_CryptoPro_KeyMeshing) */,
    845 /* 1.2.643.2.2.20.1 (OBJ_id_GostR3410_94_a) */,
    846 /* 1.2.643.2.2.20.2 (OBJ_id_GostR3410_94_aBis) */,
    847 /* 1.2.643.2.2.20.3 (OBJ_id_GostR3410_94_b) */,
    848 /* 1.2.643.2.2.20.4 (OBJ_id_GostR3410_94_bBis) */,
    821 /* 1.2.643.2.2.30.0 (OBJ_id_GostR3411_94_TestParamSet) */,
    822 /* 1.2.643.2.2.30.1 (OBJ_id_GostR3411_94_CryptoProParamSet) */,
    823 /* 1.2.643.2.2.31.0 (OBJ_id_Gost28147_89_TestParamSet) */,
    824 /* 1.2.643.2.2.31.1 (OBJ_id_Gost28147_89_CryptoPro_A_ParamSet) */,
    825 /* 1.2.643.2.2.31.2 (OBJ_id_Gost28147_89_CryptoPro_B_ParamSet) */,
    826 /* 1.2.643.2.2.31.3 (OBJ_id_Gost28147_89_CryptoPro_C_ParamSet) */,
    827 /* 1.2.643.2.2.31.4 (OBJ_id_Gost28147_89_CryptoPro_D_ParamSet) */,
    828 /* 1.2.643.2.2.31.5 (OBJ_id_Gost28147_89_CryptoPro_Oscar_1_1_ParamSet)
         */
    ,
    829 /* 1.2.643.2.2.31.6 (OBJ_id_Gost28147_89_CryptoPro_Oscar_1_0_ParamSet)
         */
    ,
    830 /* 1.2.643.2.2.31.7 (OBJ_id_Gost28147_89_CryptoPro_RIC_1_ParamSet) */,
    831 /* 1.2.643.2.2.32.0 (OBJ_id_GostR3410_94_TestParamSet) */,
    832 /* 1.2.643.2.2.32.2 (OBJ_id_GostR3410_94_CryptoPro_A_ParamSet) */,
    833 /* 1.2.643.2.2.32.3 (OBJ_id_GostR3410_94_CryptoPro_B_ParamSet) */,
    834 /* 1.2.643.2.2.32.4 (OBJ_id_GostR3410_94_CryptoPro_C_ParamSet) */,
    835 /* 1.2.643.2.2.32.5 (OBJ_id_GostR3410_94_CryptoPro_D_ParamSet) */,
    836 /* 1.2.643.2.2.33.1 (OBJ_id_GostR3410_94_CryptoPro_XchA_ParamSet) */,
    837 /* 1.2.643.2.2.33.2 (OBJ_id_GostR3410_94_CryptoPro_XchB_ParamSet) */,
    838 /* 1.2.643.2.2.33.3 (OBJ_id_GostR3410_94_CryptoPro_XchC_ParamSet) */,
    839 /* 1.2.643.2.2.35.0 (OBJ_id_GostR3410_2001_TestParamSet) */,
    840 /* 1.2.643.2.2.35.1 (OBJ_id_GostR3410_2001_CryptoPro_A_ParamSet) */,
    841 /* 1.2.643.2.2.35.2 (OBJ_id_GostR3410_2001_CryptoPro_B_ParamSet) */,
    842 /* 1.2.643.2.2.35.3 (OBJ_id_GostR3410_2001_CryptoPro_C_ParamSet) */,
    843 /* 1.2.643.2.2.36.0 (OBJ_id_GostR3410_2001_CryptoPro_XchA_ParamSet) */,
    844 /* 1.2.643.2.2.36.1 (OBJ_id_GostR3410_2001_CryptoPro_XchB_ParamSet) */,
    2 /* 1.2.840.113549.1 (OBJ_pkcs) */,
    431 /* 1.2.840.10040.2.1 (OBJ_hold_instruction_none) */,
    432 /* 1.2.840.10040.2.2 (OBJ_hold_instruction_call_issuer) */,
    433 /* 1.2.840.10040.2.3 (OBJ_hold_instruction_reject) */,
    116 /* 1.2.840.10040.4.1 (OBJ_dsa) */,
    113 /* 1.2.840.10040.4.3 (OBJ_dsaWithSHA1) */,
    406 /* 1.2.840.10045.1.1 (OBJ_X9_62_prime_field) */,
    407 /* 1.2.840.10045.1.2 (OBJ_X9_62_characteristic_two_field) */,
    408 /* 1.2.840.10045.2.1 (OBJ_X9_62_id_ecPublicKey) */,
    416 /* 1.2.840.10045.4.1 (OBJ_ecdsa_with_SHA1) */,
    791 /* 1.2.840.10045.4.2 (OBJ_ecdsa_with_Recommended) */,
    792 /* 1.2.840.10045.4.3 (OBJ_ecdsa_with_Specified) */,
    920 /* 1.2.840.10046.2.1 (OBJ_dhpublicnumber) */,
    258 /* *******.******* (OBJ_id_pkix_mod) */,
    175 /* *******.******* (OBJ_id_pe) */,
    259 /* *******.******* (OBJ_id_qt) */,
    128 /* *******.******* (OBJ_id_kp) */,
    260 /* *******.******* (OBJ_id_it) */,
    261 /* *******.******* (OBJ_id_pkip) */,
    262 /* *******.******* (OBJ_id_alg) */,
    263 /* *******.******* (OBJ_id_cmc) */,
    264 /* *******.******* (OBJ_id_on) */,
    265 /* *******.******* (OBJ_id_pda) */,
    266 /* *******.*******0 (OBJ_id_aca) */,
    267 /* *******.*******1 (OBJ_id_qcs) */,
    268 /* *******.*******2 (OBJ_id_cct) */,
    662 /* *******.*******1 (OBJ_id_ppl) */,
    176 /* *******.*******8 (OBJ_id_ad) */,
    507 /* *******.******* (OBJ_id_hex_partial_message) */,
    508 /* *******.******* (OBJ_id_hex_multipart_message) */,
    57 /* 2.16.840.1.113730 (OBJ_netscape) */,
    754 /* 0.3.4401.*******.1 (OBJ_camellia_128_ecb) */,
    766 /* 0.3.4401.*******.3 (OBJ_camellia_128_ofb128) */,
    757 /* 0.3.4401.*******.4 (OBJ_camellia_128_cfb128) */,
    755 /* 0.3.4401.*******.21 (OBJ_camellia_192_ecb) */,
    767 /* 0.3.4401.*******.23 (OBJ_camellia_192_ofb128) */,
    758 /* 0.3.4401.*******.24 (OBJ_camellia_192_cfb128) */,
    756 /* 0.3.4401.*******.41 (OBJ_camellia_256_ecb) */,
    768 /* 0.3.4401.*******.43 (OBJ_camellia_256_ofb128) */,
    759 /* 0.3.4401.*******.44 (OBJ_camellia_256_cfb128) */,
    437 /* 0.9.2342.********.100 (OBJ_pilot) */,
    776 /* 1.2.410.200004.1.3 (OBJ_seed_ecb) */,
    777 /* 1.2.410.200004.1.4 (OBJ_seed_cbc) */,
    779 /* 1.2.410.200004.1.5 (OBJ_seed_cfb128) */,
    778 /* 1.2.410.200004.1.6 (OBJ_seed_ofb128) */,
    852 /* 1.2.643.*******.3 (OBJ_id_GostR3411_94_with_GostR3410_94_cc) */,
    853 /* 1.2.643.*******.4 (OBJ_id_GostR3411_94_with_GostR3410_2001_cc) */,
    850 /* 1.2.643.2.9.1.5.3 (OBJ_id_GostR3410_94_cc) */,
    851 /* 1.2.643.2.9.1.5.4 (OBJ_id_GostR3410_2001_cc) */,
    849 /* 1.2.643.2.9.1.6.1 (OBJ_id_Gost28147_89_cc) */,
    854 /* 1.2.643.2.9.1.8.1 (OBJ_id_GostR3410_2001_ParamSet_cc) */,
    186 /* 1.2.840.113549.1.1 (OBJ_pkcs1) */,
    27 /* 1.2.840.113549.1.3 (OBJ_pkcs3) */,
    187 /* 1.2.840.113549.1.5 (OBJ_pkcs5) */,
    20 /* 1.2.840.113549.1.7 (OBJ_pkcs7) */,
    47 /* 1.2.840.113549.1.9 (OBJ_pkcs9) */,
    3 /* 1.2.840.113549.2.2 (OBJ_md2) */,
    257 /* 1.2.840.113549.2.4 (OBJ_md4) */,
    4 /* 1.2.840.113549.2.5 (OBJ_md5) */,
    797 /* 1.2.840.113549.2.6 (OBJ_hmacWithMD5) */,
    163 /* 1.2.840.113549.2.7 (OBJ_hmacWithSHA1) */,
    798 /* 1.2.840.113549.2.8 (OBJ_hmacWithSHA224) */,
    799 /* 1.2.840.113549.2.9 (OBJ_hmacWithSHA256) */,
    800 /* 1.2.840.113549.2.10 (OBJ_hmacWithSHA384) */,
    801 /* 1.2.840.113549.2.11 (OBJ_hmacWithSHA512) */,
    37 /* 1.2.840.113549.3.2 (OBJ_rc2_cbc) */,
    5 /* 1.2.840.113549.3.4 (OBJ_rc4) */,
    44 /* 1.2.840.113549.3.7 (OBJ_des_ede3_cbc) */,
    120 /* 1.2.840.113549.3.8 (OBJ_rc5_cbc) */,
    643 /* 1.2.840.113549.3.10 (OBJ_des_cdmf) */,
    680 /* 1.2.840.10045.1.2.3 (OBJ_X9_62_id_characteristic_two_basis) */,
    684 /* 1.2.840.10045.3.0.1 (OBJ_X9_62_c2pnb163v1) */,
    685 /* 1.2.840.10045.3.0.2 (OBJ_X9_62_c2pnb163v2) */,
    686 /* 1.2.840.10045.3.0.3 (OBJ_X9_62_c2pnb163v3) */,
    687 /* 1.2.840.10045.3.0.4 (OBJ_X9_62_c2pnb176v1) */,
    688 /* 1.2.840.10045.3.0.5 (OBJ_X9_62_c2tnb191v1) */,
    689 /* 1.2.840.10045.3.0.6 (OBJ_X9_62_c2tnb191v2) */,
    690 /* 1.2.840.10045.3.0.7 (OBJ_X9_62_c2tnb191v3) */,
    691 /* 1.2.840.10045.3.0.8 (OBJ_X9_62_c2onb191v4) */,
    692 /* 1.2.840.10045.3.0.9 (OBJ_X9_62_c2onb191v5) */,
    693 /* 1.2.840.10045.3.0.10 (OBJ_X9_62_c2pnb208w1) */,
    694 /* 1.2.840.10045.3.0.11 (OBJ_X9_62_c2tnb239v1) */,
    695 /* 1.2.840.10045.3.0.12 (OBJ_X9_62_c2tnb239v2) */,
    696 /* 1.2.840.10045.3.0.13 (OBJ_X9_62_c2tnb239v3) */,
    697 /* 1.2.840.10045.3.0.14 (OBJ_X9_62_c2onb239v4) */,
    698 /* 1.2.840.10045.3.0.15 (OBJ_X9_62_c2onb239v5) */,
    699 /* 1.2.840.10045.3.0.16 (OBJ_X9_62_c2pnb272w1) */,
    700 /* 1.2.840.10045.3.0.17 (OBJ_X9_62_c2pnb304w1) */,
    701 /* 1.2.840.10045.3.0.18 (OBJ_X9_62_c2tnb359v1) */,
    702 /* 1.2.840.10045.3.0.19 (OBJ_X9_62_c2pnb368w1) */,
    703 /* 1.2.840.10045.3.0.20 (OBJ_X9_62_c2tnb431r1) */,
    409 /* 1.2.840.10045.3.1.1 (OBJ_X9_62_prime192v1) */,
    410 /* 1.2.840.10045.3.1.2 (OBJ_X9_62_prime192v2) */,
    411 /* 1.2.840.10045.3.1.3 (OBJ_X9_62_prime192v3) */,
    412 /* 1.2.840.10045.3.1.4 (OBJ_X9_62_prime239v1) */,
    413 /* 1.2.840.10045.3.1.5 (OBJ_X9_62_prime239v2) */,
    414 /* 1.2.840.10045.3.1.6 (OBJ_X9_62_prime239v3) */,
    415 /* 1.2.840.10045.3.1.7 (OBJ_X9_62_prime256v1) */,
    793 /* 1.2.840.10045.4.3.1 (OBJ_ecdsa_with_SHA224) */,
    794 /* 1.2.840.10045.4.3.2 (OBJ_ecdsa_with_SHA256) */,
    795 /* 1.2.840.10045.4.3.3 (OBJ_ecdsa_with_SHA384) */,
    796 /* 1.2.840.10045.4.3.4 (OBJ_ecdsa_with_SHA512) */,
    269 /* *******.*******.1 (OBJ_id_pkix1_explicit_88) */,
    270 /* *******.*******.2 (OBJ_id_pkix1_implicit_88) */,
    271 /* *******.*******.3 (OBJ_id_pkix1_explicit_93) */,
    272 /* *******.*******.4 (OBJ_id_pkix1_implicit_93) */,
    273 /* *******.*******.5 (OBJ_id_mod_crmf) */,
    274 /* *******.*******.6 (OBJ_id_mod_cmc) */,
    275 /* *******.*******.7 (OBJ_id_mod_kea_profile_88) */,
    276 /* *******.*******.8 (OBJ_id_mod_kea_profile_93) */,
    277 /* *******.*******.9 (OBJ_id_mod_cmp) */,
    278 /* *******.*******.10 (OBJ_id_mod_qualified_cert_88) */,
    279 /* *******.*******.11 (OBJ_id_mod_qualified_cert_93) */,
    280 /* *******.*******.12 (OBJ_id_mod_attribute_cert) */,
    281 /* *******.*******.13 (OBJ_id_mod_timestamp_protocol) */,
    282 /* *******.*******.14 (OBJ_id_mod_ocsp) */,
    283 /* *******.*******.15 (OBJ_id_mod_dvcs) */,
    284 /* *******.*******.16 (OBJ_id_mod_cmp2000) */,
    177 /* *******.*******.1 (OBJ_info_access) */,
    285 /* *******.*******.2 (OBJ_biometricInfo) */,
    286 /* *******.*******.3 (OBJ_qcStatements) */,
    287 /* *******.*******.4 (OBJ_ac_auditEntity) */,
    288 /* *******.*******.5 (OBJ_ac_targeting) */,
    289 /* *******.*******.6 (OBJ_aaControls) */,
    290 /* *******.*******.7 (OBJ_sbgp_ipAddrBlock) */,
    291 /* *******.*******.8 (OBJ_sbgp_autonomousSysNum) */,
    292 /* *******.*******.9 (OBJ_sbgp_routerIdentifier) */,
    397 /* *******.*******.10 (OBJ_ac_proxying) */,
    398 /* *******.*******.11 (OBJ_sinfo_access) */,
    663 /* *******.*******.14 (OBJ_proxyCertInfo) */,
    164 /* *******.*******.1 (OBJ_id_qt_cps) */,
    165 /* *******.*******.2 (OBJ_id_qt_unotice) */,
    293 /* *******.*******.3 (OBJ_textNotice) */,
    129 /* *******.*******.1 (OBJ_server_auth) */,
    130 /* *******.*******.2 (OBJ_client_auth) */,
    131 /* *******.*******.3 (OBJ_code_sign) */,
    132 /* *******.*******.4 (OBJ_email_protect) */,
    294 /* *******.*******.5 (OBJ_ipsecEndSystem) */,
    295 /* *******.*******.6 (OBJ_ipsecTunnel) */,
    296 /* *******.*******.7 (OBJ_ipsecUser) */,
    133 /* *******.*******.8 (OBJ_time_stamp) */,
    180 /* *******.*******.9 (OBJ_OCSP_sign) */,
    297 /* *******.*******.10 (OBJ_dvcs) */,
    298 /* *******.*******.1 (OBJ_id_it_caProtEncCert) */,
    299 /* *******.*******.2 (OBJ_id_it_signKeyPairTypes) */,
    300 /* *******.*******.3 (OBJ_id_it_encKeyPairTypes) */,
    301 /* *******.*******.4 (OBJ_id_it_preferredSymmAlg) */,
    302 /* *******.*******.5 (OBJ_id_it_caKeyUpdateInfo) */,
    303 /* *******.*******.6 (OBJ_id_it_currentCRL) */,
    304 /* *******.*******.7 (OBJ_id_it_unsupportedOIDs) */,
    305 /* *******.*******.8 (OBJ_id_it_subscriptionRequest) */,
    306 /* *******.*******.9 (OBJ_id_it_subscriptionResponse) */,
    307 /* *******.*******.10 (OBJ_id_it_keyPairParamReq) */,
    308 /* *******.*******.11 (OBJ_id_it_keyPairParamRep) */,
    309 /* *******.*******.12 (OBJ_id_it_revPassphrase) */,
    310 /* *******.*******.13 (OBJ_id_it_implicitConfirm) */,
    311 /* *******.*******.14 (OBJ_id_it_confirmWaitTime) */,
    312 /* *******.*******.15 (OBJ_id_it_origPKIMessage) */,
    784 /* *******.*******.16 (OBJ_id_it_suppLangTags) */,
    313 /* *******.*******.1 (OBJ_id_regCtrl) */,
    314 /* *******.*******.2 (OBJ_id_regInfo) */,
    323 /* *******.*******.1 (OBJ_id_alg_des40) */,
    324 /* *******.*******.2 (OBJ_id_alg_noSignature) */,
    325 /* *******.*******.3 (OBJ_id_alg_dh_sig_hmac_sha1) */,
    326 /* *******.*******.4 (OBJ_id_alg_dh_pop) */,
    327 /* *******.*******.1 (OBJ_id_cmc_statusInfo) */,
    328 /* *******.*******.2 (OBJ_id_cmc_identification) */,
    329 /* *******.*******.3 (OBJ_id_cmc_identityProof) */,
    330 /* *******.*******.4 (OBJ_id_cmc_dataReturn) */,
    331 /* *******.*******.5 (OBJ_id_cmc_transactionId) */,
    332 /* *******.*******.6 (OBJ_id_cmc_senderNonce) */,
    333 /* *******.*******.7 (OBJ_id_cmc_recipientNonce) */,
    334 /* *******.*******.8 (OBJ_id_cmc_addExtensions) */,
    335 /* *******.*******.9 (OBJ_id_cmc_encryptedPOP) */,
    336 /* *******.*******.10 (OBJ_id_cmc_decryptedPOP) */,
    337 /* *******.*******.11 (OBJ_id_cmc_lraPOPWitness) */,
    338 /* *******.*******.15 (OBJ_id_cmc_getCert) */,
    339 /* *******.*******.16 (OBJ_id_cmc_getCRL) */,
    340 /* *******.*******.17 (OBJ_id_cmc_revokeRequest) */,
    341 /* *******.*******.18 (OBJ_id_cmc_regInfo) */,
    342 /* *******.*******.19 (OBJ_id_cmc_responseInfo) */,
    343 /* *******.*******.21 (OBJ_id_cmc_queryPending) */,
    344 /* *******.*******.22 (OBJ_id_cmc_popLinkRandom) */,
    345 /* *******.*******.23 (OBJ_id_cmc_popLinkWitness) */,
    346 /* *******.*******.24 (OBJ_id_cmc_confirmCertAcceptance) */,
    347 /* *******.*******.1 (OBJ_id_on_personalData) */,
    858 /* *******.*******.3 (OBJ_id_on_permanentIdentifier) */,
    348 /* *******.*******.1 (OBJ_id_pda_dateOfBirth) */,
    349 /* *******.*******.2 (OBJ_id_pda_placeOfBirth) */,
    351 /* *******.*******.3 (OBJ_id_pda_gender) */,
    352 /* *******.*******.4 (OBJ_id_pda_countryOfCitizenship) */,
    353 /* *******.*******.5 (OBJ_id_pda_countryOfResidence) */,
    354 /* *******.*******0.1 (OBJ_id_aca_authenticationInfo) */,
    355 /* *******.*******0.2 (OBJ_id_aca_accessIdentity) */,
    356 /* *******.*******0.3 (OBJ_id_aca_chargingIdentity) */,
    357 /* *******.*******0.4 (OBJ_id_aca_group) */,
    358 /* *******.*******0.5 (OBJ_id_aca_role) */,
    399 /* *******.*******0.6 (OBJ_id_aca_encAttrs) */,
    359 /* *******.*******1.1 (OBJ_id_qcs_pkixQCSyntax_v1) */,
    360 /* *******.*******2.1 (OBJ_id_cct_crs) */,
    361 /* *******.*******2.2 (OBJ_id_cct_PKIData) */,
    362 /* *******.*******2.3 (OBJ_id_cct_PKIResponse) */,
    664 /* *******.*******1.0 (OBJ_id_ppl_anyLanguage) */,
    665 /* *******.*******1.1 (OBJ_id_ppl_inheritAll) */,
    667 /* *******.*******1.2 (OBJ_Independent) */,
    178 /* *******.*******8.1 (OBJ_ad_OCSP) */,
    179 /* *******.*******8.2 (OBJ_ad_ca_issuers) */,
    363 /* *******.*******8.3 (OBJ_ad_timeStamping) */,
    364 /* *******.*******8.4 (OBJ_ad_dvcs) */,
    785 /* *******.*******8.5 (OBJ_caRepository) */,
    780 /* *******.5.5.8.1.1 (OBJ_hmac_md5) */,
    781 /* *******.5.5.8.1.2 (OBJ_hmac_sha1) */,
    58 /* 2.16.840.1.113730.1 (OBJ_netscape_cert_extension) */,
    59 /* 2.16.840.1.113730.2 (OBJ_netscape_data_type) */,
    438 /* 0.9.2342.********.100.1 (OBJ_pilotAttributeType) */,
    439 /* 0.9.2342.********.100.3 (OBJ_pilotAttributeSyntax) */,
    440 /* 0.9.2342.********.100.4 (OBJ_pilotObjectClass) */,
    441 /* 0.9.2342.********.100.10 (OBJ_pilotGroups) */,
    108 /* 1.2.840.113533.7.66.10 (OBJ_cast5_cbc) */,
    112 /* 1.2.840.113533.7.66.12 (OBJ_pbeWithMD5AndCast5_CBC) */,
    782 /* 1.2.840.113533.7.66.13 (OBJ_id_PasswordBasedMAC) */,
    783 /* 1.2.840.113533.7.66.30 (OBJ_id_DHBasedMac) */,
    6 /* 1.2.840.113549.1.1.1 (OBJ_rsaEncryption) */,
    7 /* 1.2.840.113549.1.1.2 (OBJ_md2WithRSAEncryption) */,
    396 /* 1.2.840.113549.1.1.3 (OBJ_md4WithRSAEncryption) */,
    8 /* 1.2.840.113549.1.1.4 (OBJ_md5WithRSAEncryption) */,
    65 /* 1.2.840.113549.1.1.5 (OBJ_sha1WithRSAEncryption) */,
    644 /* 1.2.840.113549.1.1.6 (OBJ_rsaOAEPEncryptionSET) */,
    919 /* 1.2.840.113549.1.1.7 (OBJ_rsaesOaep) */,
    911 /* 1.2.840.113549.1.1.8 (OBJ_mgf1) */,
    935 /* 1.2.840.113549.1.1.9 (OBJ_pSpecified) */,
    912 /* 1.2.840.113549.1.1.10 (OBJ_rsassaPss) */,
    668 /* 1.2.840.113549.1.1.11 (OBJ_sha256WithRSAEncryption) */,
    669 /* 1.2.840.113549.1.1.12 (OBJ_sha384WithRSAEncryption) */,
    670 /* 1.2.840.113549.1.1.13 (OBJ_sha512WithRSAEncryption) */,
    671 /* 1.2.840.113549.1.1.14 (OBJ_sha224WithRSAEncryption) */,
    28 /* 1.2.840.113549.1.3.1 (OBJ_dhKeyAgreement) */,
    9 /* 1.2.840.113549.1.5.1 (OBJ_pbeWithMD2AndDES_CBC) */,
    10 /* 1.2.840.113549.1.5.3 (OBJ_pbeWithMD5AndDES_CBC) */,
    168 /* 1.2.840.113549.1.5.4 (OBJ_pbeWithMD2AndRC2_CBC) */,
    169 /* 1.2.840.113549.1.5.6 (OBJ_pbeWithMD5AndRC2_CBC) */,
    170 /* 1.2.840.113549.1.5.10 (OBJ_pbeWithSHA1AndDES_CBC) */,
    68 /* 1.2.840.113549.1.5.11 (OBJ_pbeWithSHA1AndRC2_CBC) */,
    69 /* 1.2.840.113549.1.5.12 (OBJ_id_pbkdf2) */,
    161 /* 1.2.840.113549.1.5.13 (OBJ_pbes2) */,
    162 /* 1.2.840.113549.1.5.14 (OBJ_pbmac1) */,
    21 /* 1.2.840.113549.1.7.1 (OBJ_pkcs7_data) */,
    22 /* 1.2.840.113549.1.7.2 (OBJ_pkcs7_signed) */,
    23 /* 1.2.840.113549.1.7.3 (OBJ_pkcs7_enveloped) */,
    24 /* 1.2.840.113549.1.7.4 (OBJ_pkcs7_signedAndEnveloped) */,
    25 /* 1.2.840.113549.1.7.5 (OBJ_pkcs7_digest) */,
    26 /* 1.2.840.113549.1.7.6 (OBJ_pkcs7_encrypted) */,
    48 /* 1.2.840.113549.1.9.1 (OBJ_pkcs9_emailAddress) */,
    49 /* 1.2.840.113549.1.9.2 (OBJ_pkcs9_unstructuredName) */,
    50 /* 1.2.840.113549.1.9.3 (OBJ_pkcs9_contentType) */,
    51 /* 1.2.840.113549.1.9.4 (OBJ_pkcs9_messageDigest) */,
    52 /* 1.2.840.113549.1.9.5 (OBJ_pkcs9_signingTime) */,
    53 /* 1.2.840.113549.1.9.6 (OBJ_pkcs9_countersignature) */,
    54 /* 1.2.840.113549.1.9.7 (OBJ_pkcs9_challengePassword) */,
    55 /* 1.2.840.113549.1.9.8 (OBJ_pkcs9_unstructuredAddress) */,
    56 /* 1.2.840.113549.1.9.9 (OBJ_pkcs9_extCertAttributes) */,
    172 /* 1.2.840.113549.1.9.14 (OBJ_ext_req) */,
    167 /* 1.2.840.113549.1.9.15 (OBJ_SMIMECapabilities) */,
    188 /* 1.2.840.113549.1.9.16 (OBJ_SMIME) */,
    156 /* 1.2.840.113549.1.9.20 (OBJ_friendlyName) */,
    157 /* 1.2.840.113549.1.9.21 (OBJ_localKeyID) */,
    681 /* 1.2.840.10045.******* (OBJ_X9_62_onBasis) */,
    682 /* 1.2.840.10045.******* (OBJ_X9_62_tpBasis) */,
    683 /* 1.2.840.10045.******* (OBJ_X9_62_ppBasis) */,
    417 /* *******.4.1.311.17.1 (OBJ_ms_csp_name) */,
    856 /* *******.4.1.311.17.2 (OBJ_LocalKeySet) */,
    390 /* *******.4.1.1466.344 (OBJ_dcObject) */,
    91 /* *******.4.1.3029.1.2 (OBJ_bf_cbc) */,
    315 /* *******.*******.1.1 (OBJ_id_regCtrl_regToken) */,
    316 /* *******.*******.1.2 (OBJ_id_regCtrl_authenticator) */,
    317 /* *******.*******.1.3 (OBJ_id_regCtrl_pkiPublicationInfo) */,
    318 /* *******.*******.1.4 (OBJ_id_regCtrl_pkiArchiveOptions) */,
    319 /* *******.*******.1.5 (OBJ_id_regCtrl_oldCertID) */,
    320 /* *******.*******.1.6 (OBJ_id_regCtrl_protocolEncrKey) */,
    321 /* *******.*******.2.1 (OBJ_id_regInfo_utf8Pairs) */,
    322 /* *******.*******.2.2 (OBJ_id_regInfo_certReq) */,
    365 /* *******.*******8.1.1 (OBJ_id_pkix_OCSP_basic) */,
    366 /* *******.*******8.1.2 (OBJ_id_pkix_OCSP_Nonce) */,
    367 /* *******.*******8.1.3 (OBJ_id_pkix_OCSP_CrlID) */,
    368 /* *******.*******8.1.4 (OBJ_id_pkix_OCSP_acceptableResponses) */,
    369 /* *******.*******8.1.5 (OBJ_id_pkix_OCSP_noCheck) */,
    370 /* *******.*******8.1.6 (OBJ_id_pkix_OCSP_archiveCutoff) */,
    371 /* *******.*******8.1.7 (OBJ_id_pkix_OCSP_serviceLocator) */,
    372 /* *******.*******8.1.8 (OBJ_id_pkix_OCSP_extendedStatus) */,
    373 /* *******.*******8.1.9 (OBJ_id_pkix_OCSP_valid) */,
    374 /* *******.*******8.1.10 (OBJ_id_pkix_OCSP_path) */,
    375 /* *******.*******8.1.11 (OBJ_id_pkix_OCSP_trustRoot) */,
    921 /* ********.3.2.8.1.1.1 (OBJ_brainpoolP160r1) */,
    922 /* ********.3.2.8.1.1.2 (OBJ_brainpoolP160t1) */,
    923 /* ********.3.2.8.1.1.3 (OBJ_brainpoolP192r1) */,
    924 /* ********.3.2.8.1.1.4 (OBJ_brainpoolP192t1) */,
    925 /* ********.3.2.8.1.1.5 (OBJ_brainpoolP224r1) */,
    926 /* ********.3.2.8.1.1.6 (OBJ_brainpoolP224t1) */,
    927 /* ********.3.2.8.1.1.7 (OBJ_brainpoolP256r1) */,
    928 /* ********.3.2.8.1.1.8 (OBJ_brainpoolP256t1) */,
    929 /* ********.3.2.8.1.1.9 (OBJ_brainpoolP320r1) */,
    930 /* ********.3.2.8.1.1.10 (OBJ_brainpoolP320t1) */,
    931 /* ********.3.2.8.1.1.11 (OBJ_brainpoolP384r1) */,
    932 /* ********.3.2.8.1.1.12 (OBJ_brainpoolP384t1) */,
    933 /* ********.3.2.8.1.1.13 (OBJ_brainpoolP512r1) */,
    934 /* ********.3.2.8.1.1.14 (OBJ_brainpoolP512t1) */,
    936 /* 1.3.133.16.840.63.0.2 (OBJ_dhSinglePass_stdDH_sha1kdf_scheme) */,
    941 /* 1.3.133.16.840.63.0.3 (OBJ_dhSinglePass_cofactorDH_sha1kdf_scheme) */
    ,
    418 /* 2.16.840.1.101.3.4.1.1 (OBJ_aes_128_ecb) */,
    419 /* 2.16.840.1.101.3.4.1.2 (OBJ_aes_128_cbc) */,
    420 /* 2.16.840.1.101.3.4.1.3 (OBJ_aes_128_ofb128) */,
    421 /* 2.16.840.1.101.3.4.1.4 (OBJ_aes_128_cfb128) */,
    788 /* 2.16.840.1.101.3.4.1.5 (OBJ_id_aes128_wrap) */,
    895 /* 2.16.840.1.101.3.4.1.6 (OBJ_aes_128_gcm) */,
    896 /* 2.16.840.1.101.3.4.1.7 (OBJ_aes_128_ccm) */,
    897 /* 2.16.840.1.101.3.4.1.8 (OBJ_id_aes128_wrap_pad) */,
    422 /* 2.16.840.1.101.3.4.1.21 (OBJ_aes_192_ecb) */,
    423 /* 2.16.840.1.101.3.4.1.22 (OBJ_aes_192_cbc) */,
    424 /* 2.16.840.1.101.3.4.1.23 (OBJ_aes_192_ofb128) */,
    425 /* 2.16.840.1.101.3.4.1.24 (OBJ_aes_192_cfb128) */,
    789 /* 2.16.840.1.101.3.4.1.25 (OBJ_id_aes192_wrap) */,
    898 /* 2.16.840.1.101.3.4.1.26 (OBJ_aes_192_gcm) */,
    899 /* 2.16.840.1.101.3.4.1.27 (OBJ_aes_192_ccm) */,
    900 /* 2.16.840.1.101.3.4.1.28 (OBJ_id_aes192_wrap_pad) */,
    426 /* 2.16.840.1.101.3.4.1.41 (OBJ_aes_256_ecb) */,
    427 /* 2.16.840.1.101.3.4.1.42 (OBJ_aes_256_cbc) */,
    428 /* 2.16.840.1.101.3.4.1.43 (OBJ_aes_256_ofb128) */,
    429 /* 2.16.840.1.101.3.4.1.44 (OBJ_aes_256_cfb128) */,
    790 /* 2.16.840.1.101.3.4.1.45 (OBJ_id_aes256_wrap) */,
    901 /* 2.16.840.1.101.3.4.1.46 (OBJ_aes_256_gcm) */,
    902 /* 2.16.840.1.101.3.4.1.47 (OBJ_aes_256_ccm) */,
    903 /* 2.16.840.1.101.3.4.1.48 (OBJ_id_aes256_wrap_pad) */,
    672 /* 2.16.840.1.101.3.4.2.1 (OBJ_sha256) */,
    673 /* 2.16.840.1.101.3.4.2.2 (OBJ_sha384) */,
    674 /* 2.16.840.1.101.3.4.2.3 (OBJ_sha512) */,
    675 /* 2.16.840.1.101.3.4.2.4 (OBJ_sha224) */,
    802 /* 2.16.840.1.101.3.4.3.1 (OBJ_dsa_with_SHA224) */,
    803 /* 2.16.840.1.101.3.4.3.2 (OBJ_dsa_with_SHA256) */,
    71 /* 2.16.840.1.113730.1.1 (OBJ_netscape_cert_type) */,
    72 /* 2.16.840.1.113730.1.2 (OBJ_netscape_base_url) */,
    73 /* 2.16.840.1.113730.1.3 (OBJ_netscape_revocation_url) */,
    74 /* 2.16.840.1.113730.1.4 (OBJ_netscape_ca_revocation_url) */,
    75 /* 2.16.840.1.113730.1.7 (OBJ_netscape_renewal_url) */,
    76 /* 2.16.840.1.113730.1.8 (OBJ_netscape_ca_policy_url) */,
    77 /* 2.16.840.1.113730.1.12 (OBJ_netscape_ssl_server_name) */,
    78 /* 2.16.840.1.113730.1.13 (OBJ_netscape_comment) */,
    79 /* 2.16.840.1.113730.2.5 (OBJ_netscape_cert_sequence) */,
    139 /* 2.16.840.1.113730.4.1 (OBJ_ns_sgc) */,
    458 /* 0.9.2342.********.100.1.1 (OBJ_userId) */,
    459 /* 0.9.2342.********.100.1.2 (OBJ_textEncodedORAddress) */,
    460 /* 0.9.2342.********.100.1.3 (OBJ_rfc822Mailbox) */,
    461 /* 0.9.2342.********.100.1.4 (OBJ_info) */,
    462 /* 0.9.2342.********.100.1.5 (OBJ_favouriteDrink) */,
    463 /* 0.9.2342.********.100.1.6 (OBJ_roomNumber) */,
    464 /* 0.9.2342.********.100.1.7 (OBJ_photo) */,
    465 /* 0.9.2342.********.100.1.8 (OBJ_userClass) */,
    466 /* 0.9.2342.********.100.1.9 (OBJ_host) */,
    467 /* 0.9.2342.********.100.1.10 (OBJ_manager) */,
    468 /* 0.9.2342.********.100.1.11 (OBJ_documentIdentifier) */,
    469 /* 0.9.2342.********.100.1.12 (OBJ_documentTitle) */,
    470 /* 0.9.2342.********.100.1.13 (OBJ_documentVersion) */,
    471 /* 0.9.2342.********.100.1.14 (OBJ_documentAuthor) */,
    472 /* 0.9.2342.********.100.1.15 (OBJ_documentLocation) */,
    473 /* 0.9.2342.********.100.1.20 (OBJ_homeTelephoneNumber) */,
    474 /* 0.9.2342.********.100.1.21 (OBJ_secretary) */,
    475 /* 0.9.2342.********.100.1.22 (OBJ_otherMailbox) */,
    476 /* 0.9.2342.********.100.1.23 (OBJ_lastModifiedTime) */,
    477 /* 0.9.2342.********.100.1.24 (OBJ_lastModifiedBy) */,
    391 /* 0.9.2342.********.100.1.25 (OBJ_domainComponent) */,
    478 /* 0.9.2342.********.100.1.26 (OBJ_aRecord) */,
    479 /* 0.9.2342.********.100.1.27 (OBJ_pilotAttributeType27) */,
    480 /* 0.9.2342.********.100.1.28 (OBJ_mXRecord) */,
    481 /* 0.9.2342.********.100.1.29 (OBJ_nSRecord) */,
    482 /* 0.9.2342.********.100.1.30 (OBJ_sOARecord) */,
    483 /* 0.9.2342.********.100.1.31 (OBJ_cNAMERecord) */,
    484 /* 0.9.2342.********.100.1.37 (OBJ_associatedDomain) */,
    485 /* 0.9.2342.********.100.1.38 (OBJ_associatedName) */,
    486 /* 0.9.2342.********.100.1.39 (OBJ_homePostalAddress) */,
    487 /* 0.9.2342.********.100.1.40 (OBJ_personalTitle) */,
    488 /* 0.9.2342.********.100.1.41 (OBJ_mobileTelephoneNumber) */,
    489 /* 0.9.2342.********.100.1.42 (OBJ_pagerTelephoneNumber) */,
    490 /* 0.9.2342.********.100.1.43 (OBJ_friendlyCountryName) */,
    491 /* 0.9.2342.********.100.1.45 (OBJ_organizationalStatus) */,
    492 /* 0.9.2342.********.100.1.46 (OBJ_janetMailbox) */,
    493 /* 0.9.2342.********.100.1.47 (OBJ_mailPreferenceOption) */,
    494 /* 0.9.2342.********.100.1.48 (OBJ_buildingName) */,
    495 /* 0.9.2342.********.100.1.49 (OBJ_dSAQuality) */,
    496 /* 0.9.2342.********.100.1.50 (OBJ_singleLevelQuality) */,
    497 /* 0.9.2342.********.100.1.51 (OBJ_subtreeMinimumQuality) */,
    498 /* 0.9.2342.********.100.1.52 (OBJ_subtreeMaximumQuality) */,
    499 /* 0.9.2342.********.100.1.53 (OBJ_personalSignature) */,
    500 /* 0.9.2342.********.100.1.54 (OBJ_dITRedirect) */,
    501 /* 0.9.2342.********.100.1.55 (OBJ_audio) */,
    502 /* 0.9.2342.********.100.1.56 (OBJ_documentPublisher) */,
    442 /* 0.9.2342.********.100.3.4 (OBJ_iA5StringSyntax) */,
    443 /* 0.9.2342.********.100.3.5 (OBJ_caseIgnoreIA5StringSyntax) */,
    444 /* 0.9.2342.********.100.4.3 (OBJ_pilotObject) */,
    445 /* 0.9.2342.********.100.4.4 (OBJ_pilotPerson) */,
    446 /* 0.9.2342.********.100.4.5 (OBJ_account) */,
    447 /* 0.9.2342.********.100.4.6 (OBJ_document) */,
    448 /* 0.9.2342.********.100.4.7 (OBJ_room) */,
    449 /* 0.9.2342.********.100.4.9 (OBJ_documentSeries) */,
    392 /* 0.9.2342.********.100.4.13 (OBJ_Domain) */,
    450 /* 0.9.2342.********.100.4.14 (OBJ_rFC822localPart) */,
    451 /* 0.9.2342.********.100.4.15 (OBJ_dNSDomain) */,
    452 /* 0.9.2342.********.100.4.17 (OBJ_domainRelatedObject) */,
    453 /* 0.9.2342.********.100.4.18 (OBJ_friendlyCountry) */,
    454 /* 0.9.2342.********.100.4.19 (OBJ_simpleSecurityObject) */,
    455 /* 0.9.2342.********.100.4.20 (OBJ_pilotOrganization) */,
    456 /* 0.9.2342.********.100.4.21 (OBJ_pilotDSA) */,
    457 /* 0.9.2342.********.100.4.22 (OBJ_qualityLabelledData) */,
    189 /* 1.2.840.113549.******** (OBJ_id_smime_mod) */,
    190 /* 1.2.840.113549.******** (OBJ_id_smime_ct) */,
    191 /* 1.2.840.113549.******** (OBJ_id_smime_aa) */,
    192 /* 1.2.840.113549.******** (OBJ_id_smime_alg) */,
    193 /* 1.2.840.113549.******** (OBJ_id_smime_cd) */,
    194 /* 1.2.840.113549.******** (OBJ_id_smime_spq) */,
    195 /* 1.2.840.113549.******** (OBJ_id_smime_cti) */,
    158 /* 1.2.840.113549.******** (OBJ_x509Certificate) */,
    159 /* 1.2.840.113549.******** (OBJ_sdsiCertificate) */,
    160 /* 1.2.840.113549.******** (OBJ_x509Crl) */,
    144 /* 1.2.840.113549.******** (OBJ_pbe_WithSHA1And128BitRC4) */,
    145 /* 1.2.840.113549.******** (OBJ_pbe_WithSHA1And40BitRC4) */,
    146 /* 1.2.840.113549.******** (OBJ_pbe_WithSHA1And3_Key_TripleDES_CBC) */,
    147 /* 1.2.840.113549.******** (OBJ_pbe_WithSHA1And2_Key_TripleDES_CBC) */,
    148 /* 1.2.840.113549.******** (OBJ_pbe_WithSHA1And128BitRC2_CBC) */,
    149 /* 1.2.840.113549.******** (OBJ_pbe_WithSHA1And40BitRC2_CBC) */,
    171 /* *******.4.1.311.2.1.14 (OBJ_ms_ext_req) */,
    134 /* *******.4.1.311.2.1.21 (OBJ_ms_code_ind) */,
    135 /* *******.4.1.311.2.1.22 (OBJ_ms_code_com) */,
    136 /* *******.4.1.311.10.3.1 (OBJ_ms_ctl_sign) */,
    137 /* *******.4.1.311.10.3.3 (OBJ_ms_sgc) */,
    138 /* *******.4.1.311.10.3.4 (OBJ_ms_efs) */,
    648 /* *******.4.1.311.20.2.2 (OBJ_ms_smartcard_login) */,
    649 /* *******.4.1.311.20.2.3 (OBJ_ms_upn) */,
    751 /* 1.2.392.200011.********.2 (OBJ_camellia_128_cbc) */,
    752 /* 1.2.392.200011.********.3 (OBJ_camellia_192_cbc) */,
    753 /* 1.2.392.200011.********.4 (OBJ_camellia_256_cbc) */,
    907 /* 1.2.392.200011.********.2 (OBJ_id_camellia128_wrap) */,
    908 /* 1.2.392.200011.********.3 (OBJ_id_camellia192_wrap) */,
    909 /* 1.2.392.200011.********.4 (OBJ_id_camellia256_wrap) */,
    196 /* 1.2.840.113549.********.1 (OBJ_id_smime_mod_cms) */,
    197 /* 1.2.840.113549.********.2 (OBJ_id_smime_mod_ess) */,
    198 /* 1.2.840.113549.********.3 (OBJ_id_smime_mod_oid) */,
    199 /* 1.2.840.113549.********.4 (OBJ_id_smime_mod_msg_v3) */,
    200 /* 1.2.840.113549.********.5 (OBJ_id_smime_mod_ets_eSignature_88) */,
    201 /* 1.2.840.113549.********.6 (OBJ_id_smime_mod_ets_eSignature_97) */,
    202 /* 1.2.840.113549.********.7 (OBJ_id_smime_mod_ets_eSigPolicy_88) */,
    203 /* 1.2.840.113549.********.8 (OBJ_id_smime_mod_ets_eSigPolicy_97) */,
    204 /* 1.2.840.113549.********.1 (OBJ_id_smime_ct_receipt) */,
    205 /* 1.2.840.113549.********.2 (OBJ_id_smime_ct_authData) */,
    206 /* 1.2.840.113549.********.3 (OBJ_id_smime_ct_publishCert) */,
    207 /* 1.2.840.113549.********.4 (OBJ_id_smime_ct_TSTInfo) */,
    208 /* 1.2.840.113549.********.5 (OBJ_id_smime_ct_TDTInfo) */,
    209 /* 1.2.840.113549.********.6 (OBJ_id_smime_ct_contentInfo) */,
    210 /* 1.2.840.113549.********.7 (OBJ_id_smime_ct_DVCSRequestData) */,
    211 /* 1.2.840.113549.********.8 (OBJ_id_smime_ct_DVCSResponseData) */,
    786 /* 1.2.840.113549.********.9 (OBJ_id_smime_ct_compressedData) */,
    787 /* 1.2.840.113549.********.27 (OBJ_id_ct_asciiTextWithCRLF) */,
    212 /* 1.2.840.113549.********.1 (OBJ_id_smime_aa_receiptRequest) */,
    213 /* 1.2.840.113549.********.2 (OBJ_id_smime_aa_securityLabel) */,
    214 /* 1.2.840.113549.********.3 (OBJ_id_smime_aa_mlExpandHistory) */,
    215 /* 1.2.840.113549.********.4 (OBJ_id_smime_aa_contentHint) */,
    216 /* 1.2.840.113549.********.5 (OBJ_id_smime_aa_msgSigDigest) */,
    217 /* 1.2.840.113549.********.6 (OBJ_id_smime_aa_encapContentType) */,
    218 /* 1.2.840.113549.********.7 (OBJ_id_smime_aa_contentIdentifier) */,
    219 /* 1.2.840.113549.********.8 (OBJ_id_smime_aa_macValue) */,
    220 /* 1.2.840.113549.********.9 (OBJ_id_smime_aa_equivalentLabels) */,
    221 /* 1.2.840.113549.********.10 (OBJ_id_smime_aa_contentReference) */,
    222 /* 1.2.840.113549.********.11 (OBJ_id_smime_aa_encrypKeyPref) */,
    223 /* 1.2.840.113549.********.12 (OBJ_id_smime_aa_signingCertificate) */,
    224 /* 1.2.840.113549.********.13 (OBJ_id_smime_aa_smimeEncryptCerts) */,
    225 /* 1.2.840.113549.********.14 (OBJ_id_smime_aa_timeStampToken) */,
    226 /* 1.2.840.113549.********.15 (OBJ_id_smime_aa_ets_sigPolicyId) */,
    227 /* 1.2.840.113549.********.16 (OBJ_id_smime_aa_ets_commitmentType) */,
    228 /* 1.2.840.113549.********.17 (OBJ_id_smime_aa_ets_signerLocation) */,
    229 /* 1.2.840.113549.********.18 (OBJ_id_smime_aa_ets_signerAttr) */,
    230 /* 1.2.840.113549.********.19 (OBJ_id_smime_aa_ets_otherSigCert) */,
    231 /* 1.2.840.113549.********.20 (OBJ_id_smime_aa_ets_contentTimestamp) */,
    232 /* 1.2.840.113549.********.21 (OBJ_id_smime_aa_ets_CertificateRefs) */,
    233 /* 1.2.840.113549.********.22 (OBJ_id_smime_aa_ets_RevocationRefs) */,
    234 /* 1.2.840.113549.********.23 (OBJ_id_smime_aa_ets_certValues) */,
    235 /* 1.2.840.113549.********.24 (OBJ_id_smime_aa_ets_revocationValues) */,
    236 /* 1.2.840.113549.********.25 (OBJ_id_smime_aa_ets_escTimeStamp) */,
    237 /* 1.2.840.113549.********.26 (OBJ_id_smime_aa_ets_certCRLTimestamp) */,
    238 /* 1.2.840.113549.********.27 (OBJ_id_smime_aa_ets_archiveTimeStamp) */,
    239 /* 1.2.840.113549.********.28 (OBJ_id_smime_aa_signatureType) */,
    240 /* 1.2.840.113549.********.29 (OBJ_id_smime_aa_dvcs_dvc) */,
    241 /* 1.2.840.113549.********.1 (OBJ_id_smime_alg_ESDHwith3DES) */,
    242 /* 1.2.840.113549.********.2 (OBJ_id_smime_alg_ESDHwithRC2) */,
    243 /* 1.2.840.113549.********.3 (OBJ_id_smime_alg_3DESwrap) */,
    244 /* 1.2.840.113549.********.4 (OBJ_id_smime_alg_RC2wrap) */,
    245 /* 1.2.840.113549.********.5 (OBJ_id_smime_alg_ESDH) */,
    246 /* 1.2.840.113549.********.6 (OBJ_id_smime_alg_CMS3DESwrap) */,
    247 /* 1.2.840.113549.********.7 (OBJ_id_smime_alg_CMSRC2wrap) */,
    125 /* 1.2.840.113549.********.8 (OBJ_zlib_compression) */,
    893 /* 1.2.840.113549.********.9 (OBJ_id_alg_PWRI_KEK) */,
    248 /* 1.2.840.113549.********.1 (OBJ_id_smime_cd_ldap) */,
    249 /* 1.2.840.113549.********.1 (OBJ_id_smime_spq_ets_sqt_uri) */,
    250 /* 1.2.840.113549.********.2 (OBJ_id_smime_spq_ets_sqt_unotice) */,
    251 /* 1.2.840.113549.********.1 (OBJ_id_smime_cti_ets_proofOfOrigin) */,
    252 /* 1.2.840.113549.********.2 (OBJ_id_smime_cti_ets_proofOfReceipt) */,
    253 /* 1.2.840.113549.********.3 (OBJ_id_smime_cti_ets_proofOfDelivery) */,
    254 /* 1.2.840.113549.********.4 (OBJ_id_smime_cti_ets_proofOfSender) */,
    255 /* 1.2.840.113549.********.5 (OBJ_id_smime_cti_ets_proofOfApproval) */,
    256 /* 1.2.840.113549.********.6 (OBJ_id_smime_cti_ets_proofOfCreation) */,
    150 /* 1.2.840.113549.*********.1 (OBJ_keyBag) */,
    151 /* 1.2.840.113549.*********.2 (OBJ_pkcs8ShroudedKeyBag) */,
    152 /* 1.2.840.113549.*********.3 (OBJ_certBag) */,
    153 /* 1.2.840.113549.*********.4 (OBJ_crlBag) */,
    154 /* 1.2.840.113549.*********.5 (OBJ_secretBag) */,
    155 /* 1.2.840.113549.*********.6 (OBJ_safeContentsBag) */,
    34 /* *******.*********.1.1.2 (OBJ_idea_cbc) */,
};
